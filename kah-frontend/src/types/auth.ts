// src/types/auth.ts

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: Record<string, unknown>;
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
  date_joined: string;
  role?: Role;
  role_id?: number;
}

export interface LoginRequest {
  username?: string;
  email?: string;
  password: string;
  role_id: number;
}

export interface LoginResponse {
  user: User;
  token: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  user?: User;
  token?: string;
} 