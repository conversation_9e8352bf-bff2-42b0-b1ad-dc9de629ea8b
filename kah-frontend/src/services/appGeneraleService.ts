import api from './axios';
import type { MessagingUser } from '../types/messaging';

export class AppGeneraleService {
  /**
   * Get users for messaging purposes
   */
  static async getMessagingUsers(): Promise<MessagingUser[]> {
    try {
      console.log('AppGeneraleService: Making request to /auth/messaging-users/');
      const response = await api.get<MessagingUser[]>('/auth/messaging-users/');
      console.log('AppGeneraleService: Response received:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('AppGeneraleService: Error fetching messaging users:', error);
      if (error.response) {
        console.error('AppGeneraleService: Response status:', error.response.status);
        console.error('AppGeneraleService: Response data:', error.response.data);
      } else if (error.request) {
        console.error('AppGeneraleService: Network error:', error.request);
      } else {
        console.error('AppGeneraleService: Other error:', error.message);
      }
      throw error;
    }
  }
}
