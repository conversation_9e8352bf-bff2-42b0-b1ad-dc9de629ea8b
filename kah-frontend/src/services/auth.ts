// src/services/auth.ts
import api from './axios';
import type { User, LoginResponse, AuthResponse, Role } from '../types/auth';

export class AuthService {
  /**
   * Get available roles for login
   */
  static async getRoles(): Promise<Role[]> {
    try {
      const response = await api.get<Role[]>('/auth/roles/');
      return response.data;
    } catch (error: unknown) {
      console.error('Failed to fetch roles:', error);
      return [];
    }
  }

  /**
   * Login user with username, password and role
   */
  static async login(username: string, password: string, roleId: number): Promise<AuthResponse> {
    try {
      const response = await api.post<LoginResponse>('/auth/login/', {
        username,
        password,
        role_id: roleId,
      });

      const { user, token } = response.data;
      
      // Store token in localStorage
      localStorage.setItem('token', token);
      
      return {
        success: true,
        user,
        token,
      };
    } catch (error: unknown) {
      console.error('Login failed:', error);
      const errorResponse = error as { response?: { data?: { error?: string } } };
      return {
        success: false,
        message: errorResponse.response?.data?.error || 'Login failed',
      };
    }
  }

  /**
   * Logout user
   */
  static async logout(): Promise<AuthResponse> {
    try {
      await api.post('/auth/logout/');
      
      // Remove token from localStorage
      localStorage.removeItem('token');
      
      return {
        success: true,
        message: 'Logged out successfully',
      };
    } catch (error: unknown) {
      console.error('Logout failed:', error);
      // Still remove token even if logout request fails
      localStorage.removeItem('token');
      
      const errorResponse = error as { response?: { data?: { error?: string } } };
      return {
        success: false,
        message: errorResponse.response?.data?.error || 'Logout failed',
      };
    }
  }

  /**
   * Get current user information
   */
  static async getCurrentUser(): Promise<AuthResponse> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        return {
          success: false,
          message: 'No token found',
        };
      }

      const response = await api.get<User>('/auth/user/');
      
      return {
        success: true,
        user: response.data,
      };
    } catch (error: unknown) {
      console.error('Get current user failed:', error);
      // Remove invalid token
      localStorage.removeItem('token');
      
      const errorResponse = error as { response?: { data?: { error?: string } } };
      return {
        success: false,
        message: errorResponse.response?.data?.error || 'Failed to get user information',
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    return !!token;
  }

  /**
   * Get stored token
   */
  static getToken(): string | null {
    return localStorage.getItem('token');
  }

  /**
   * Clear stored token
   */
  static clearToken(): void {
    localStorage.removeItem('token');
  }
}
