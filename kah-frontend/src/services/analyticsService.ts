// src/services/analyticsService.ts
import api from './axios';

export interface DataSource {
  id: string;
  name: string;
  description: string;
  records: number;
  last_updated: string | null;
  status: 'available' | 'no_data' | 'error';
  endpoint: string;
}

export interface DataSourcesResponse {
  status: string;
  total_sources: number;
  sources: DataSource[];
  last_check: string;
}

export interface CollectDataResponse {
  status: string;
  message: string;
  task_id: string;
  estimated_duration: string;
}

export interface DataFramePreview {
  name: string;
  shape: [number, number];
  columns: string[];
  sample_data: Record<string, any>[];
  data_types: Record<string, string>;
  last_updated: string;
  error?: string;
}

export interface DataFramesResponse {
  status: string;
  dataframes: Record<string, DataFramePreview>;
  total_dataframes: number;
  generated_at: string;
}

class AnalyticsService {
  /**
   * Obtenir le statut de toutes les sources de données internes
   */
  async getDataSourcesStatus(): Promise<DataSourcesResponse> {
    try {
      const response = await api.get('/analytics/data_sources_status/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du statut des sources:', error);
      throw error;
    }
  }

  /**
   * Déclencher la collecte manuelle de toutes les données
   */
  async collectAllData(): Promise<CollectDataResponse> {
    try {
      const response = await api.post('/analytics/collect_data/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors du déclenchement de la collecte globale:', error);
      throw error;
    }
  }

  /**
   * Déclencher la collecte pour une source spécifique
   */
  async collectSingleSource(sourceId: string): Promise<any> {
    try {
      const response = await api.post('/analytics/collect_single_source/', {
        source_id: sourceId
      });
      return response.data;
    } catch (error) {
      console.error(`Erreur lors de la collecte de ${sourceId}:`, error);
      throw error;
    }
  }

  /**
   * Obtenir un aperçu des dataframes pour toutes les applications
   */
  async getDataFramesPreview(): Promise<DataFramesResponse> {
    try {
      const response = await api.get('/analytics/get_dataframes_preview/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des previews:', error);
      throw error;
    }
  }

  /**
   * Lancer l'analyse complète
   */
  async runAnalysis(): Promise<any> {
    try {
      const response = await api.post('/analytics/run_analysis/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors du lancement de l\'analyse:', error);
      throw error;
    }
  }

  /**
   * Obtenir le résumé du dashboard
   */
  async getDashboardSummary(): Promise<any> {
    try {
      const response = await api.get('/analytics/dashboard_summary/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du résumé:', error);
      throw error;
    }
  }
}

export default new AnalyticsService();
