/* Kaydan Analytics Hub (KAH) App Styles */

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

.App {
  width: 100%;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reset default margins and padding */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: inherit;
}

/* Ensure full height for the app */
html, body, #root {
  height: 100%;
}

/* Beautiful dark mode support - much nicer and more readable */
.dark {
  color-scheme: dark;
  --bg-primary: #0f172a;      /* Deep dark blue background */
  --bg-secondary: #1e293b;     /* Slightly lighter dark background */
  --bg-tertiary: #334155;      /* Card/surface background */
  --text-primary: #f8fafc;     /* Very light text - highly readable */
  --text-secondary: #e2e8f0;   /* Light text for secondary content */
  --text-muted: #94a3b8;       /* Muted text - still very readable */
  --border-primary: #475569;    /* Subtle borders */
  --border-secondary: #64748b;  /* Lighter borders for contrast */
  --accent-primary: #f97316;    /* Warm orange accent */
  --accent-secondary: #fb923c;  /* Lighter orange for hover states */
}

.light {
  color-scheme: light;
  --bg-primary: #ffffff;        /* Pure white background */
  --bg-secondary: #f8fafc;      /* Very light gray background */
  --bg-tertiary: #f1f5f9;       /* Light gray for cards */
  --text-primary: #0f172a;      /* Dark text for readability */
  --text-secondary: #334155;     /* Medium dark text */
  --text-muted: #64748b;        /* Muted gray text */
  --border-primary: #e2e8f0;    /* Light borders */
  --border-secondary: #cbd5e1;  /* Slightly darker borders */
  --accent-primary: #f97316;    /* Warm orange accent */
  --accent-secondary: #fb923c;  /* Lighter orange for hover states */
}

/* High contrast mode - maximum accessibility */
.high-contrast {
  color-scheme: dark;
  --bg-primary: #000000;        /* Pure black background */
  --bg-secondary: #1a1a1a;      /* Very dark gray */
  --bg-tertiary: #2d2d2d;       /* Dark gray for surfaces */
  --text-primary: #ffffff;       /* Pure white text */
  --text-secondary: #ffffff;     /* White text for all content */
  --text-muted: #cccccc;        /* Light gray for muted text */
  --border-primary: #ffffff;     /* White borders */
  --border-secondary: #ffffff;   /* White borders */
  --accent-primary: #ff6b35;    /* Bright orange for visibility */
  --accent-secondary: #ff8c42;  /* Bright orange for hover */
  --border-contrast: 2px;        /* Thicker borders */
  --focus-ring-width: 3px;      /* Thicker focus rings */
  --shadow-intensity: 0.5;      /* Enhanced shadows */
}

/* Smooth transitions for theme changes */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Focus ring styles */
.focus-ring:focus {
  outline: 2px solid var(--focus-ring-width, 2px) rgb(59 130 246);
  outline-offset: 2px;
}

/* Custom scrollbar for dark mode - much nicer */
.dark ::-webkit-scrollbar {
  width: 10px;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 5px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 5px;
  border: 2px solid #1e293b;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* High contrast scrollbar */
.high-contrast ::-webkit-scrollbar {
  width: 14px;
}

.high-contrast ::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 7px;
}

.high-contrast ::-webkit-scrollbar-thumb {
  background: #ffffff;
  border-radius: 7px;
  border: 2px solid #1a1a1a;
}

.high-contrast ::-webkit-scrollbar-thumb:hover {
  background: #cccccc;
}

/* Theme-aware background colors */
.bg-theme-primary {
  background-color: var(--bg-primary);
}

.bg-theme-secondary {
  background-color: var(--bg-secondary);
}

.bg-theme-tertiary {
  background-color: var(--bg-tertiary);
}

/* Theme-aware text colors */
.text-theme-primary {
  color: var(--text-primary);
}

.text-theme-secondary {
  color: var(--text-secondary);
}

.text-theme-muted {
  color: var(--text-muted);
}

/* Theme-aware border colors */
.border-theme-primary {
  border-color: var(--border-primary);
}

.border-theme-secondary {
  border-color: var(--border-secondary);
}

/* Theme-aware accent colors */
.text-accent-primary {
  color: var(--accent-primary);
}

.text-accent-secondary {
  color: var(--accent-secondary);
}

.bg-accent-primary {
  background-color: var(--accent-primary);
}

.bg-accent-secondary {
  background-color: var(--accent-secondary);
}

/* Enhanced dark mode specific styles */
.dark {
  /* Better contrast for dark mode */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Improved readability for dark mode text */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  color: #f8fafc;
  font-weight: 600;
}

.dark p {
  color: #e2e8f0;
  line-height: 1.6;
}

.dark .text-gray-600 {
  color: #94a3b8 !important;
}

.dark .text-gray-500 {
  color: #94a3b8 !important;
}

.dark .text-gray-400 {
  color: #64748b !important;
}

/* Better contrast for dark mode cards */
.dark .bg-white {
  background-color: #1e293b !important;
}

.dark .bg-gray-50 {
  background-color: #334155 !important;
}

.dark .bg-gray-100 {
  background-color: #334155 !important;
}

/* Improved dark mode borders */
.dark .border-gray-200 {
  border-color: #475569 !important;
}

.dark .border-gray-300 {
  border-color: #475569 !important;
}

/* Better dark mode form elements */
.dark input, .dark select, .dark textarea {
  background-color: #1e293b !important;
  border-color: #475569 !important;
  color: #f8fafc !important;
}

.dark input:focus, .dark select:focus, .dark textarea:focus {
  border-color: #f97316 !important;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
}

/* Dark mode button improvements */
.dark .bg-primary-600 {
  background-color: #f97316 !important;
}

.dark .bg-primary-500 {
  background-color: #fb923c !important;
}

.dark .text-primary-600 {
  color: #f97316 !important;
}

.dark .text-primary-500 {
  color: #fb923c !important;
}
