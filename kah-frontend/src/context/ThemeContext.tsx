import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Theme = 'light' | 'dark' | 'auto' | 'high-contrast';

interface ThemeContextType {
  currentTheme: Theme;
  effectiveTheme: 'light' | 'dark' | 'high-contrast';
  systemTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isDark: boolean;
  isHighContrast: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<Theme>('light');
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light');

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light');
    };

    // Set initial system theme
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

    // Listen for changes
    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);
  }, []);

  // Load saved theme preference on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('preferred-theme') as Theme;
    if (savedTheme && ['light', 'dark', 'auto', 'high-contrast'].includes(savedTheme)) {
      setCurrentTheme(savedTheme);
    } else {
      // Check if user prefers dark mode by default
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const defaultTheme: Theme = prefersDark ? 'dark' : 'light';
      setCurrentTheme(defaultTheme);
    }
  }, []);

  const getEffectiveTheme = (): 'light' | 'dark' | 'high-contrast' => {
    if (currentTheme === 'auto') {
      return systemTheme;
    }
    return currentTheme as 'light' | 'dark' | 'high-contrast';
  };

  const setTheme = (theme: Theme) => {
    setCurrentTheme(theme);
    localStorage.setItem('preferred-theme', theme);
  };

  const toggleTheme = () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  const effectiveTheme = getEffectiveTheme();
  const isDark = effectiveTheme === 'dark' || effectiveTheme === 'high-contrast';
  const isHighContrast = effectiveTheme === 'high-contrast';

  // Apply theme to document whenever effectiveTheme changes
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;

    // Remove all theme classes
    root.classList.remove('light', 'dark', 'high-contrast');
    body.classList.remove('light', 'dark', 'high-contrast');

    // Apply theme classes
    root.classList.add(effectiveTheme);
    body.classList.add(effectiveTheme);

    // Special handling for high contrast theme
    if (effectiveTheme === 'high-contrast') {
      root.classList.add('dark'); // High contrast mode uses dark base
      body.style.setProperty('--high-contrast', 'true');
    } else {
      body.style.removeProperty('--high-contrast');
    }

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      const color = effectiveTheme === 'dark' || effectiveTheme === 'high-contrast' 
        ? '#0f172a' // dark-900
        : '#ffffff' // white
      metaThemeColor.setAttribute('content', color);
    }

    // Update CSS custom properties for theme-aware components
    updateThemeCustomProperties(effectiveTheme);

    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('themeChanged', { 
      detail: { 
        theme: currentTheme, 
        effectiveTheme,
        isHighContrast: effectiveTheme === 'high-contrast'
      }
    }));
  }, [effectiveTheme, currentTheme]);

  const updateThemeCustomProperties = (theme: 'light' | 'dark' | 'high-contrast') => {
    const root = document.documentElement;

    if (theme === 'high-contrast') {
      // High contrast theme overrides
      root.style.setProperty('--border-contrast', '2px');
      root.style.setProperty('--focus-ring-width', '3px');
      root.style.setProperty('--shadow-intensity', '0.5');
    } else {
      // Reset to default values
      root.style.setProperty('--border-contrast', '1px');
      root.style.setProperty('--focus-ring-width', '2px');
      root.style.setProperty('--shadow-intensity', '0.1');
    }
  };

  const value: ThemeContextType = {
    currentTheme,
    effectiveTheme,
    systemTheme,
    setTheme,
    toggleTheme,
    isDark,
    isHighContrast,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext; 