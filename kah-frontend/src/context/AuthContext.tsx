import React, { createContext, useState, useContext, useEffect } from "react";
import type { ReactNode } from "react";
import { AuthService } from "../services/auth";
import type { User, Role } from "../types/auth";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  roles: Role[];
  login: (username: string, password: string, roleId: number) => Promise<boolean>;
  logout: () => void;
  checkAuth: () => Promise<void>;
  fetchRoles: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [roles, setRoles] = useState<Role[]>([]);

  const fetchRoles = async () => {
    try {
      const rolesData = await AuthService.getRoles();
      setRoles(rolesData);
    } catch (error) {
      console.error("Failed to fetch roles:", error);
    }
  };

  const checkAuth = async () => {
    try {
      const response = await AuthService.getCurrentUser();
      if (response.success && response.user) {
        setUser(response.user);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string, roleId: number): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await AuthService.login(username, password, roleId);
      
      if (response.success && response.user) {
        setUser(response.user);
        setIsAuthenticated(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Login failed:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  useEffect(() => {
    checkAuth();
    fetchRoles();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    roles,
    login,
    logout,
    checkAuth,
    fetchRoles,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

    
