import React, { createContext, useState, useContext, useEffect } from "react";
import type { ReactNode } from "react";
import { AuthService } from "../services/auth";
import type { User, Role } from "../types/auth";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  roles: Role[];
  login: (username: string, password: string, roleId: number) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  fetchRoles: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [roles, setRoles] = useState<Role[]>([]);

  const fetchRoles = async () => {
    try {
      console.log('AuthContext: Fetching roles...');
      const rolesData = await AuthService.getRoles();
      console.log('AuthContext: Roles fetched:', rolesData);
      setRoles(rolesData);
    } catch (error) {
      console.error("Failed to fetch roles:", error);
      // Don't set roles to empty array, just log the error
      // Roles are only needed for login form, so this is not critical
    }
  };

  const checkAuth = async () => {
    try {
      const response = await AuthService.getCurrentUser();
      if (response.success && response.user) {
        setUser(response.user);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string, roleId: number): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await AuthService.login(username, password, roleId);
      
      if (response.success && response.user) {
        setUser(response.user);
        setIsAuthenticated(true);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Login failed:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    console.log('AuthContext: Starting logout process');
    try {
      console.log('AuthContext: Calling AuthService.logout()');
      await AuthService.logout();
      console.log('AuthContext: AuthService.logout() completed');
    } catch (error) {
      console.error('AuthContext: Logout error:', error);
    } finally {
      console.log('AuthContext: Clearing user state');
      setUser(null);
      setIsAuthenticated(false);
      console.log('AuthContext: Logout process completed');
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      await checkAuth();
      // Always fetch roles for the login form
      await fetchRoles();
    };
    
    initializeAuth();
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    roles,
    login,
    logout,
    checkAuth,
    fetchRoles,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

    
