import React, { useState, useEffect, useRef } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { 
  Home,
  Camera,
  BarChart3,
  Eye,
  TrendingUp,
  MapPin,
  FileText,
  History,
  Database,
  Archive,
  Download,
  Settings,
  Cog,
  Plug,
  User,
  LogOut,
  Search,
  X,
  ChevronRight,
  ChevronDown,
  Bell,
  Wifi,
  WifiOff,
  HelpCircle,
  Zap
} from 'lucide-react'

// Import layout components
import ThemeToggle from './ThemeToggle'

interface NavigationItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  path?: string
  badge?: number | string
  children?: NavigationItem[]
  isNew?: boolean
  disabled?: boolean
}

interface MobileMenuProps {
  isOpen: boolean
  onClose: () => void
  className?: string
}

const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose, className = '' }) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const location = useLocation()
  const navigate = useNavigate()
  const menuRef = useRef<HTMLDivElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Navigation items (same as Sidebar but optimized for mobile)
  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      path: '/'
    },
    {
      id: 'live-detection',
      label: 'Live Detection',
      icon: Camera,
      path: '/live',
      badge: 3
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      children: [
        {
          id: 'analytics-overview',
          label: 'Overview',
          icon: Eye,
          path: '/analytics/overview'
        },
        {
          id: 'analytics-trends',
          label: 'Traffic Trends',
          icon: TrendingUp,
          path: '/analytics/trends'
        },
        {
          id: 'analytics-zones',
          label: 'Zone Performance',
          icon: MapPin,
          path: '/analytics/zones'
        },
        {
          id: 'analytics-reports',
          label: 'Reports',
          icon: FileText,
          path: '/analytics/reports',
          isNew: true
        }
      ]
    },
    {
      id: 'historical',
      label: 'Historical Data',
      icon: History,
      children: [
        {
          id: 'historical-sessions',
          label: 'Sessions',
          icon: Database,
          path: '/historical/sessions'
        },
        {
          id: 'historical-data',
          label: 'Data Explorer',
          icon: Archive,
          path: '/historical/data'
        },
        {
          id: 'historical-export',
          label: 'Export Center',
          icon: Download,
          path: '/historical/export'
        }
      ]
    },
    {
      id: 'configuration',
      label: 'Configuration',
      icon: Settings,
      children: [
        {
          id: 'config-detection',
          label: 'Detection Models',
          icon: Cog,
          path: '/configuration/detection'
        },
        {
          id: 'config-camera',
          label: 'Camera Settings',
          icon: Camera,
          path: '/configuration/camera'
        },
        {
          id: 'config-alerts',
          label: 'Alerts & Notifications',
          icon: Bell,
          path: '/configuration/alerts'
        }
      ]
    },
    {
      id: 'integrations',
      label: 'Integrations',
      icon: Plug,
      path: '/integrations',
      badge: 'Beta'
    },
    {
      id: 'account',
      label: 'Account',
      icon: User,
      path: '/account'
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: HelpCircle,
      path: '/help'
    }
  ]

  // Close menu when route changes
  useEffect(() => {
    onClose()
  }, [location.pathname, onClose])

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Close menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Focus search input when menu opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus()
      }, 300) // Wait for animation to complete
    }
  }, [isOpen])

  // Auto-expand current section
  useEffect(() => {
    const path = location.pathname
    if (path.startsWith('/analytics') && !expandedItems.includes('analytics')) {
      setExpandedItems(prev => [...prev, 'analytics'])
    }
    if (path.startsWith('/historical') && !expandedItems.includes('historical')) {
      setExpandedItems(prev => [...prev, 'historical'])
    }
    if (path.startsWith('/configuration') && !expandedItems.includes('configuration')) {
      setExpandedItems(prev => [...prev, 'configuration'])
    }
  }, [location.pathname, expandedItems])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const isChildActive = (children: NavigationItem[] = []) => {
    return children.some(child => child.path && isActive(child.path))
  }

  const handleItemClick = (item: NavigationItem) => {
    if (item.path) {
      navigate(item.path)
    }
    onClose()
  }

  const handleLogout = () => {
    // In a real app, this would handle logout logic
    console.log('Logging out...')
    onClose()
  }

  const filteredItems = navigationItems.filter(item =>
    searchQuery === '' || 
    item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.children?.some(child => 
      child.label.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)
    const isItemActive = item.path ? isActive(item.path) : isChildActive(item.children)

    if (hasChildren) {
      return (
        <div key={item.id} className="space-y-1">
          {/* Parent item */}
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full flex items-center justify-between px-4 py-3 text-base font-medium rounded-lg transition-all duration-200 ${
              isItemActive
                ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700'
            }`}
          >
            <div className="flex items-center space-x-3">
              <item.icon className={`w-6 h-6 ${
                isItemActive 
                  ? 'text-primary-600 dark:text-primary-400' 
                  : 'text-gray-400 dark:text-gray-500'
              }`} />
              <span>{item.label}</span>
              {item.badge && (
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  typeof item.badge === 'number'
                    ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                    : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                }`}>
                  {item.badge}
                </span>
              )}
              {item.isNew && (
                <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full">
                  New
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="w-5 h-5 text-gray-400" />
            ) : (
              <ChevronRight className="w-5 h-5 text-gray-400" />
            )}
          </button>

          {/* Children items */}
          <div className={`ml-6 space-y-1 overflow-hidden transition-all duration-300 ${
            isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}>
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </div>
        </div>
      )
    }

    // Leaf item
    return (
      <button
        key={item.id}
        onClick={() => handleItemClick(item)}
        className={`w-full flex items-center justify-between px-4 py-3 text-base font-medium rounded-lg transition-all duration-200 ${
          level > 0 ? 'text-sm' : ''
        } ${
          item.path && isActive(item.path)
            ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700'
        }`}
      >
        <div className="flex items-center space-x-3">
          <item.icon className={`w-${level > 0 ? '5' : '6'} h-${level > 0 ? '5' : '6'} ${
            item.path && isActive(item.path)
              ? 'text-primary-600 dark:text-primary-400' 
              : 'text-gray-400 dark:text-gray-500'
          }`} />
          <span>{item.label}</span>
          {item.badge && (
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
              typeof item.badge === 'number'
                ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
            }`}>
              {item.badge}
            </span>
          )}
          {item.isNew && (
            <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full">
              New
            </span>
          )}
        </div>
      </button>
    )
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" />
      
      {/* Mobile Menu */}
      <div 
        ref={menuRef}
        className={`fixed inset-y-0 left-0 w-80 max-w-[85vw] bg-white dark:bg-dark-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out lg:hidden overflow-hidden ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } ${className}`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-dark-700">
            <Link to="/" onClick={onClose} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-600 to-primary-400 rounded-lg flex items-center justify-center">
                <Home className="w-6 h-6 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-gray-900 dark:text-white">
                  VisionGuard
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Detection Analytics
                </span>
              </div>
            </Link>
            
            <button
              onClick={onClose}
              className="p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
              aria-label="Close menu"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Search */}
          <div className="px-4 py-3 border-b border-gray-200 dark:border-dark-700">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search pages..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto px-4 py-4">
            <nav className="space-y-2">
              {filteredItems.map(item => renderNavigationItem(item))}
            </nav>

            {searchQuery && filteredItems.length === 0 && (
              <div className="text-center py-8">
                <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  No results found for "{searchQuery}"
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 dark:border-dark-700 px-4 py-4 space-y-4">
            {/* Theme and Language Controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <ThemeToggle />
              </div>
              
              <div className="flex items-center space-x-2 text-sm">
                {isOnline ? (
                  <>
                    <Wifi className="w-4 h-4 text-green-500" />
                    <span className="text-green-600 dark:text-green-400">Online</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-4 h-4 text-red-500" />
                    <span className="text-red-600 dark:text-red-400">Offline</span>
                  </>
                )}
              </div>
            </div>

            {/* User Info */}
            <div className="flex items-center justify-between py-3 px-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">Admin User</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
                </div>
              </div>
              
              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                aria-label="Sign out"
              >
                <LogOut className="w-4 h-4" />
              </button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-3 text-center">
              <div className="px-3 py-2 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="text-lg font-bold text-gray-900 dark:text-white">3</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Active</div>
              </div>
              <div className="px-3 py-2 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="text-lg font-bold text-gray-900 dark:text-white">1.2k</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Today</div>
              </div>
              <div className="px-3 py-2 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="text-lg font-bold text-green-600 dark:text-green-400">99%</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">Uptime</div>
              </div>
            </div>

            {/* Version Info */}
            <div className="text-center text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-center space-x-1">
                <Zap className="w-3 h-3" />
                <span>VisionGuard v1.0.0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default MobileMenu