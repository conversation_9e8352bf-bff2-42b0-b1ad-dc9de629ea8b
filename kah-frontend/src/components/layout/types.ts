// src/components/layout/types.ts
export interface NavigationItem {
    id: string
    label: string
    icon: React.ComponentType<{ className?: string }>
    path?: string
    badge?: number | string
    children?: NavigationItem[]
    isNew?: boolean
    disabled?: boolean
  }
  
  export interface BreadcrumbItem {
    label: string
    href?: string
    icon?: React.ComponentType<{ className?: string }>
    current?: boolean
    disabled?: boolean
  }
  
  export type Theme = 'light' | 'dark' | 'auto' | 'high-contrast'