import React from 'react';

interface BaseInterfaceProps {
  children: React.ReactNode;
  getCurrentComponent: () => React.ReactNode;
  getPageTitle: () => string;
  getPageDescription: () => string;
  additionalContent?: React.ReactNode;
  actionButtons?: React.ReactNode;
}

const BaseInterface: React.FC<BaseInterfaceProps> = ({
  children,
  getCurrentComponent,
  getPageTitle,
  getPageDescription,
  additionalContent,
  actionButtons
}) => {
  return (
    <div className="p-6 space-y-6">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white shadow-lg">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold mb-2">{getPageTitle()}</h1>
            <p className="text-primary-100 text-lg">{getPageDescription()}</p>
          </div>
          {actionButtons && (
            <div className="flex space-x-3">
              {actionButtons}
            </div>
          )}
        </div>
      </div>

      {/* Additional Content (like status cards, etc.) */}
      {additionalContent}

      {/* Dynamic Content */}
      <div className="bg-white dark:bg-dark-800 rounded-xl shadow-sm border border-gray-200 dark:border-dark-700 overflow-hidden">
        {getCurrentComponent()}
      </div>

      {/* Children Content (for any additional components) */}
      {children}
    </div>
  );
};

export default BaseInterface; 