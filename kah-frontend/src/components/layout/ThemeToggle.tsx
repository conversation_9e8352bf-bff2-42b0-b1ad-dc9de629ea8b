import React, { useState, useRef, useEffect } from 'react'
import { Sun, Moon, Monitor, Palette, ChevronDown, Check } from 'lucide-react'

type Theme = 'light' | 'dark' | 'auto' | 'high-contrast'

interface ThemeOption {
  id: Theme
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}

const ThemeToggle: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentTheme, setCurrentTheme] = useState<Theme>('light')
  const [systemTheme, setSystemTheme] = useState<'light' | 'dark'>('light')
  const dropdownRef = useRef<HTMLDivElement>(null)

  const themeOptions: ThemeOption[] = [
    {
      id: 'light',
      name: 'Light',
      description: 'Clean, bright interface',
      icon: Sun
    },
    {
      id: 'dark',
      name: 'Dark',
      description: 'Easy on the eyes',
      icon: Moon
    },
    {
      id: 'auto',
      name: 'Auto',
      description: 'Follows system preference',
      icon: Monitor
    },
    {
      id: 'high-contrast',
      name: 'High Contrast',
      description: 'Enhanced visibility',
      icon: Palette
    }
  ]

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? 'dark' : 'light')
    }

    // Set initial system theme
    setSystemTheme(mediaQuery.matches ? 'dark' : 'light')

    // Listen for changes
    mediaQuery.addEventListener('change', handleSystemThemeChange)
    
    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange)
  }, [])

  // Load saved theme preference and apply on mount
  useEffect(() => {
    const savedTheme = localStorage.getItem('preferred-theme') as Theme
    if (savedTheme && themeOptions.find(option => option.id === savedTheme)) {
      setCurrentTheme(savedTheme)
      applyTheme(savedTheme)
    } else {
      // Check if user prefers dark mode by default
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      const defaultTheme: Theme = prefersDark ? 'dark' : 'light'
      setCurrentTheme(defaultTheme)
      applyTheme(defaultTheme)
    }
  }, [])

  // Apply theme whenever currentTheme or systemTheme changes
  useEffect(() => {
    applyTheme(currentTheme)
  }, [currentTheme, systemTheme])

  const applyTheme = (theme: Theme) => {
    const root = document.documentElement
    const body = document.body

    // Remove all theme classes
    root.classList.remove('light', 'dark', 'high-contrast')
    body.classList.remove('light', 'dark', 'high-contrast')

    let appliedTheme: 'light' | 'dark' | 'high-contrast'

    switch (theme) {
      case 'auto':
        appliedTheme = systemTheme === 'dark' ? 'dark' : 'light'
        break
      case 'high-contrast':
        appliedTheme = 'high-contrast'
        break
      default:
        appliedTheme = theme
    }

    // Apply theme classes
    root.classList.add(appliedTheme)
    body.classList.add(appliedTheme)

    // Special handling for high contrast theme
    if (appliedTheme === 'high-contrast') {
      root.classList.add('dark') // High contrast mode uses dark base
      body.style.setProperty('--high-contrast', 'true')
    } else {
      body.style.removeProperty('--high-contrast')
    }

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (metaThemeColor) {
      const color = appliedTheme === 'dark' || appliedTheme === 'high-contrast' 
        ? '#0f172a' // dark-900
        : '#ffffff' // white
      metaThemeColor.setAttribute('content', color)
    }

    // Save preference (except for auto, which should remember the actual choice)
    localStorage.setItem('preferred-theme', theme)

    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('themeChanged', { 
      detail: { 
        theme, 
        appliedTheme,
        isHighContrast: appliedTheme === 'high-contrast'
      }
    }))

    // Update CSS custom properties for theme-aware components
    updateThemeCustomProperties(appliedTheme)
  }

  const updateThemeCustomProperties = (theme: 'light' | 'dark' | 'high-contrast') => {
    const root = document.documentElement

    if (theme === 'high-contrast') {
      // High contrast theme overrides
      root.style.setProperty('--border-contrast', '2px')
      root.style.setProperty('--focus-ring-width', '3px')
      root.style.setProperty('--shadow-intensity', '0.5')
    } else {
      // Reset to default values
      root.style.setProperty('--border-contrast', '1px')
      root.style.setProperty('--focus-ring-width', '2px')
      root.style.setProperty('--shadow-intensity', '0.1')
    }
  }

  const handleThemeChange = (theme: Theme) => {
    setCurrentTheme(theme)
    setIsOpen(false)

    // Provide haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }

    // Log theme change for analytics
    console.log(`Theme changed to: ${theme}`)
  }

  const getCurrentThemeData = () => {
    return themeOptions.find(option => option.id === currentTheme) || themeOptions[0]
  }

  const getEffectiveTheme = (): 'light' | 'dark' | 'high-contrast' => {
    if (currentTheme === 'auto') {
      return systemTheme
    }
    return currentTheme as 'light' | 'dark' | 'high-contrast'
  }

  const currentThemeData = getCurrentThemeData()
  const effectiveTheme = getEffectiveTheme()

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Theme Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200"
        aria-label="Change theme"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <currentThemeData.icon className="w-5 h-5" />
        <span className="hidden sm:block text-sm font-medium">
          {currentTheme === 'auto' ? `Auto (${systemTheme})` : currentThemeData.name}
        </span>
        <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Theme Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-12 w-72 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-50 overflow-hidden">
          {/* Header */}
          <div className="p-3 border-b border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <div className="flex items-center space-x-2">
              <Palette className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Appearance
              </span>
            </div>
          </div>

          {/* Theme Options */}
          <div className="py-2" role="listbox">
            {themeOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => handleThemeChange(option.id)}
                className={`w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors duration-150 ${
                  currentTheme === option.id 
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' 
                    : 'text-gray-700 dark:text-gray-200'
                }`}
                role="option"
                aria-selected={currentTheme === option.id}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    currentTheme === option.id
                      ? 'bg-primary-100 dark:bg-primary-900'
                      : 'bg-gray-100 dark:bg-dark-600'
                  }`}>
                    <option.icon className={`w-4 h-4 ${
                      currentTheme === option.id
                        ? 'text-primary-600 dark:text-primary-400'
                        : 'text-gray-500 dark:text-gray-400'
                    }`} />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">
                      {option.name}
                      {option.id === 'auto' && (
                        <span className="ml-1 text-xs text-gray-500 dark:text-gray-400">
                          ({systemTheme})
                        </span>
                      )}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {option.description}
                    </span>
                  </div>
                </div>
                
                {currentTheme === option.id && (
                  <Check className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                )}
              </button>
            ))}
          </div>

          {/* Preview Section */}
          <div className="p-3 border-t border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
              <div className="flex items-center justify-between">
                <span>Current theme:</span>
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {effectiveTheme === 'high-contrast' ? 'High Contrast' : 
                   effectiveTheme.charAt(0).toUpperCase() + effectiveTheme.slice(1)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>System preference:</span>
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {systemTheme.charAt(0).toUpperCase() + systemTheme.slice(1)}
                </span>
              </div>
              {effectiveTheme === 'high-contrast' && (
                <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-blue-700 dark:text-blue-300">
                  <span className="text-xs">
                    ♿ Enhanced accessibility mode active
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Keyboard Shortcut Info */}
          <div className="p-3 border-t border-gray-200 dark:border-dark-700">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-between">
                <span>Quick toggle:</span>
                <span className="font-mono bg-gray-200 dark:bg-dark-600 px-2 py-1 rounded">
                  Ctrl + Shift + T
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ThemeToggle