import React, { useState, useRef, useEffect } from 'react'
import { Sun, Moon, Monitor, Palette, ChevronDown, Check } from 'lucide-react'
import { useTheme } from '../../context/ThemeContext'

type Theme = 'light' | 'dark' | 'auto' | 'high-contrast'

interface ThemeOption {
  id: Theme
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}

const ThemeToggle: React.FC = () => {
  const { currentTheme, setTheme, systemTheme, effectiveTheme, isDark, isHighContrast } = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const themeOptions: ThemeOption[] = [
    {
      id: 'light',
      name: 'Light',
      description: 'Clean, bright interface',
      icon: Sun
    },
    {
      id: 'dark',
      name: 'Dark',
      description: 'Easy on the eyes',
      icon: Moon
    },
    {
      id: 'auto',
      name: 'Auto',
      description: 'Follows system preference',
      icon: Monitor
    },
    {
      id: 'high-contrast',
      name: 'High Contrast',
      description: 'Enhanced visibility',
      icon: Palette
    }
  ]

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl + Shift + T to toggle between light and dark
      if (event.ctrlKey && event.shiftKey && event.key === 'T') {
        event.preventDefault()
        
        // Toggle between light and dark themes
        const newTheme = currentTheme === 'light' ? 'dark' : 'light'
        setTheme(newTheme)
        
        // Show a brief notification
        showThemeChangeNotification(newTheme)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [currentTheme, setTheme])

  // Show theme change notification
  const showThemeChangeNotification = (theme: Theme) => {
    // Create a temporary notification element
    const notification = document.createElement('div')
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
      theme === 'dark' 
        ? 'bg-gray-800 text-white' 
        : 'bg-white text-gray-900 border border-gray-200'
    }`
    notification.innerHTML = `
      <div class="flex items-center space-x-2">
        <span class="text-sm font-medium">Theme changed to ${theme}</span>
      </div>
    `
    
    document.body.appendChild(notification)
    
    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full')
    }, 10)
    
    // Remove after 2 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full')
      setTimeout(() => {
        document.body.removeChild(notification)
      }, 300)
    }, 2000)
  }

  const handleThemeChange = (theme: Theme) => {
    setTheme(theme)
    setIsOpen(false)

    // Provide haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }

    // Log theme change for analytics
    console.log(`Theme changed to: ${theme}`)
  }

  const getCurrentThemeData = () => {
    return themeOptions.find(option => option.id === currentTheme) || themeOptions[0]
  }

  const currentThemeData = getCurrentThemeData()

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Theme Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 p-2 rounded-lg text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200"
        aria-label="Change theme"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <currentThemeData.icon className="w-5 h-5" />
        <span className="hidden sm:block text-sm font-medium">
          {currentTheme === 'auto' ? `Auto (${systemTheme})` : currentThemeData.name}
        </span>
        <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Theme Dropdown */}
      {isOpen && (
        <div className="absolute right-0 top-12 w-72 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-50 overflow-hidden">
          {/* Header */}
          <div className="p-3 border-b border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <div className="flex items-center space-x-2">
              <Palette className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Appearance
              </span>
            </div>
          </div>

          {/* Theme Options */}
          <div className="py-2" role="listbox">
            {themeOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => handleThemeChange(option.id)}
                className={`w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors duration-150 ${
                  currentTheme === option.id 
                    ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' 
                    : 'text-gray-700 dark:text-gray-200'
                }`}
                role="option"
                aria-selected={currentTheme === option.id}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    currentTheme === option.id
                      ? 'bg-primary-100 dark:bg-primary-900'
                      : 'bg-gray-100 dark:bg-dark-600'
                  }`}>
                    <option.icon className={`w-4 h-4 ${
                      currentTheme === option.id
                        ? 'text-primary-600 dark:text-primary-400'
                        : 'text-gray-500 dark:text-gray-400'
                    }`} />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">
                      {option.name}
                      {option.id === 'auto' && (
                        <span className="ml-1 text-xs text-gray-500 dark:text-gray-400">
                          ({systemTheme})
                        </span>
                      )}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {option.description}
                    </span>
                  </div>
                </div>
                
                {currentTheme === option.id && (
                  <Check className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                )}
              </button>
            ))}
          </div>

          {/* Preview Section */}
          <div className="p-3 border-t border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
              <div className="flex items-center justify-between">
                <span>Current theme:</span>
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {effectiveTheme === 'high-contrast' ? 'High Contrast' : 
                   effectiveTheme.charAt(0).toUpperCase() + effectiveTheme.slice(1)}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>System preference:</span>
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {systemTheme.charAt(0).toUpperCase() + systemTheme.slice(1)}
                </span>
              </div>
              {effectiveTheme === 'high-contrast' && (
                <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-blue-700 dark:text-blue-300">
                  <span className="text-xs">
                    ♿ Enhanced accessibility mode active
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Keyboard Shortcut Info */}
          <div className="p-3 border-t border-gray-200 dark:border-dark-700">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center justify-between">
                <span>Quick toggle:</span>
                <span className="font-mono bg-gray-200 dark:bg-dark-600 px-2 py-1 rounded">
                  Ctrl + Shift + T
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ThemeToggle