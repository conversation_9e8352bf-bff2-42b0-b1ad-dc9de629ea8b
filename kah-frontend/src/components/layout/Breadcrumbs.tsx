import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  ChevronRight, 
  Home, 
  Camera, 
  BarChart3, 
  History, 
  Settings, 
  Plug, 
  User, 
  HelpCircle,
  Eye,
  TrendingUp,
  MapPin,
  FileText,
  Database,
  Download,
  Archive,
  Target,
  Video,
  Bell,
  Calendar,
  Clock,
  Filter
} from 'lucide-react'

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ComponentType<{ className?: string }>
  current?: boolean
  disabled?: boolean
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[]
  className?: string
  showIcons?: boolean
  showHome?: boolean
  maxItems?: number
  separator?: 'chevron' | 'slash' | 'dot'
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className = '',
  showIcons = true,
  showHome = true,
  maxItems = 5,
  separator = 'chevron'
}) => {
  const location = useLocation()

  // Route-to-breadcrumb mapping
  const routeMap: Record<string, { label: string; icon?: React.ComponentType<{ className?: string }> }> = {
    '/': { label: 'Dashboard', icon: Home },
    '/live': { label: 'Live Detection', icon: Camera },
    '/analytics': { label: 'Analytics', icon: BarChart3 },
    '/analytics/overview': { label: 'Overview', icon: Eye },
    '/analytics/trends': { label: 'Traffic Trends', icon: TrendingUp },
    '/analytics/zones': { label: 'Zone Performance', icon: MapPin },
    '/analytics/reports': { label: 'Reports', icon: FileText },
    '/historical': { label: 'Historical Data', icon: History },
    '/historical/sessions': { label: 'Sessions', icon: Database },
    '/historical/data': { label: 'Data Explorer', icon: Archive },
    '/historical/export': { label: 'Export Center', icon: Download },
    '/configuration': { label: 'Configuration', icon: Settings },
    '/configuration/detection': { label: 'Detection Models', icon: Target },
    '/configuration/camera': { label: 'Camera Settings', icon: Video },
    '/configuration/alerts': { label: 'Alerts & Notifications', icon: Bell },
    '/integrations': { label: 'Integrations', icon: Plug },
    '/account': { label: 'Account', icon: User },
    '/help': { label: 'Help & Support', icon: HelpCircle }
  }

  // Generate breadcrumbs from current route if no custom items provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (items) return items

    const pathSegments = location.pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []

    // Add home if showHome is true and we're not on home page
    if (showHome && location.pathname !== '/') {
      breadcrumbs.push({
        label: 'Dashboard',
        href: '/',
        icon: Home
      })
    }

    // Build path progressively
    let currentPath = ''
    for (let i = 0; i < pathSegments.length; i++) {
      currentPath += `/${pathSegments[i]}`
      const routeInfo = routeMap[currentPath]
      
      if (routeInfo) {
        breadcrumbs.push({
          label: routeInfo.label,
          href: i === pathSegments.length - 1 ? undefined : currentPath, // Don't link to current page
          icon: routeInfo.icon,
          current: i === pathSegments.length - 1
        })
      } else {
        // Handle dynamic routes or unknown paths
        const segment = pathSegments[i]
        const formattedLabel = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
        
        breadcrumbs.push({
          label: formattedLabel,
          href: i === pathSegments.length - 1 ? undefined : currentPath,
          current: i === pathSegments.length - 1
        })
      }
    }

    return breadcrumbs
  }

  const breadcrumbItems = generateBreadcrumbs()

  // Truncate breadcrumbs if they exceed maxItems
  const truncatedBreadcrumbs = breadcrumbItems.length > maxItems
    ? [
        ...breadcrumbItems.slice(0, 1), // First item (usually Home)
        { label: '...', disabled: true }, // Ellipsis
        ...breadcrumbItems.slice(-Math.max(maxItems - 2, 1)) // Last items
      ]
    : breadcrumbItems

  const getSeparator = () => {
    switch (separator) {
      case 'slash':
        return <span className="text-gray-400 dark:text-gray-500 mx-2">/</span>
      case 'dot':
        return <span className="text-gray-400 dark:text-gray-500 mx-2">•</span>
      default:
        return <ChevronRight className="w-4 h-4 text-gray-400 dark:text-gray-500 mx-2" />
    }
  }

  const renderBreadcrumbItem = (item: BreadcrumbItem, index: number, isLast: boolean) => {
    const isEllipsis = item.label === '...'
    
    if (isEllipsis) {
      return (
        <span key={index} className="text-gray-400 dark:text-gray-500 px-2">
          {item.label}
        </span>
      )
    }

    const content = (
      <span className={`flex items-center space-x-1 ${
        item.current || isLast || item.disabled
          ? 'text-gray-900 dark:text-white font-medium'
          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
      } ${item.disabled ? 'cursor-not-allowed' : ''}`}>
        {showIcons && item.icon && (
          <item.icon className={`w-4 h-4 ${
            item.current || isLast
              ? 'text-primary-600 dark:text-primary-400'
              : 'text-gray-400 dark:text-gray-500'
          }`} />
        )}
        <span className="text-sm font-medium">{item.label}</span>
      </span>
    )

    if (item.href && !item.current && !item.disabled) {
      return (
        <Link
          key={index}
          to={item.href}
          className="transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-dark-700 rounded px-2 py-1 -mx-2 -my-1"
          aria-current={item.current ? 'page' : undefined}
        >
          {content}
        </Link>
      )
    }

    return (
      <span 
        key={index}
        className={item.current ? 'px-2 py-1 -mx-2 -my-1' : ''}
        aria-current={item.current ? 'page' : undefined}
      >
        {content}
      </span>
    )
  }

  if (truncatedBreadcrumbs.length === 0) {
    return null
  }

  // Don't show breadcrumbs if only showing home and we're on home page
  if (truncatedBreadcrumbs.length === 1 && truncatedBreadcrumbs[0].href === '/') {
    return null
  }

  return (
    <nav 
      className={`flex items-center space-x-1 text-sm ${className}`}
      aria-label="Breadcrumb"
    >
      <ol className="flex items-center space-x-1">
        {truncatedBreadcrumbs.map((item, index) => {
          const isLast = index === truncatedBreadcrumbs.length - 1
          
          return (
            <li key={index} className="flex items-center">
              {renderBreadcrumbItem(item, index, isLast)}
              {!isLast && getSeparator()}
            </li>
          )
        })}
      </ol>

      {/* Additional Context Information */}
      {location.search && (
        <div className="flex items-center ml-4 pl-4 border-l border-gray-300 dark:border-gray-600">
          <Filter className="w-3 h-3 text-gray-400 mr-1" />
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Filtered
          </span>
        </div>
      )}

      {/* Last Modified Info (for specific pages) */}
      {(location.pathname.includes('/historical/') || location.pathname.includes('/analytics/')) && (
        <div className="flex items-center ml-4 pl-4 border-l border-gray-300 dark:border-gray-600">
          <Clock className="w-3 h-3 text-gray-400 mr-1" />
          <span className="text-xs text-gray-500 dark:text-gray-400">
            Updated {new Date().toLocaleDateString()}
          </span>
        </div>
      )}
    </nav>
  )
}

// Preset breadcrumb configurations for common use cases
export const BreadcrumbPresets = {
  // Minimal breadcrumbs with just text
  minimal: (items?: BreadcrumbItem[]) => (
    <Breadcrumbs 
      items={items}
      showIcons={false}
      showHome={false}
      separator="slash"
      className="text-xs"
    />
  ),

  // Full featured breadcrumbs
  full: (items?: BreadcrumbItem[]) => (
    <Breadcrumbs 
      items={items}
      showIcons={true}
      showHome={true}
      separator="chevron"
      maxItems={6}
    />
  ),

  // Compact breadcrumbs for mobile
  compact: (items?: BreadcrumbItem[]) => (
    <Breadcrumbs 
      items={items}
      showIcons={false}
      showHome={false}
      maxItems={3}
      separator="dot"
      className="text-xs"
    />
  ),

  // Custom breadcrumbs for specific sections
  analytics: () => (
    <Breadcrumbs 
      items={[
        { label: 'Analytics', href: '/analytics', icon: BarChart3 },
        { label: 'Traffic Analysis', current: true, icon: TrendingUp }
      ]}
      showIcons={true}
      showHome={true}
    />
  ),

  // Session-specific breadcrumbs
  session: (sessionId: string, sessionName?: string) => (
    <Breadcrumbs 
      items={[
        { label: 'Historical Data', href: '/historical', icon: History },
        { label: 'Sessions', href: '/historical/sessions', icon: Database },
        { 
          label: sessionName || `Session ${sessionId}`, 
          current: true, 
          icon: Calendar 
        }
      ]}
      showIcons={true}
      showHome={true}
    />
  )
}

export default Breadcrumbs