import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Activity, 
  Wifi, 
  WifiOff, 
  Clock, 
  Server, 
  Database, 
  Shield, 
  ExternalLink,
  Heart,
  Zap,
  Globe,
  Github,
  Mail,
  Phone
} from 'lucide-react'

interface SystemStatus {
  api: 'online' | 'offline' | 'degraded'
  database: 'online' | 'offline' | 'degraded'
  detection: 'online' | 'offline' | 'degraded'
  storage: 'online' | 'offline' | 'degraded'
}

interface FooterProps {
  compact?: boolean
}

const Footer: React.FC<FooterProps> = ({ compact = false }) => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [lastSync, setLastSync] = useState(new Date())
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    api: 'online',
    database: 'online',
    detection: 'online',
    storage: 'degraded'
  })

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Update sync time every 30 seconds
  useEffect(() => {
    const syncTimer = setInterval(() => {
      setLastSync(new Date())
    }, 30000)

    return () => clearInterval(syncTimer)
  }, [])

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Mock system status updates
  useEffect(() => {
    const statusTimer = setInterval(() => {
      // Simulate occasional status changes
      if (Math.random() < 0.1) { // 10% chance of status change
        setSystemStatus(prev => ({
          ...prev,
          storage: prev.storage === 'online' ? 'degraded' : 'online'
        }))
      }
    }, 10000)

    return () => clearInterval(statusTimer)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-500'
      case 'degraded': return 'text-yellow-500'
      case 'offline': return 'text-red-500'
      default: return 'text-gray-400'
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const getTimeSince = (date: Date) => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000)
    if (seconds < 60) return `${seconds}s ago`
    const minutes = Math.floor(seconds / 60)
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ago`
  }

  const appVersion = import.meta.env.VITE_APP_VERSION || '1.0.0'
  const buildNumber = import.meta.env.VITE_BUILD_NUMBER || '2024.01.15'

  if (compact) {
    return (
      <footer className="bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 px-4 py-2">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-3">
            <span>© 2024 VisionGuard</span>
            <div className="flex items-center space-x-1">
              {isOnline ? (
                <Wifi className="w-3 h-3 text-green-500" />
              ) : (
                <WifiOff className="w-3 h-3 text-red-500" />
              )}
              <span>{isOnline ? 'Online' : 'Offline'}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <span>v{appVersion}</span>
            <span>{formatTime(currentTime)}</span>
          </div>
        </div>
      </footer>
    )
  }

  return (
    <footer className="bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700">
      {/* Main Footer Content */}
      <div className="px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-600 to-primary-400 rounded-lg flex items-center justify-center">
                <Activity className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-bold text-gray-900 dark:text-white">
                VisionGuard
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Advanced video detection analytics platform for real-time monitoring and intelligent insights.
            </p>
            <div className="flex items-center space-x-3">
              <a 
                href="https://github.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Github className="w-5 h-5" />
              </a>
              <a 
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Mail className="w-5 h-5" />
              </a>
              <a 
                href="tel:******-0123"
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              >
                <Phone className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <Link 
                  to="/live" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Live Detection
                </Link>
              </li>
              <li>
                <Link 
                  to="/analytics" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Analytics Dashboard
                </Link>
              </li>
              <li>
                <Link 
                  to="/historical" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Historical Data
                </Link>
              </li>
              <li>
                <Link 
                  to="/configuration" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Configuration
                </Link>
              </li>
              <li>
                <Link 
                  to="/help" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Help & Support
                </Link>
              </li>
            </ul>
          </div>

          {/* System Status */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">
              System Status
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Server className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">API</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${
                    systemStatus.api === 'online' ? 'bg-green-500 animate-pulse' :
                    systemStatus.api === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className={`text-xs font-medium ${getStatusColor(systemStatus.api)}`}>
                    {systemStatus.api}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Database</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${
                    systemStatus.database === 'online' ? 'bg-green-500 animate-pulse' :
                    systemStatus.database === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className={`text-xs font-medium ${getStatusColor(systemStatus.database)}`}>
                    {systemStatus.database}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Activity className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Detection</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${
                    systemStatus.detection === 'online' ? 'bg-green-500 animate-pulse' :
                    systemStatus.detection === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className={`text-xs font-medium ${getStatusColor(systemStatus.detection)}`}>
                    {systemStatus.detection}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Shield className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Storage</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className={`w-2 h-2 rounded-full ${
                    systemStatus.storage === 'online' ? 'bg-green-500 animate-pulse' :
                    systemStatus.storage === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className={`text-xs font-medium ${getStatusColor(systemStatus.storage)}`}>
                    {systemStatus.storage}
                  </span>
                </div>
              </div>
            </div>

            <Link 
              to="/status" 
              className="inline-flex items-center text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mt-3"
            >
              View detailed status
              <ExternalLink className="w-3 h-3 ml-1" />
            </Link>
          </div>

          {/* Legal & Info */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">
              Legal & Info
            </h3>
            <ul className="space-y-2 mb-4">
              <li>
                <a 
                  href="/privacy" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a 
                  href="/terms" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Terms of Service
                </a>
              </li>
              <li>
                <a 
                  href="/security" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Security
                </a>
              </li>
              <li>
                <a 
                  href="/api-docs" 
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  API Documentation
                </a>
              </li>
            </ul>

            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
              <div>Version {appVersion}</div>
              <div>Build {buildNumber}</div>
              <div className="flex items-center space-x-1">
                <span>Made with</span>
                <Heart className="w-3 h-3 text-red-500" />
                <span>by VisionGuard Team</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-200 dark:border-dark-700 px-6 py-4">
        <div className="flex flex-col sm:flex-row items-center justify-between space-y-2 sm:space-y-0">
          {/* Left side - Copyright and Connection */}
          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <span>© 2024 VisionGuard Analytics. All rights reserved.</span>
            <div className="flex items-center space-x-1">
              {isOnline ? (
                <>
                  <Wifi className="w-4 h-4 text-green-500" />
                  <span className="text-green-600 dark:text-green-400">Online</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-4 h-4 text-red-500" />
                  <span className="text-red-600 dark:text-red-400">Offline</span>
                </>
              )}
            </div>
          </div>

          {/* Right side - Time and Sync Info */}
          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{formatTime(currentTime)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Zap className="w-4 h-4" />
              <span>Last sync: {getTimeSince(lastSync)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Globe className="w-4 h-4" />
              <span>UTC{new Date().getTimezoneOffset() > 0 ? '-' : '+'}
                {Math.abs(new Date().getTimezoneOffset() / 60)}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer