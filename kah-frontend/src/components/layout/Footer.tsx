import React, { useState, useEffect } from 'react'
import { 
  Wifi, 
  WifiOff
} from 'lucide-react'

interface FooterProps {
  compact?: boolean
}

const Footer: React.FC<FooterProps> = ({ compact = false }) => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isOnline, setIsOnline] = useState(navigator.onLine)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Listen for online/offline events
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  if (compact) {
    return (
      <footer className="bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 px-4 py-2">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-3">
            <span>© 2024 KAH</span>
            <div className="flex items-center space-x-1">
              {isOnline ? (
                <Wifi className="w-3 h-3 text-green-500" />
              ) : (
                <WifiOff className="w-3 h-3 text-red-500" />
              )}
              <span>{isOnline ? 'Online' : 'Offline'}</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <span>v1.0.0</span>
            <span>{currentTime.toLocaleTimeString()}</span>
          </div>
        </div>
      </footer>
    )
  }

  return (
    <footer className="bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 px-6 py-3">
      <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          <span>© 2024 Kaydan Analytic Hub</span>
          <span>•</span>
          <span>Version 1.0.0</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span>System Online</span>
          </div>
          <span>•</span>
          <span>Last sync: {currentTime.toLocaleTimeString()}</span>
        </div>
      </div>
    </footer>
  )
}

export default Footer