import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  Settings, 
  User, 
  Home,
  ChevronDown,
  ChevronRight,
  Activity,
  Database as DonneeIcon,
  BarChart3 as AnalyseIcon,
  Share as PublieIcon,
  ClipboardList as ReportIcon
} from 'lucide-react'
import { useAuth } from '../../context/AuthContext';

interface NavigationItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  path?: string
  badge?: number | string
  children?: NavigationItem[]
  isNew?: boolean
}

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const isChildActive = (children: NavigationItem[] = []) => {
    return children.some(child => child.path && isActive(child.path))
  }

  // Get user role display name
  const getUserRoleDisplay = () => {
    const userRole = user?.role?.name || 'analyst';
    
    switch (userRole) {
      case 'analyst':
        return 'Data Analyst';
      case 'decision_board':
        return 'Decision Board';
      case 'operation_board':
        return 'Operations Board';
      case 'super_admin':
        return 'Super Admin';
      case 'admin':
        return 'Administrator';
      default:
        return 'User';
    }
  }

  // Role-specific navigation items
  const getNavigationItems = (): NavigationItem[] => {
    const userRole = user?.role?.name || 'analyst';

    switch (userRole) {
      case 'analyst':
        return [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            path: '/analyst/dashboard'
          },
          {
            id: 'donnee',
            label: 'Données',
            icon: DonneeIcon,
            path: '/analyst/donnee'
          },
          {
            id: 'analyse',
            label: 'Analyse',
            icon: AnalyseIcon,
            path: '/analyst/analyse'
          },
          {
            id: 'publie',
            label: 'Publie',
            icon: PublieIcon,
            path: '/analyst/publie'
          }
        ];

      case 'decision_board':
        return [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            path: '/board/dashboard'
          },
          {
            id: 'report',
            label: 'Report',
            icon: ReportIcon,
            path: '/board/report'
          }
        ];

      case 'operation_board':
        return [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            path: '/operations/dashboard'
          },
          {
            id: 'projects',
            label: 'Suivi de Projet',
            icon: Activity,
            path: '/operations/projects'
          },
          {
            id: 'stock',
            label: 'Stock',
            icon: DonneeIcon,
            path: '/operations/stock'
          },
          {
            id: 'commercial',
            label: 'Performance Commercial',
            icon: AnalyseIcon,
            path: '/operations/commercial'
          },
          {
            id: 'maintenance',
            label: 'Maintenance Prédictive',
            icon: ReportIcon,
            path: '/operations/maintenance'
          }
        ];

      case 'super_admin':
      case 'admin':
        return [
          {
            id: 'dashboard',
            label: 'Admin Dashboard',
            icon: Home,
            path: '/admin/dashboard'
          },
          {
            id: 'users',
            label: 'User Management',
            icon: User,
            path: '/admin/users'
          },
          {
            id: 'settings',
            label: 'System Settings',
            icon: Settings,
            path: '/admin/settings'
          }
        ];

      default:
        return [
          {
            id: 'dashboard',
            label: 'Dashboard',
            icon: Home,
            path: '/dashboard'
          }
        ];
    }
  };

  const navigationItems = getNavigationItems();

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)
    const isItemActive = item.path ? isActive(item.path) : isChildActive(item.children)

    if (hasChildren) {
      return (
        <div key={item.id} className="space-y-1">
          {/* Parent item */}
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ${
              isItemActive
                ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg'
                : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <div className="flex items-center space-x-3">
              <item.icon className={`w-5 h-5 ${
                isItemActive 
                  ? 'text-white' 
                  : 'text-gray-400 dark:text-gray-500'
              }`} />
              <span>{item.label}</span>
              {item.badge && (
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                  typeof item.badge === 'number'
                    ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                    : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                }`}>
                  {item.badge}
                </span>
              )}
              {item.isNew && (
                <span className="px-2 py-0.5 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full">
                  New
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-gray-400" />
            ) : (
              <ChevronRight className="w-4 h-4 text-gray-400" />
            )}
          </button>

          {/* Children items */}
          <div className={`ml-4 space-y-1 overflow-hidden transition-all duration-300 ${
            isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}>
            {item.children?.map(child => renderNavigationItem(child, level + 1))}
          </div>
        </div>
      )
    }

    // Leaf item (no children)
    return (
      <Link
        key={item.id}
        to={item.path || '#'}
        className={`flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group ${
          level > 0 ? 'ml-8' : ''
        } ${
          isItemActive
            ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg'
            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700 hover:text-gray-900 dark:hover:text-white'
        }`}
      >
        <div className="flex items-center space-x-3">
          <item.icon className={`w-5 h-5 ${
            isItemActive 
              ? 'text-white' 
              : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300'
          }`} />
          <span>{item.label}</span>
          {item.badge && (
            <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
              typeof item.badge === 'number'
                ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
            }`}>
              {item.badge}
            </span>
          )}
          {item.isNew && (
            <span className="px-2 py-0.5 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full">
              New
            </span>
          )}
        </div>
      </Link>
    )
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-dark-800 shadow-xl">
      {/* Logo/Brand */}
      <div className="flex items-center px-6 py-8 border-b border-gray-200 dark:border-dark-700 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20">
        <Link to="/" className="flex items-center space-x-3 group">
          <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-primary-700 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-200">
            <Activity className="w-7 h-7 text-white" />
          </div>
          <div className="flex flex-col">
            <span className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
              KAH
            </span>
            <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">
              {getUserRoleDisplay()}
            </span>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-3 overflow-y-auto">
        {navigationItems.map(item => renderNavigationItem(item))}
      </nav>

      {/* User Info */}
      <div className="px-4 py-4 border-t border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700/50">
        <div className="flex items-center space-x-3 p-3 rounded-xl bg-white dark:bg-dark-800 shadow-sm">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {user?.first_name} {user?.last_name}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {user?.email}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sidebar