import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  Home, 
  Camera, 
  BarChart3, 
  History, 
  Settings, 
  Plug, 
  User, 
  HelpCircle,
  ChevronDown,
  ChevronRight,
  TrendingUp,
  MapPin,
  FileText,
  Database,
  Download,
  Archive,
  Sliders,
  Video,
  Bell,
  Target,
  Eye,
  Shield
} from 'lucide-react'

interface NavigationItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  path?: string
  badge?: number | string
  children?: NavigationItem[]
  isNew?: boolean
  disabled?: boolean
  description?: string
}

interface NavigationProps {
  className?: string
  orientation?: 'vertical' | 'horizontal'
  showLabels?: boolean
  compact?: boolean
  onItemClick?: (item: NavigationItem) => void
}

const Navigation: React.FC<NavigationProps> = ({ 
  

  className = '',
  orientation = 'vertical',
  showLabels = true,
  compact = false,
  onItemClick
}) => {
  const location = useLocation()
  const [expandedItems, setExpandedItems] = useState<string[]>(['analytics', 'historical', 'configuration'])
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  // Auto-expand parent items based on current route
  useEffect(() => {
    const path = location.pathname
    const newExpanded = [...expandedItems]

    if (path.startsWith('/analytics') && !newExpanded.includes('analytics')) {
      newExpanded.push('analytics')
    }
    if (path.startsWith('/historical') && !newExpanded.includes('historical')) {
      newExpanded.push('historical')
    }
    if (path.startsWith('/configuration') && !newExpanded.includes('configuration')) {
      newExpanded.push('configuration')
    }

    setExpandedItems(newExpanded)
  }, [location.pathname])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const isChildActive = (children: NavigationItem[] = []) => {
    return children.some(child => child.path && isActive(child.path))
  }

  const handleItemClick = (item: NavigationItem, event?: React.MouseEvent) => {
    if (item.disabled) {
      event?.preventDefault()
      return
    }
    
    onItemClick?.(item)
  }
  

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      path: '/',
      description: 'Main overview and quick access'
    },
    {
      id: 'live-detection',
      label: 'Live Detection',
      icon: Camera,
      path: '/live',
      badge: 3, // Active sessions
      description: 'Real-time object detection and monitoring'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      description: 'Data visualization and insights',
      children: [
        {
          id: 'analytics-overview',
          label: 'Overview',
          icon: Eye,
          path: '/analytics/overview',
          description: 'Key metrics and performance summary'
        },
        {
          id: 'analytics-trends',
          label: 'Traffic Trends',
          icon: TrendingUp,
          path: '/analytics/trends',
          description: 'Historical patterns and forecasting'
        },
        {
          id: 'analytics-zones',
          label: 'Zone Performance',
          icon: MapPin,
          path: '/analytics/zones',
          description: 'Area-specific analytics and comparisons'
        },
        {
          id: 'analytics-reports',
          label: 'Reports',
          icon: FileText,
          path: '/analytics/reports',
          isNew: true,
          description: 'Generated reports and exports'
        }
      ]
    },
    {
      id: 'historical',
      label: 'Historical Data',
      icon: History,
      description: 'Past sessions and archived data',
      children: [
        {
          id: 'historical-sessions',
          label: 'Sessions',
          icon: Database,
          path: '/historical/sessions',
          description: 'Browse and replay past detection sessions'
        },
        {
          id: 'historical-data',
          label: 'Data Explorer',
          icon: Archive,
          path: '/historical/data',
          description: 'Advanced data querying and analysis'
        },
        {
          id: 'historical-export',
          label: 'Export Center',
          icon: Download,
          path: '/historical/export',
          description: 'Download data in various formats'
        }
      ]
    },
    {
      id: 'configuration',
      label: 'Configuration',
      icon: Settings,
      description: 'System settings and preferences',
      children: [
        {
          id: 'config-detection',
          label: 'Detection Models',
          icon: Target,
          path: '/configuration/detection',
          description: 'AI model settings and optimization'
        },
        {
          id: 'config-camera',
          label: 'Camera Settings',
          icon: Video,
          path: '/configuration/camera',
          description: 'Video input and quality configuration'
        },
        {
          id: 'config-zones',
          label: 'Zones',
          icon: MapPin,
          path: '/configuration/zones',
          description: 'Zone settings and management'
        },
        {
          id: 'config-alerts',
          label: 'Alerts & Notifications',
          icon: Bell,
          path: '/configuration/alerts',
          description: 'Notification rules and thresholds'
        },
        {
          id: 'config-general',
          label: 'General',
          icon: Settings,
          path: '/configuration/general',
          description: 'General settings and preferences'
        },
        {
          id: 'config-privacy-security',
          label: 'Privacy & Security',
          icon: Shield,
          path: '/configuration/privacy-security',
          description: 'Privacy and security settings'
        },
        {
          id: 'config-performance-settings',
          label: 'Performance Settings',
          icon: Sliders,
          path: '/configuration/performance-settings',
          description: 'Performance settings and optimization'
        },
        {
          id: 'config-import-export',
          label: 'Import & Export',
          icon: Download,
          path: '/configuration/import-export',
          description: 'Import and export settings and data'
        }
      ]
    },
    {
      id: 'integrations',
      label: 'Integrations',
      icon: Plug,
      path: '/integrations',
      badge: 'Beta',
      description: 'API connections and third-party services'
    },
    {
      id: 'account',
      label: 'Account',
      icon: User,
      path: '/account',
      description: 'User profile and team management'
    },
    {
      id: 'help',
      label: 'Help & Support',
      icon: HelpCircle,
      path: '/help',
      description: 'Documentation and assistance'
    }
  ]

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.id)
    const isItemActive = item.path ? isActive(item.path) : isChildActive(item.children)

    if (hasChildren) {
      return (
        <div key={item.id} className="space-y-1">
          {/* Parent item */}
          <button
            onClick={() => toggleExpanded(item.id)}
            onMouseEnter={() => setHoveredItem(item.id)}
            onMouseLeave={() => setHoveredItem(null)}
            disabled={item.disabled}
            className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 group ${
              isItemActive
                ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                : item.disabled
                ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700'
            } ${orientation === 'horizontal' ? 'flex-col text-center px-2' : ''}`}
            title={compact ? item.label : item.description}
          >
            <div className={`flex items-center ${orientation === 'horizontal' ? 'flex-col' : 'space-x-3'}`}>
              <item.icon className={`w-5 h-5 ${
                isItemActive 
                  ? 'text-primary-600 dark:text-primary-400' 
                  : item.disabled
                  ? 'text-gray-400 dark:text-gray-600'
                  : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300'
              } ${orientation === 'horizontal' ? 'mb-1' : ''}`} />
              
              {(showLabels && !compact) && (
                <span className={orientation === 'horizontal' ? 'text-xs' : ''}>{item.label}</span>
              )}
              
              {(showLabels && !compact) && item.badge && (
                <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                  typeof item.badge === 'number'
                    ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                    : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                }`}>
                  {item.badge}
                </span>
              )}
              
              {(showLabels && !compact) && item.isNew && (
                <span className="px-2 py-0.5 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full">
                  New
                </span>
              )}
            </div>
            
            {(showLabels && !compact && orientation === 'vertical') && (
              <div className="flex items-center space-x-1">
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-400" />
                )}
              </div>
            )}
          </button>

          {/* Children items */}
          {orientation === 'vertical' && (
            <div className={`ml-4 space-y-1 overflow-hidden transition-all duration-300 ${
              isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
            }`}>
              {item.children?.map(child => renderNavigationItem(child, level + 1))}
            </div>
          )}

          {/* Horizontal dropdown */}
          {orientation === 'horizontal' && isExpanded && (
            <div className="absolute top-full left-0 mt-2 bg-white dark:bg-dark-800 rounded-lg shadow-lg border border-gray-200 dark:border-dark-700 z-50 min-w-48">
              <div className="py-2">
                {item.children?.map(child => (
                  <Link
                    key={child.id}
                    to={child.path || '#'}
                    onClick={(e) => handleItemClick(child, e)}
                    className={`flex items-center space-x-2 px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors ${
                      child.path && isActive(child.path)
                        ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20'
                        : 'text-gray-700 dark:text-gray-200'
                    }`}
                  >
                    <child.icon className="w-4 h-4" />
                    <span>{child.label}</span>
                    {child.isNew && (
                      <span className="px-1.5 py-0.5 text-xs bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded">
                        New
                      </span>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      )
    }

    // Leaf item (no children)
    return (
      <Link
        key={item.id}
        to={item.path || '#'}
        onClick={(e) => handleItemClick(item, e)}
        onMouseEnter={() => setHoveredItem(item.id)}
        onMouseLeave={() => setHoveredItem(null)}
        className={`flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 group ${
          level > 0 && orientation === 'vertical' ? 'ml-8' : ''
        } ${
          item.path && isActive(item.path)
            ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
            : item.disabled
            ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed pointer-events-none'
            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700'
        } ${orientation === 'horizontal' ? 'flex-col text-center px-2' : ''}`}
        title={compact ? item.label : item.description}
      >
        <div className={`flex items-center ${orientation === 'horizontal' ? 'flex-col' : 'space-x-3'}`}>
          <item.icon className={`w-5 h-5 ${
            item.path && isActive(item.path)
              ? 'text-primary-600 dark:text-primary-400' 
              : item.disabled
              ? 'text-gray-400 dark:text-gray-600'
              : 'text-gray-400 dark:text-gray-500 group-hover:text-gray-600 dark:group-hover:text-gray-300'
          } ${orientation === 'horizontal' ? 'mb-1' : ''}`} />
          
          {(showLabels && !compact) && (
            <span className={orientation === 'horizontal' ? 'text-xs' : ''}>{item.label}</span>
          )}
          
          {(showLabels && !compact) && item.badge && (
            <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
              typeof item.badge === 'number'
                ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
                : 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
            }`}>
              {item.badge}
            </span>
          )}
          
          {(showLabels && !compact) && item.isNew && (
            <span className="px-2 py-0.5 text-xs font-medium bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full">
              New
            </span>
          )}
        </div>

        {/* Tooltip for compact mode */}
        {compact && hoveredItem === item.id && (
          <div className="absolute left-full ml-2 px-2 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-xs rounded shadow-lg whitespace-nowrap z-50">
            {item.label}
            {item.description && (
              <div className="text-gray-300 dark:text-gray-600 mt-1">
                {item.description}
              </div>
            )}
          </div>
        )}
      </Link>
    )
  }

  const containerClasses = `
    ${className}
    ${orientation === 'vertical' ? 'space-y-2' : 'flex space-x-2'}
    ${compact ? 'py-2' : 'py-4'}
  `.trim()

  return (
    <nav className={containerClasses} role="navigation" aria-label="Main navigation">
      {/* System Status Indicator (for compact vertical mode) */}
      {compact && orientation === 'vertical' && (
        <div className="px-3 py-2 border-b border-gray-200 dark:border-dark-700">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-xs text-gray-500 dark:text-gray-400">Online</span>
          </div>
        </div>
      )}

      {/* Navigation Items */}
      <div className={orientation === 'vertical' ? 'space-y-1' : 'flex space-x-1'}>
        {navigationItems.map(item => renderNavigationItem(item))}
      </div>

      {/* Quick Stats (for compact vertical mode) */}
      {compact && orientation === 'vertical' && (
        <div className="px-3 py-2 border-t border-gray-200 dark:border-dark-700 mt-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500 dark:text-gray-400">Active</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">3</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500 dark:text-gray-400">Today</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">1.2k</span>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}

export default Navigation