import React, { useState, useRef, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { 
  Menu, 
  Bell, 
  User, 
  Settings, 
  LogOut,
  ChevronDown,
  X,
  Clock,
  AlertCircle,
  CheckCircle,
  Info,
  Search
} from 'lucide-react'

// Import layout components
import ThemeToggle from './ThemeToggle'
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import api from '../../services/axios';

interface HeaderProps {
  onMenuClick: () => void
}

interface Notification {
  id: number
  title: string
  message: string
  time: string
  type: 'warning' | 'success' | 'info' | 'error'
  unread: boolean
  actionable?: boolean
  href?: string
  category?: string
  priority?: 'low' | 'medium' | 'high'
  sender?: string
  senderId?: number
  isMessage?: boolean
  canReply?: boolean
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const { logout, user } = useAuth();
  const { currentTheme, effectiveTheme, isDark } = useTheme();
  
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loadingNotifications, setLoadingNotifications] = useState(false)
  const [notificationsError, setNotificationsError] = useState<string | null>(null)
  
  const location = useLocation()
  const navigate = useNavigate()
  const notificationRef = useRef<HTMLDivElement>(null)
  const userMenuRef = useRef<HTMLDivElement>(null)

  // Fetch notifications from backend
  const fetchNotifications = async () => {
    try {
      setLoadingNotifications(true);
      setNotificationsError(null);
      
      const response = await api.get('/auth/notifications/');
      console.log('Header: Notifications fetched successfully:', response.data);
      
      // Transform backend data to frontend format
      const transformedNotifications: Notification[] = response.data.map((notification: any) => ({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        time: formatTimeAgo(new Date(notification.created_at)),
        type: mapNotificationType(notification.notification_type),
        unread: !notification.is_read,
        actionable: notification.is_message,
        href: notification.is_message ? '/messages' : undefined,
        category: notification.notification_type === 'message' ? 'Messages' : 
                 notification.notification_type === 'broadcast' ? 'Broadcast' : 
                 notification.notification_type === 'alert' ? 'Alerts' : 'System',
        priority: notification.priority,
        sender: notification.sender ? `${notification.sender.first_name} ${notification.sender.last_name}` : undefined,
        senderId: notification.sender ? notification.sender.id : undefined,
        isMessage: notification.is_message,
        canReply: notification.can_reply,
      }));
      
      setNotifications(transformedNotifications);
    } catch (error: any) {
      console.error('Header: Error fetching notifications:', error);
      setNotificationsError('Failed to load notifications');
    } finally {
      setLoadingNotifications(false);
    }
  };

  // Helper function to format time ago
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Helper function to map notification types
  const mapNotificationType = (type: string): 'warning' | 'success' | 'info' | 'error' => {
    switch (type) {
      case 'alert': return 'error';
      case 'broadcast': return 'warning';
      case 'message': return 'info';
      default: return 'info';
    }
  };

  // Fetch notifications when component mounts
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Refresh notifications when dropdown opens
  useEffect(() => {
    if (notificationsOpen) {
      fetchNotifications();
    }
  }, [notificationsOpen]);

  const unreadCount = notifications.filter(n => n.unread).length

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false)
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Get page title based on current route and role
  const getPageTitle = () => {
    const userRole = user?.role?.name || 'analyst';
    const path = location.pathname;
    
    // Handle account page for all roles
    if (path.includes('/account')) {
      return 'Account Settings';
    }
    
    // Handle settings page for all roles
    if (path.includes('/settings')) {
      return 'Settings';
    }
    
    // Handle AI Insight page for all roles
    if (path.includes('/ai_insight')) {
      return 'AI Insight';
    }
    
    // Role-specific page titles
    switch (userRole) {
      case 'analyst':
        if (path.includes('/donnee')) return 'Données';
        if (path.includes('/analyse')) return 'Analyse';
        if (path.includes('/publie')) return 'Publie';
        return 'Analyst Interface';
        
      case 'decision_board':
        if (path.includes('/dashboard')) return 'Dashboard';
        if (path.includes('/kpi')) return 'KPI';
        if (path.includes('/rentability')) return 'Analyse de rentabilité';
        if (path.includes('/forecasting')) return 'Projection';
        if (path.includes('/benchmark')) return 'Benchmark';
        if (path.includes('/report')) return 'Report';
        return 'Board Decision';
        
      case 'operation_board':
        if (path.includes('/dashboard')) return 'Dashboard';
        if (path.includes('/projects')) return 'Suivi de Projets';
        if (path.includes('/stock')) return 'Gestion des Stocks';
        if (path.includes('/commercial')) return 'Performance Commerciale';
        if (path.includes('/maintenance')) return 'Maintenance Prédictive';
        if (path.includes('/report')) return 'Rapports Opérationnels';
        return 'Operations Board';
        
      default:
        return 'KAH Analytics Hub';
    }
  }

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return AlertCircle
      case 'success': return CheckCircle
      case 'error': return X
      default: return Info
    }
  }

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return 'text-yellow-600 dark:text-yellow-400'
      case 'success': return 'text-green-600 dark:text-green-400'
      case 'error': return 'text-red-600 dark:text-red-400'
      default: return 'text-blue-600 dark:text-blue-400'
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    if (notification.href) {
      navigate(notification.href)
      setNotificationsOpen(false)
    }
  }

  const markAllNotificationsRead = async () => {
    try {
      const response = await api.post('/auth/notifications/mark-all-read/');
      if (response.status === 200) {
        setNotifications(prev =>
          prev.map(notification => ({ ...notification, unread: false }))
        );
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  const handleUserLogout = async () => {
    console.log('=== LOGOUT DEBUG ===');
    console.log('1. Logout initiated from Header component');
    
    // Show confirmation dialog
    const confirmed = window.confirm('Are you sure you want to sign out?');
    if (!confirmed) {
      console.log('4. Logout cancelled by user');
      return;
    }
    
    console.log('5. Logout confirmed, starting logout process');
    setIsLoggingOut(true);
    setUserMenuOpen(false); // Close the user menu
    
    try {
      console.log('6. Calling logout function from AuthContext...');
      await logout();
      console.log('7. Logout function completed successfully');
      console.log('8. Navigating to login page...');
      // Navigate to login page after successful logout
      window.location.href = '/auth/login'; // Use window.location.href instead of navigate
      console.log('9. Navigation completed');
    } catch (error) {
      console.error('ERROR: Failed to log out:', error);
      console.log('ERROR: Still navigating to login page despite error');
      // Still navigate to login page even if logout fails
      window.location.href = '/auth/login';
    } finally {
      console.log('10. Logout process completed');
      setIsLoggingOut(false);
    }
  };

  return (
    <header className="bg-white/80 dark:bg-dark-800/80 backdrop-blur-xl shadow-lg border-b border-gray-200/50 dark:border-dark-700/50 sticky top-0 z-40">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side - Mobile menu + Title */}
        <div className="flex items-center space-x-6">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 dark:hover:from-primary-900/20 dark:hover:to-primary-800/20 transition-all duration-300 focus:ring-2 focus:ring-primary-500/20"
          >
            <Menu className="w-5 h-5" />
          </button>
          
          <div className="flex items-center space-x-4">
            {/* Page Title with enhanced styling */}
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-primary-600 to-primary-700 dark:from-white dark:via-primary-400 dark:to-primary-300 bg-clip-text text-transparent">
                {getPageTitle()}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400 hidden sm:block mt-1">
                {new Date().toLocaleDateString('en-US', { 
                  weekday: 'long', 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>

            {/* Status indicator */}
            <div className="hidden md:flex items-center space-x-2 px-3 py-1.5 bg-green-50 dark:bg-green-900/20 rounded-full border border-green-200 dark:border-green-800">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs font-medium text-green-700 dark:text-green-300">Online</span>
            </div>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-3">
          {/* Search Button */}
          <button className="p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-dark-700 dark:hover:to-dark-600 transition-all duration-300 focus:ring-2 focus:ring-primary-500/20">
            <Search className="w-5 h-5" />
          </button>

          {/* Theme Toggle with enhanced styling */}
          <div className="p-1 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 dark:from-dark-700 dark:to-dark-600">
            <ThemeToggle />
          </div>

          {/* Notifications with enhanced styling */}
          <div className="relative" ref={notificationRef}>
            <button
              onClick={(e) => {
                e.stopPropagation()
                setNotificationsOpen(!notificationsOpen)
                setUserMenuOpen(false)
              }}
              className="relative p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 dark:hover:from-primary-900/20 dark:hover:to-primary-800/20 transition-all duration-300 focus:ring-2 focus:ring-primary-500/20"
            >
              <Bell className="w-5 h-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg animate-pulse">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Enhanced Notifications Dropdown */}
            {notificationsOpen && (
              <div className="absolute right-0 top-14 w-96 bg-white/95 dark:bg-dark-800/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 dark:border-dark-700/50 z-50 animate-in slide-in-from-top-2 duration-200">
                <div className="p-6 border-b border-gray-200/50 dark:border-dark-700/50 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-t-2xl">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl">
                        <Bell className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                          Notifications
                        </h3>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {unreadCount} unread • {notifications.length} total
                        </p>
                      </div>
                    </div>
                    <button 
                      onClick={markAllNotificationsRead}
                      className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium hover:bg-primary-50 dark:hover:bg-primary-900/20 px-3 py-1 rounded-lg transition-colors"
                    >
                      Mark all read
                    </button>
                  </div>
                </div>
                
                <div className="max-h-96 overflow-y-auto">
                  {loadingNotifications ? (
                    <div className="p-8 text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Loading notifications...</p>
                    </div>
                  ) : notificationsError ? (
                    <div className="p-8 text-center">
                      <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <AlertCircle className="w-6 h-6 text-red-500" />
                      </div>
                      <p className="text-sm text-red-500 dark:text-red-400 mb-4">{notificationsError}</p>
                      <button
                        onClick={fetchNotifications}
                        className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium bg-primary-50 dark:bg-primary-900/20 px-4 py-2 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-800/20 transition-colors"
                      >
                        Retry
                      </button>
                    </div>
                  ) : notifications.length === 0 ? (
                    <div className="p-8 text-center">
                      <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Bell className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                      </div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No notifications</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">You're all caught up!</p>
                    </div>
                  ) : (
                    notifications.slice(0, 5).map((notification) => (
                      <div 
                        key={notification.id}
                        onClick={() => handleNotificationClick(notification)}
                        className={`p-4 border-b border-gray-100/50 dark:border-dark-700/50 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-dark-700 dark:hover:to-dark-600 transition-all duration-200 ${
                          notification.href ? 'cursor-pointer' : ''
                        } ${notification.unread ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''}`}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`p-2 rounded-xl ${
                            notification.type === 'warning' ? 'bg-gradient-to-r from-yellow-100 to-yellow-200 dark:from-yellow-900/20 dark:to-yellow-800/20' :
                            notification.type === 'success' ? 'bg-gradient-to-r from-green-100 to-green-200 dark:from-green-900/20 dark:to-green-800/20' :
                            notification.type === 'error' ? 'bg-gradient-to-r from-red-100 to-red-200 dark:from-red-900/20 dark:to-red-800/20' :
                            'bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900/20 dark:to-blue-800/20'
                          }`}>
                            {(() => {
                              const IconComponent = getNotificationIcon(notification.type);
                              return <IconComponent className={`w-4 h-4 ${getNotificationColor(notification.type)}`} />;
                            })()}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <p className="text-sm font-semibold text-gray-900 dark:text-white">
                                {notification.title}
                              </p>
                              {notification.unread && (
                                <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse ml-2"></div>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                  <Clock className="w-3 h-3 mr-1" />
                                  {notification.time}
                                </div>
                                {notification.actionable && (
                                  <span className="px-2 py-1 text-xs font-medium bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full">
                                    Action required
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
                
                <div className="p-4 border-t border-gray-200/50 dark:border-dark-700/50 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-b-2xl">
                  <button 
                    onClick={() => {
                      navigate('/notifications');
                      setNotificationsOpen(false);
                    }}
                    className="w-full text-center text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium hover:bg-primary-50 dark:hover:bg-primary-900/20 py-2 rounded-lg transition-colors"
                  >
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced User Menu */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={(e) => {
                e.stopPropagation()
                setUserMenuOpen(!userMenuOpen)
                setNotificationsOpen(false)
              }}
              className="flex items-center space-x-3 p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 dark:hover:from-primary-900/20 dark:hover:to-primary-800/20 transition-all duration-300 focus:ring-2 focus:ring-primary-500/20"
            >
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 rounded-full flex items-center justify-center shadow-lg">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-sm font-semibold text-gray-900 dark:text-white">
                  {user?.first_name || 'User'} {user?.last_name || ''}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {user?.role?.name || 'user'}
                </p>
              </div>
              <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${userMenuOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* Enhanced User Dropdown */}
            {userMenuOpen && (
              <div className="absolute right-0 top-14 w-64 bg-white/95 dark:bg-dark-800/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 dark:border-dark-700/50 z-50 animate-in slide-in-from-top-2 duration-200">
                <div className="p-6 border-b border-gray-200/50 dark:border-dark-700/50 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-t-2xl">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 rounded-full flex items-center justify-center shadow-lg">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-bold text-gray-900 dark:text-white">
                        {user?.first_name} {user?.last_name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email}</p>
                      <div className="mt-2">
                        <span className="px-2 py-1 text-xs font-medium bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full capitalize">
                          {user?.role?.name || 'user'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="py-2">
                  <button 
                    onClick={() => navigate('/account')}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-dark-700 dark:hover:to-dark-600 transition-all duration-200 text-left rounded-xl mx-2"
                  >
                    <User className="w-4 h-4" />
                    <span>Account</span>
                  </button>
                  <button 
                    onClick={() => navigate('/settings')}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-dark-700 dark:hover:to-dark-600 transition-all duration-200 text-left rounded-xl mx-2"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Settings</span>
                  </button>
                </div>
                
                <div className="border-t border-gray-200/50 dark:border-dark-700/50 py-2">
                  <button 
                    onClick={handleUserLogout}
                    disabled={isLoggingOut}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 dark:hover:from-red-900/20 dark:hover:to-red-800/20 transition-all duration-200 text-left rounded-xl mx-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>{isLoggingOut ? 'Signing out...' : 'Sign out'}</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header