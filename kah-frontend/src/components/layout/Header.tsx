import React, { useState, useRef, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { 
  Menu, 
  Bell, 
  User, 
  Settings, 
  LogOut,
  ChevronDown,
  X,
  Clock,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'

// Import layout components
import ThemeToggle from './ThemeToggle'
import { useAuth } from '../../context/AuthContext';

interface HeaderProps {
  onMenuClick: () => void
}

interface Notification {
  id: number
  title: string
  message: string
  time: string
  type: 'warning' | 'success' | 'info' | 'error'
  unread: boolean
  actionable?: boolean
  href?: string
}

const Header: React.FC<HeaderProps> = ({ onMenuClick }) => {
  const { logout, user } = useAuth();
  
  const [notificationsOpen, setNotificationsOpen] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  
  const location = useLocation()
  const navigate = useNavigate()
  const notificationRef = useRef<HTMLDivElement>(null)
  const userMenuRef = useRef<HTMLDivElement>(null)

  // Mock notifications data
  const notifications: Notification[] = [
    {
      id: 1,
      title: 'High traffic detected',
      message: 'Entrance zone exceeding threshold (15+ people)',
      time: '2 min ago',
      type: 'warning',
      unread: true,
      actionable: true,
      href: '/live'
    },
    {
      id: 2,
      title: 'Detection session completed',
      message: 'Main camera session saved successfully',
      time: '15 min ago',
      type: 'success',
      unread: true,
      href: '/historical/sessions'
    },
    {
      id: 3,
      title: 'System update available',
      message: 'Version 2.1.0 is ready for installation',
      time: '1 hour ago',
      type: 'info',
      unread: false,
      actionable: true
    },
    {
      id: 4,
      title: 'Camera connection restored',
      message: 'Main entrance camera is back online',
      time: '2 hours ago',
      type: 'success',
      unread: false
    },
    {
      id: 5,
      title: 'Export completed',
      message: 'Weekly detection report has been generated',
      time: '3 hours ago',
      type: 'info',
      unread: false,
      href: '/historical/export'
    }
  ]

  const unreadCount = notifications.filter(n => n.unread).length

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setNotificationsOpen(false)
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Get page title based on current route and role
  const getPageTitle = () => {
    const userRole = user?.role?.name || 'analyst';
    const path = location.pathname;
    
    // Role-specific page titles
    switch (userRole) {
      case 'analyst':
        if (path.includes('/donnee')) return 'Données';
        if (path.includes('/analyse')) return 'Analyse';
        if (path.includes('/publie')) return 'Publie';
        return 'Analyst Interface';
        
      case 'decision_board':
        if (path.includes('/dashboard')) return 'Dashboard';
        if (path.includes('/report')) return 'Report';
        return 'Board Decision';
        
      case 'operation_board':
        if (path.includes('/dashboard')) return 'Dashboard';
        if (path.includes('/report')) return 'Report';
        return 'Operations Board';
        
      default:
        return 'KAH Analytics Hub';
    }
  }

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return AlertCircle
      case 'success': return CheckCircle
      case 'error': return X
      default: return Info
    }
  }

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return 'text-yellow-600 dark:text-yellow-400'
      case 'success': return 'text-green-600 dark:text-green-400'
      case 'error': return 'text-red-600 dark:text-red-400'
      default: return 'text-blue-600 dark:text-blue-400'
    }
  }

  const handleNotificationClick = (notification: Notification) => {
    if (notification.href) {
      navigate(notification.href)
      setNotificationsOpen(false)
    }
  }

  const markAllNotificationsRead = () => {
    // In a real app, this would make an API call
    console.log('Marking all notifications as read')
  }

  const handleUserLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Failed to log out:', error);
      // Optionally show a notification to the user
    }
  };

  return (
    <header className="bg-white dark:bg-dark-800 shadow-lg border-b border-gray-200 dark:border-dark-700 sticky top-0 z-40">
      <div className="flex items-center justify-between px-6 py-5">
        {/* Left side - Mobile menu + Title */}
        <div className="flex items-center space-x-6">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200 focus-ring"
            aria-label="Open navigation menu"
          >
            <Menu className="w-5 h-5" />
          </button>
          
          <div className="hidden sm:block">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent">
              {getPageTitle()}
            </h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>

          {/* Mobile title */}
          <div className="sm:hidden">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              {getPageTitle()}
            </h1>
          </div>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center space-x-3">
          {/* Theme Toggle */}
          <div className="bg-gray-100 dark:bg-dark-700 rounded-xl p-2">
            <ThemeToggle />
          </div>

          {/* Notifications */}
          <div className="relative" ref={notificationRef}>
            <button
              onClick={() => {
                setNotificationsOpen(!notificationsOpen)
                setUserMenuOpen(false)
              }}
              className="relative p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200 focus-ring"
              aria-label="View notifications"
            >
              <Bell className="w-5 h-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Notifications Dropdown */}
            {notificationsOpen && (
              <div className="absolute right-0 top-14 w-80 sm:w-96 bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-gray-200 dark:border-dark-700 z-50">
                <div className="p-5 border-b border-gray-200 dark:border-dark-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-t-xl">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                      Notifications
                    </h3>
                    <button 
                      onClick={markAllNotificationsRead}
                      className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium"
                    >
                      Mark all read
                    </button>
                  </div>
                </div>
                
                <div className="max-h-80 overflow-y-auto">
                  {notifications.map((notification) => {
                    const IconComponent = getNotificationIcon(notification.type)
                    return (
                      <div 
                        key={notification.id}
                        onClick={() => handleNotificationClick(notification)}
                        className={`p-4 border-b border-gray-100 dark:border-dark-700 hover:bg-gray-50 dark:hover:bg-dark-700 transition-all duration-200 ${
                          notification.href ? 'cursor-pointer' : ''
                        } ${notification.unread ? 'bg-blue-50 dark:bg-blue-900/10' : ''}`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`p-2 rounded-lg ${
                            notification.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                            notification.type === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                            notification.type === 'error' ? 'bg-red-100 dark:bg-red-900/20' :
                            'bg-blue-100 dark:bg-blue-900/20'
                          }`}>
                            <IconComponent className={`w-4 h-4 ${getNotificationColor(notification.type)}`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-semibold text-gray-900 dark:text-white">
                              {notification.title}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                              {notification.message}
                            </p>
                            <div className="flex items-center justify-between mt-3">
                              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                <Clock className="w-3 h-3 mr-1" />
                                {notification.time}
                              </div>
                              {notification.actionable && (
                                <span className="text-xs text-primary-600 dark:text-primary-400 font-medium">
                                  Action required
                                </span>
                              )}
                            </div>
                          </div>
                          {notification.unread && (
                            <div className="w-3 h-3 bg-blue-500 rounded-full mt-2 animate-pulse" />
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
                
                <div className="p-4 border-t border-gray-200 dark:border-dark-700 bg-gray-50 dark:bg-dark-700/50 rounded-b-xl">
                  <button className="w-full text-center text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* User Menu */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => {
                setUserMenuOpen(!userMenuOpen)
                setNotificationsOpen(false)
              }}
              className="flex items-center space-x-3 p-3 rounded-xl text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200 focus-ring"
              aria-label="User menu"
            >
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center shadow-lg">
                <User className="w-5 h-5 text-white" />
              </div>
              <span className="hidden sm:block text-sm font-semibold text-gray-900 dark:text-white">
                {user?.first_name || 'User'} {user?.last_name || ''}
              </span>
              <ChevronDown className="w-4 h-4" />
            </button>

            {/* User Dropdown */}
            {userMenuOpen && (
              <div className="absolute right-0 top-14 w-56 bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-gray-200 dark:border-dark-700 z-50">
                <div className="p-5 border-b border-gray-200 dark:border-dark-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-t-xl">
                  <p className="text-sm font-bold text-gray-900 dark:text-white">
                    {user?.first_name} {user?.last_name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{user?.email}</p>
                  <p className="text-xs text-primary-600 dark:text-primary-400 capitalize font-medium mt-2">
                    {user?.role?.name || 'user'}
                  </p>
                </div>
                
                <div className="py-2">
                  <button 
                    onClick={() => navigate('/account')}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200 text-left rounded-lg mx-2"
                  >
                    <User className="w-4 h-4" />
                    <span>Profile</span>
                  </button>
                  <button 
                    onClick={() => navigate('/configuration')}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-700 transition-all duration-200 text-left rounded-lg mx-2"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Settings</span>
                  </button>
                </div>
                
                <div className="border-t border-gray-200 dark:border-dark-700 py-2">
                  <button 
                    onClick={handleUserLogout}
                    className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 text-left rounded-lg mx-2"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Sign out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header