import React from 'react';
import { ArrowUp, ArrowDown, Minus } from 'lucide-react';

interface TrendData {
  direction: 'up' | 'down' | 'neutral';
  percentage: number;
  label?: string;
}

interface MetricCardProps {
  title: string;
  value: number | string;
  trend?: TrendData;
  icon: React.ComponentType<{ className?: string }>;
  variant?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'detection';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  className?: string;
  onClick?: () => void;
  subtitle?: string;
  formatValue?: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  trend,
  icon: Icon,
  variant = 'primary',
  size = 'md',
  loading = false,
  className = '',
  onClick,
  subtitle,
  formatValue = true
}) => {
  // Variant styles for icon background
  const iconVariants = {
    primary: 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400',
    success: 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400',
    warning: 'bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400',
    danger: 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400',
    info: 'bg-cyan-100 dark:bg-cyan-900/20 text-cyan-600 dark:text-cyan-400',
    detection: 'bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400'
  };

  // Size styles
  const sizeStyles = {
    sm: {
      container: 'p-4',
      icon: 'p-2 w-8 h-8',
      iconSize: 'w-4 h-4',
      title: 'text-xs',
      value: 'text-lg',
      trend: 'text-xs'
    },
    md: {
      container: 'p-6',
      icon: 'p-3 w-12 h-12',
      iconSize: 'w-6 h-6',
      title: 'text-sm',
      value: 'text-2xl',
      trend: 'text-sm'
    },
    lg: {
      container: 'p-8',
      icon: 'p-4 w-16 h-16',
      iconSize: 'w-8 h-8',
      title: 'text-base',
      value: 'text-3xl',
      trend: 'text-base'
    }
  };

  // Trend styles
  const getTrendStyles = (direction: TrendData['direction']) => {
    switch (direction) {
      case 'up':
        return {
          icon: ArrowUp,
          color: 'text-green-600 dark:text-green-400',
          bgColor: 'text-green-600 dark:text-green-400'
        };
      case 'down':
        return {
          icon: ArrowDown,
          color: 'text-red-600 dark:text-red-400',
          bgColor: 'text-red-600 dark:text-red-400'
        };
      case 'neutral':
        return {
          icon: Minus,
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'text-gray-600 dark:text-gray-400'
        };
      default:
        return {
          icon: Minus,
          color: 'text-gray-600 dark:text-gray-400',
          bgColor: 'text-gray-600 dark:text-gray-400'
        };
    }
  };

  // Format value if needed
  const formatDisplayValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    if (!formatValue) return val.toString();
    
    // Format large numbers with locale-specific separators
    if (val >= 1000) {
      return val.toLocaleString();
    }
    
    return val.toString();
  };

  // Loading skeleton
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${sizeStyles[size].container} ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-3"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            </div>
            <div className={`${sizeStyles[size].icon} bg-gray-200 dark:bg-gray-700 rounded-lg`}></div>
          </div>
        </div>
      </div>
    );
  }

  const currentSize = sizeStyles[size];
  const iconVariant = iconVariants[variant];

  return (
    <div 
      className={`
        bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 
        ${currentSize.container} 
        ${onClick ? 'cursor-pointer hover:shadow-md hover:scale-[1.02] transition-all duration-200' : ''} 
        ${className}
      `}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      } : undefined}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          {/* Title */}
          <p className={`${currentSize.title} font-medium text-gray-600 dark:text-gray-400 truncate`}>
            {title}
          </p>
          
          {/* Value */}
          <p className={`${currentSize.value} font-bold text-gray-900 dark:text-white mt-1 truncate`}>
            {formatDisplayValue(value)}
          </p>
          
          {/* Subtitle */}
          {subtitle && (
            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1 truncate">
              {subtitle}
            </p>
          )}
          
          {/* Trend */}
          {trend && (
            <div className="flex items-center mt-2">
              {(() => {
                const trendStyles = getTrendStyles(trend.direction);
                const TrendIcon = trendStyles.icon;
                return (
                  <>
                    <TrendIcon className={`w-4 h-4 ${trendStyles.color}`} />
                    <span className={`${currentSize.trend} ${trendStyles.color} ml-1 font-medium`}>
                      {trend.direction !== 'neutral' && (trend.percentage > 0 ? '+' : '')}{trend.percentage}%
                    </span>
                    {trend.label && (
                      <span className={`${currentSize.trend} text-gray-500 dark:text-gray-400 ml-1`}>
                        {trend.label}
                      </span>
                    )}
                  </>
                );
              })()}
            </div>
          )}
        </div>
        
        {/* Icon */}
        <div className={`${currentSize.icon} ${iconVariant} rounded-lg flex items-center justify-center flex-shrink-0 ml-4`}>
          <Icon className={currentSize.iconSize} />
        </div>
      </div>
    </div>
  );
};

export default MetricCard;

// Export types
export type { TrendData, MetricCardProps };