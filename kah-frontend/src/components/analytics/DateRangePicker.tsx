import React, { useState, useRef, useEffect } from 'react';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  X,
  Check,
  RotateCcw,
  CalendarDays,
  ArrowRight,
  Zap,
  History,
  TrendingUp,
  BarChart3
} from 'lucide-react';

export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface DateRangePreset {
  id: string;
  label: string;
  range: DateRange;
  icon?: React.ComponentType<any>;
  description?: string;
  isRelative?: boolean;
  getRange?: () => DateRange;
}

interface DateRangePickerProps {
  value?: DateRange;
  onChange?: (range: DateRange) => void;
  onApply?: (range: DateRange) => void;
  onCancel?: () => void;
  placeholder?: string;
  format?: string;
  disabled?: boolean;
  required?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'outlined' | 'filled';
  showPresets?: boolean;
  showTime?: boolean;
  showApplyButton?: boolean;
  autoApply?: boolean;
  allowSingleDate?: boolean;
  maxRange?: number; // days
  minDate?: Date;
  maxDate?: Date;
  className?: string;
  presets?: DateRangePreset[];
  customPresets?: DateRangePreset[];
  showRelativePresets?: boolean;
  showCustomInput?: boolean;
  error?: string;
  helperText?: string;
  label?: string;
  clearable?: boolean;
  position?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
  showWeekNumbers?: boolean;
  firstDayOfWeek?: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday
  highlightToday?: boolean;
  showQuickActions?: boolean;
  locale?: string;
  timezone?: string;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value = { start: null, end: null },
  onChange,
  onApply,
  onCancel,
  placeholder = 'Select date range',
  format = 'MMM dd, yyyy',
  disabled = false,
  required = false,
  size = 'md',
  variant = 'default',
  showPresets = true,
  showTime = false,
  showApplyButton = true,
  autoApply = false,
  allowSingleDate = true,
  maxRange,
  minDate,
  maxDate,
  className = '',
  presets,
  customPresets = [],
  showRelativePresets = true,
  showCustomInput = false,
  error,
  helperText,
  label,
  clearable = true,
  position = 'bottom-start',
  showWeekNumbers = false,
  firstDayOfWeek = 0,
  highlightToday = true,
  showQuickActions = true,
  locale = 'en-US',
  timezone = 'UTC'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localRange, setLocalRange] = useState<DateRange>(value);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectionMode, setSelectionMode] = useState<'start' | 'end' | null>(null);
  const [hoverDate, setHoverDate] = useState<Date | null>(null);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [customStartInput, setCustomStartInput] = useState('');
  const [customEndInput, setCustomEndInput] = useState('');

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Default presets
  const defaultPresets: DateRangePreset[] = [
    {
      id: 'today',
      label: 'Today',
      icon: Calendar,
      isRelative: true,
      getRange: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const end = new Date(today);
        end.setHours(23, 59, 59, 999);
        return { start: today, end };
      }
    },
    {
      id: 'yesterday',
      label: 'Yesterday',
      icon: History,
      isRelative: true,
      getRange: () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        yesterday.setHours(0, 0, 0, 0);
        const end = new Date(yesterday);
        end.setHours(23, 59, 59, 999);
        return { start: yesterday, end };
      }
    },
    {
      id: 'last7days',
      label: 'Last 7 Days',
      icon: BarChart3,
      isRelative: true,
      getRange: () => {
        const end = new Date();
        end.setHours(23, 59, 59, 999);
        const start = new Date();
        start.setDate(start.getDate() - 6);
        start.setHours(0, 0, 0, 0);
        return { start, end };
      }
    },
    {
      id: 'last30days',
      label: 'Last 30 Days',
      icon: TrendingUp,
      isRelative: true,
      getRange: () => {
        const end = new Date();
        end.setHours(23, 59, 59, 999);
        const start = new Date();
        start.setDate(start.getDate() - 29);
        start.setHours(0, 0, 0, 0);
        return { start, end };
      }
    },
    {
      id: 'thisMonth',
      label: 'This Month',
      icon: CalendarDays,
      isRelative: true,
      getRange: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth(), 1);
        const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        end.setHours(23, 59, 59, 999);
        return { start, end };
      }
    },
    {
      id: 'lastMonth',
      label: 'Last Month',
      icon: CalendarDays,
      isRelative: true,
      getRange: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const end = new Date(now.getFullYear(), now.getMonth(), 0);
        end.setHours(23, 59, 59, 999);
        return { start, end };
      }
    }
  ];

  const allPresets = presets || [...defaultPresets, ...customPresets];

  // Sync with external value changes
  useEffect(() => {
    setLocalRange(value);
  }, [value]);

  // Close on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Format date for display
  const formatDate = (date: Date | null): string => {
    if (!date) return '';
    
    try {
      return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        ...(showTime && {
          hour: '2-digit',
          minute: '2-digit'
        })
      }).format(date);
    } catch (error) {
      return date.toLocaleDateString();
    }
  };

  // Get display text for input
  const getDisplayText = (): string => {
    if (!localRange.start && !localRange.end) {
      return '';
    }
    
    if (localRange.start && localRange.end) {
      return `${formatDate(localRange.start)} - ${formatDate(localRange.end)}`;
    }
    
    if (localRange.start) {
      return allowSingleDate ? formatDate(localRange.start) : `${formatDate(localRange.start)} - ...`;
    }
    
    return '';
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    if (disabled) return;

    // Check date constraints
    if (minDate && date < minDate) return;
    if (maxDate && date > maxDate) return;

    let newRange: DateRange = { ...localRange };

    if (!localRange.start || (localRange.start && localRange.end)) {
      // Starting new selection
      newRange = { start: date, end: null };
      setSelectionMode('end');
    } else if (localRange.start && !localRange.end) {
      // Completing selection
      if (date < localRange.start) {
        newRange = { start: date, end: localRange.start };
      } else {
        // Check max range constraint
        if (maxRange) {
          const daysDiff = Math.ceil((date.getTime() - localRange.start.getTime()) / (1000 * 60 * 60 * 24));
          if (daysDiff > maxRange) {
            return;
          }
        }
        newRange = { start: localRange.start, end: date };
      }
      setSelectionMode(null);
    }

    setLocalRange(newRange);
    setSelectedPreset(null);
    onChange?.(newRange);

    if (autoApply && newRange.start && (newRange.end || allowSingleDate)) {
      onApply?.(newRange);
      setIsOpen(false);
    }
  };

  // Handle preset selection
  const handlePresetSelect = (preset: DateRangePreset) => {
    const range = preset.isRelative && preset.getRange ? preset.getRange() : preset.range;
    setLocalRange(range);
    setSelectedPreset(preset.id);
    setSelectionMode(null);
    onChange?.(range);

    if (autoApply) {
      onApply?.(range);
      setIsOpen(false);
    }
  };

  // Handle apply
  const handleApply = () => {
    onApply?.(localRange);
    setIsOpen(false);
  };

  // Handle cancel
  const handleCancel = () => {
    setLocalRange(value);
    setSelectedPreset(null);
    setSelectionMode(null);
    onCancel?.();
    setIsOpen(false);
  };

  // Handle close
  const handleClose = () => {
    if (!showApplyButton && onChange) {
      onChange(localRange);
    }
    setIsOpen(false);
  };

  // Handle clear
  const handleClear = () => {
    const emptyRange = { start: null, end: null };
    setLocalRange(emptyRange);
    setSelectedPreset(null);
    onChange?.(emptyRange);
    if (autoApply) {
      onApply?.(emptyRange);
    }
  };

  // Generate calendar days
  const generateCalendarDays = (month: Date) => {
    const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
    const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    const startOfCalendar = new Date(startOfMonth);
    
    // Adjust for first day of week
    startOfCalendar.setDate(startOfCalendar.getDate() - ((startOfCalendar.getDay() - firstDayOfWeek + 7) % 7));
    
    const days: Date[] = [];
    const current = new Date(startOfCalendar);
    
    // Generate 6 weeks of days
    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    
    return days;
  };

  // Check if date is in range
  const isDateInRange = (date: Date): boolean => {
    if (!localRange.start) return false;
    
    if (localRange.end) {
      return date >= localRange.start && date <= localRange.end;
    }
    
    if (hoverDate && selectionMode === 'end') {
      const rangeStart = localRange.start < hoverDate ? localRange.start : hoverDate;
      const rangeEnd = localRange.start < hoverDate ? hoverDate : localRange.start;
      return date >= rangeStart && date <= rangeEnd;
    }
    
    return date.toDateString() === localRange.start.toDateString();
  };

  // Check if date is range endpoint
  const isDateEndpoint = (date: Date): boolean => {
    return (
      (localRange.start && date.toDateString() === localRange.start.toDateString()) ||
      (localRange.end && date.toDateString() === localRange.end.toDateString())
    );
  };

  // Get size classes
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  // Get variant classes
  const variantClasses = {
    default: 'border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800',
    minimal: 'border-0 bg-transparent',
    outlined: 'border-2 border-gray-300 dark:border-gray-600 bg-transparent',
    filled: 'border-0 bg-gray-100 dark:bg-gray-700'
  };

  const calendarDays = generateCalendarDays(currentMonth);
  const today = new Date();

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Input */}
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={getDisplayText()}
          placeholder={placeholder}
          readOnly
          disabled={disabled}
          onClick={() => !disabled && setIsOpen(true)}
          className={`
            w-full rounded-lg transition-colors cursor-pointer
            ${sizeClasses[size]}
            ${variantClasses[variant]}
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500'}
            ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'focus:border-primary-500 focus:ring-primary-500'}
            ${isOpen ? 'ring-2 ring-primary-500 border-primary-500' : ''}
            text-gray-900 dark:text-white
          `}
        />
        
        {/* Icons */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-1">
          {clearable && getDisplayText() && !disabled && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleClear();
              }}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            >
              <X className="w-4 h-4 text-gray-400" />
            </button>
          )}
          <Calendar className="w-4 h-4 text-gray-400" />
        </div>
      </div>

      {/* Error/Helper Text */}
      {(error || helperText) && (
        <p className={`mt-1 text-xs ${error ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'}`}>
          {error || helperText}
        </p>
      )}

      {/* Dropdown */}
      {isOpen && (
        <div className={`
          absolute z-50 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 
          rounded-xl shadow-lg min-w-full
          ${position.includes('top') ? 'bottom-full mb-2' : 'top-full'}
          ${position.includes('end') ? 'right-0' : 'left-0'}
        `}>
          <div className="flex">
            {/* Presets Panel */}
            {showPresets && allPresets.length > 0 && (
              <div className="w-64 border-r border-gray-200 dark:border-gray-700 p-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  Quick Select
                </h4>
                
                <div className="space-y-1">
                  {allPresets.map((preset) => {
                    const Icon = preset.icon || Calendar;
                    const isSelected = selectedPreset === preset.id;
                    
                    return (
                      <button
                        key={preset.id}
                        onClick={() => handlePresetSelect(preset)}
                        className={`
                          w-full flex items-center px-3 py-2 text-sm rounded-lg transition-colors
                          ${isSelected 
                            ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300' 
                            : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                          }
                        `}
                      >
                        <Icon className="w-4 h-4 mr-3 flex-shrink-0" />
                        <div className="text-left">
                          <div className="font-medium">{preset.label}</div>
                          {preset.description && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {preset.description}
                            </div>
                          )}
                        </div>
                      </button>
                    );
                  })}
                </div>

                {/* Quick Actions */}
                {showQuickActions && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <h5 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                      Quick Actions
                    </h5>
                    <div className="flex gap-1">
                      <button
                        onClick={handleClear}
                        className="flex-1 flex items-center justify-center px-2 py-1.5 text-xs text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                      >
                        <RotateCcw className="w-3 h-3 mr-1" />
                        Clear
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Calendar Panel */}
            <div className="p-4">
              {/* Calendar Header */}
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={() => setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() - 1)))}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {currentMonth.toLocaleDateString(locale, { month: 'long', year: 'numeric' })}
                </h3>
                
                <button
                  onClick={() => setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() + 1)))}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>

              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {/* Day headers */}
                {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day, index) => (
                  <div
                    key={day}
                    className="h-8 flex items-center justify-center text-xs font-medium text-gray-500 dark:text-gray-400"
                  >
                    {day}
                  </div>
                ))}
                
                {/* Calendar days */}
                {calendarDays.map((date, index) => {
                  const isCurrentMonth = date.getMonth() === currentMonth.getMonth();
                  const isToday = highlightToday && date.toDateString() === today.toDateString();
                  const isInRange = isDateInRange(date);
                  const isEndpoint = isDateEndpoint(date);
                  const isDisabled = 
                    (minDate && date < minDate) || 
                    (maxDate && date > maxDate);

                  return (
                    <button
                      key={index}
                      onClick={() => !isDisabled && handleDateSelect(date)}
                      onMouseEnter={() => setHoverDate(date)}
                      onMouseLeave={() => setHoverDate(null)}
                      disabled={isDisabled}
                      className={`
                        h-8 w-8 flex items-center justify-center text-sm rounded-lg transition-colors
                        ${!isCurrentMonth ? 'text-gray-400 dark:text-gray-600' : 'text-gray-900 dark:text-white'}
                        ${isToday ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 font-semibold' : ''}
                        ${isInRange && !isEndpoint ? 'bg-primary-50 dark:bg-primary-900/10' : ''}
                        ${isEndpoint ? 'bg-primary-500 text-white font-semibold' : ''}
                        ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}
                        ${!isInRange && !isToday && !isDisabled ? 'hover:bg-gray-100 dark:hover:bg-gray-700' : ''}
                      `}
                    >
                      {date.getDate()}
                    </button>
                  );
                })}
              </div>

              {/* Custom Input */}
              {showCustomInput && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex gap-2">
                    <input
                      type="date"
                      value={customStartInput}
                      onChange={(e) => setCustomStartInput(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                      placeholder="Start date"
                    />
                    <input
                      type="date"
                      value={customEndInput}
                      onChange={(e) => setCustomEndInput(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                      placeholder="End date"
                    />
                  </div>
                </div>
              )}

              {/* Footer Actions */}
              {(showApplyButton || !autoApply) && (
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {localRange.start && localRange.end && (
                      <>
                        {Math.ceil((localRange.end.getTime() - localRange.start.getTime()) / (1000 * 60 * 60 * 24)) + 1} days
                      </>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={handleCancel}
                      className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleApply}
                      disabled={!localRange.start || (!allowSingleDate && !localRange.end)}
                      className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;
export type { DateRange, DateRangePreset, DateRangePickerProps };