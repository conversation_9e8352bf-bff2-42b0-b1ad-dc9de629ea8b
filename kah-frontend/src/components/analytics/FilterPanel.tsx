import React, { useState, useRef, useEffect } from 'react';
import {
  Filter,
  Calendar,
  MapPin,
  Users,
  Car,
  Tag,
  Clock,
  Search,
  X,
  ChevronDown,
  RefreshCw,
  Download,
  Settings,
  Eye,
  EyeOff,
  Plus,
  Minus,
  Check,
  AlertCircle,
  RotateCcw
} from 'lucide-react';

export interface FilterOption {
  id: string;
  label: string;
  value: any;
  count?: number;
  color?: string;
  icon?: React.ComponentType<any>;
  disabled?: boolean;
  description?: string;
}

export interface FilterGroup {
  id: string;
  label: string;
  type: 'select' | 'multiselect' | 'daterange' | 'search' | 'slider' | 'toggle' | 'custom';
  options?: FilterOption[];
  value?: any;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  visible?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  icon?: React.ComponentType<any>;
  min?: number;
  max?: number;
  step?: number;
  multiple?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  customComponent?: React.ComponentType<any>;
}

export interface AppliedFilter {
  groupId: string;
  groupLabel: string;
  value: any;
  displayValue: string;
  removable?: boolean;
}

export interface FilterState {
  [groupId: string]: any;
}

interface FilterPanelProps {
  filters: FilterGroup[];
  value?: FilterState;
  onFilterChange?: (filters: FilterState) => void;
  onApply?: (filters: FilterState) => void;
  onReset?: () => void;
  onExport?: () => void;
  layout?: 'horizontal' | 'vertical' | 'compact' | 'sidebar';
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'card' | 'floating';
  showAppliedFilters?: boolean;
  showFilterCount?: boolean;
  showActions?: boolean;
  showSearch?: boolean;
  autoApply?: boolean;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
  presets?: Array<{
    id: string;
    label: string;
    filters: FilterState;
    icon?: React.ComponentType<any>;
  }>;
  showPresets?: boolean;
  allowSavePreset?: boolean;
  onSavePreset?: (name: string, filters: FilterState) => void;
  validation?: {
    [groupId: string]: (value: any) => string | null;
  };
  searchPlaceholder?: string;
  emptyMessage?: string;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  filters,
  value = {},
  onFilterChange,
  onApply,
  onReset,
  onExport,
  layout = 'horizontal',
  size = 'md',
  variant = 'default',
  showAppliedFilters = true,
  showFilterCount = true,
  showActions = true,
  showSearch = false,
  autoApply = false,
  collapsible = false,
  defaultCollapsed = false,
  loading = false,
  disabled = false,
  className = '',
  presets = [],
  showPresets = false,
  allowSavePreset = false,
  onSavePreset,
  validation = {},
  searchPlaceholder = 'Search filters...',
  emptyMessage = 'No filters available'
}) => {
  const [localFilters, setLocalFilters] = useState<FilterState>(value);
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showSavePresetDialog, setShowSavePresetDialog] = useState(false);
  const [presetName, setPresetName] = useState('');

  const panelRef = useRef<HTMLDivElement>(null);

  // Sync with external value changes
  useEffect(() => {
    setLocalFilters(value);
  }, [value]);

  // Handle filter value change
  const handleFilterChange = (groupId: string, newValue: any) => {
    const updatedFilters = { ...localFilters, [groupId]: newValue };
    setLocalFilters(updatedFilters);

    // Validate if validation function exists
    if (validation[groupId]) {
      const error = validation[groupId](newValue);
      setErrors(prev => ({
        ...prev,
        [groupId]: error || ''
      }));
    }

    onFilterChange?.(updatedFilters);

    if (autoApply) {
      onApply?.(updatedFilters);
    }
  };

  // Apply filters
  const handleApply = () => {
    // Validate all filters before applying
    const newErrors: Record<string, string> = {};
    let hasErrors = false;

    filters.forEach(filter => {
      if (validation[filter.id]) {
        const error = validation[filter.id](localFilters[filter.id]);
        if (error) {
          newErrors[filter.id] = error;
          hasErrors = true;
        }
      }
    });

    setErrors(newErrors);

    if (!hasErrors) {
      onApply?.(localFilters);
    }
  };

  // Reset filters
  const handleReset = () => {
    const resetFilters: FilterState = {};
    filters.forEach(filter => {
      if (filter.value !== undefined) {
        resetFilters[filter.id] = filter.value;
      }
    });
    setLocalFilters(resetFilters);
    setErrors({});
    onReset?.();
    onFilterChange?.(resetFilters);
    if (autoApply) {
      onApply?.(resetFilters);
    }
  };

  // Remove specific filter
  const removeFilter = (groupId: string) => {
    const updatedFilters = { ...localFilters };
    delete updatedFilters[groupId];
    setLocalFilters(updatedFilters);
    onFilterChange?.(updatedFilters);
    if (autoApply) {
      onApply?.(updatedFilters);
    }
  };

  // Load preset
  const loadPreset = (preset: any) => {
    setLocalFilters(preset.filters);
    onFilterChange?.(preset.filters);
    if (autoApply) {
      onApply?.(preset.filters);
    }
  };

  // Save preset
  const handleSavePreset = () => {
    if (presetName.trim() && onSavePreset) {
      onSavePreset(presetName.trim(), localFilters);
      setPresetName('');
      setShowSavePresetDialog(false);
    }
  };

  // Toggle group collapse
  const toggleGroup = (groupId: string) => {
    const newCollapsed = new Set(collapsedGroups);
    if (newCollapsed.has(groupId)) {
      newCollapsed.delete(groupId);
    } else {
      newCollapsed.add(groupId);
    }
    setCollapsedGroups(newCollapsed);
  };

  // Filter visible filters based on search
  const visibleFilters = filters.filter(filter => {
    if (!filter.visible) return false;
    if (!searchTerm) return true;
    return filter.label.toLowerCase().includes(searchTerm.toLowerCase());
  });

  // Get applied filters for display
  const appliedFilters: AppliedFilter[] = visibleFilters.reduce((acc, filter) => {
    const value = localFilters[filter.id];
    if (value !== undefined && value !== null && value !== '') {
      let displayValue = '';
      
      switch (filter.type) {
        case 'select':
        case 'multiselect':
          if (Array.isArray(value)) {
            const labels = value.map(v => 
              filter.options?.find(opt => opt.value === v)?.label || v
            );
            displayValue = labels.join(', ');
          } else {
            displayValue = filter.options?.find(opt => opt.value === value)?.label || value;
          }
          break;
        case 'daterange':
          if (value.start && value.end) {
            displayValue = `${value.start} - ${value.end}`;
          }
          break;
        case 'slider':
          displayValue = Array.isArray(value) ? `${value[0]} - ${value[1]}` : value.toString();
          break;
        case 'toggle':
          displayValue = value ? 'Enabled' : 'Disabled';
          break;
        default:
          displayValue = value.toString();
      }

      if (displayValue) {
        acc.push({
          groupId: filter.id,
          groupLabel: filter.label,
          value,
          displayValue,
          removable: filter.clearable !== false
        });
      }
    }
    return acc;
  }, [] as AppliedFilter[]);

  // Get size classes
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  // Get layout classes
  const getLayoutClasses = () => {
    switch (layout) {
      case 'vertical':
        return 'flex flex-col space-y-4';
      case 'compact':
        return 'flex flex-wrap gap-2';
      case 'sidebar':
        return 'flex flex-col space-y-6';
      case 'horizontal':
      default:
        return 'flex flex-wrap gap-4 items-end';
    }
  };

  // Get variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'border-0 bg-transparent p-0';
      case 'card':
        return 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 shadow-sm';
      case 'floating':
        return 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-4 shadow-lg';
      case 'default':
      default:
        return 'bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-4';
    }
  };

  // Render filter input based on type
  const renderFilterInput = (filter: FilterGroup) => {
    const value = localFilters[filter.id];
    const hasError = errors[filter.id];
    const isGroupCollapsed = collapsedGroups.has(filter.id);

    const inputClasses = `
      border rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white
      focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors
      ${hasError ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
      ${disabled || filter.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400 dark:hover:border-gray-500'}
      ${sizeClasses[size]}
    `;

    const baseInputProps = {
      disabled: disabled || filter.disabled,
      className: inputClasses
    };

    switch (filter.type) {
      case 'select':
        return (
          <select
            {...baseInputProps}
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.id, e.target.value)}
            className={`${inputClasses} px-3 py-2`}
          >
            {filter.placeholder && (
              <option value="">{filter.placeholder}</option>
            )}
            {filter.options?.map((option) => (
              <option key={option.id} value={option.value} disabled={option.disabled}>
                {option.label} {option.count !== undefined && `(${option.count})`}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <div className="space-y-2">
            {filter.searchable && (
              <input
                type="text"
                placeholder="Search options..."
                className={`${inputClasses} px-3 py-2 w-full`}
                {...baseInputProps}
              />
            )}
            <div className="max-h-32 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
              {filter.options?.map((option) => (
                <label
                  key={option.id}
                  className="flex items-center px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={Array.isArray(value) && value.includes(option.value)}
                    onChange={(e) => {
                      const currentValues = Array.isArray(value) ? value : [];
                      const newValues = e.target.checked
                        ? [...currentValues, option.value]
                        : currentValues.filter(v => v !== option.value);
                      handleFilterChange(filter.id, newValues);
                    }}
                    disabled={disabled || filter.disabled || option.disabled}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="ml-2 text-sm text-gray-900 dark:text-white">
                    {option.label}
                    {option.count !== undefined && (
                      <span className="text-gray-500 ml-1">({option.count})</span>
                    )}
                  </span>
                </label>
              ))}
            </div>
          </div>
        );

      case 'daterange':
        return (
          <div className="flex gap-2">
            <input
              type="date"
              value={value?.start || ''}
              onChange={(e) => handleFilterChange(filter.id, { ...value, start: e.target.value })}
              className={`${inputClasses} px-3 py-2 flex-1`}
              {...baseInputProps}
            />
            <span className="self-center text-gray-500">to</span>
            <input
              type="date"
              value={value?.end || ''}
              onChange={(e) => handleFilterChange(filter.id, { ...value, end: e.target.value })}
              className={`${inputClasses} px-3 py-2 flex-1`}
              {...baseInputProps}
            />
          </div>
        );

      case 'search':
        return (
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={value || ''}
              onChange={(e) => handleFilterChange(filter.id, e.target.value)}
              placeholder={filter.placeholder}
              className={`${inputClasses} pl-10 pr-4 py-2 w-full`}
              {...baseInputProps}
            />
            {value && filter.clearable && (
              <button
                onClick={() => handleFilterChange(filter.id, '')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        );

      case 'slider':
        return (
          <div className="space-y-2">
            <input
              type="range"
              min={filter.min || 0}
              max={filter.max || 100}
              step={filter.step || 1}
              value={value || filter.min || 0}
              onChange={(e) => handleFilterChange(filter.id, parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              {...baseInputProps}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{filter.min || 0}</span>
              <span className="font-medium">{value || filter.min || 0}</span>
              <span>{filter.max || 100}</span>
            </div>
          </div>
        );

      case 'toggle':
        return (
          <label className="inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => handleFilterChange(filter.id, e.target.checked)}
              disabled={disabled || filter.disabled}
              className="sr-only"
            />
            <div className={`
              relative w-11 h-6 transition-colors rounded-full
              ${value ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'}
              ${disabled || filter.disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}>
              <div className={`
                absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform
                ${value ? 'translate-x-5' : 'translate-x-0'}
              `} />
            </div>
            <span className="ml-3 text-sm text-gray-900 dark:text-white">
              {value ? 'Enabled' : 'Disabled'}
            </span>
          </label>
        );

      case 'custom':
        const CustomComponent = filter.customComponent;
        return CustomComponent ? (
          <CustomComponent
            value={value}
            onChange={(newValue: any) => handleFilterChange(filter.id, newValue)}
            disabled={disabled || filter.disabled}
            {...filter}
          />
        ) : null;

      default:
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleFilterChange(filter.id, e.target.value)}
            placeholder={filter.placeholder}
            className={`${inputClasses} px-3 py-2 w-full`}
            {...baseInputProps}
          />
        );
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className={`${getVariantClasses()} ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="flex gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Empty state
  if (visibleFilters.length === 0) {
    return (
      <div className={`${getVariantClasses()} ${className} text-center`}>
        <Filter className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-600 dark:text-gray-400">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div ref={panelRef} className={`${getVariantClasses()} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Filter className="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Filters
            {showFilterCount && appliedFilters.length > 0 && (
              <span className="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-xs font-medium">
                {appliedFilters.length}
              </span>
            )}
          </h3>
        </div>

        <div className="flex items-center gap-2">
          {/* Search */}
          {showSearch && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={searchPlaceholder}
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
          )}

          {/* Collapse toggle */}
          {collapsible && (
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title={isCollapsed ? 'Expand filters' : 'Collapse filters'}
            >
              {isCollapsed ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </button>
          )}
        </div>
      </div>

      {!isCollapsed && (
        <>
          {/* Presets */}
          {showPresets && presets.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Quick Filters
                </span>
                {allowSavePreset && (
                  <button
                    onClick={() => setShowSavePresetDialog(true)}
                    className="text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400"
                  >
                    Save Current
                  </button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {presets.map((preset) => {
                  const Icon = preset.icon || Filter;
                  return (
                    <button
                      key={preset.id}
                      onClick={() => loadPreset(preset)}
                      className="flex items-center px-3 py-1.5 text-xs border border-gray-300 dark:border-gray-600 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <Icon className="w-3 h-3 mr-1" />
                      {preset.label}
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Applied Filters */}
          {showAppliedFilters && appliedFilters.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Applied Filters ({appliedFilters.length})
                </span>
                <button
                  onClick={handleReset}
                  className="text-xs text-red-600 hover:text-red-700 dark:text-red-400 flex items-center"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Clear All
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {appliedFilters.map((filter) => (
                  <div
                    key={filter.groupId}
                    className="flex items-center px-3 py-1.5 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded-full text-xs"
                  >
                    <span className="font-medium mr-1">{filter.groupLabel}:</span>
                    <span>{filter.displayValue}</span>
                    {filter.removable && (
                      <button
                        onClick={() => removeFilter(filter.groupId)}
                        className="ml-2 hover:text-primary-800 dark:hover:text-primary-100"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Filter Controls */}
          <div className={getLayoutClasses()}>
            {visibleFilters.map((filter) => {
              const Icon = filter.icon || Filter;
              const isGroupCollapsed = collapsedGroups.has(filter.id);
              const hasError = errors[filter.id];

              return (
                <div key={filter.id} className={layout === 'vertical' || layout === 'sidebar' ? 'w-full' : 'min-w-0'}>
                  {/* Filter Label */}
                  <div className="flex items-center justify-between mb-2">
                    <label className="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                      <Icon className="w-4 h-4 mr-2 text-gray-500" />
                      {filter.label}
                      {filter.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    {filter.collapsible && (
                      <button
                        onClick={() => toggleGroup(filter.id)}
                        className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                      >
                        <ChevronDown 
                          className={`w-3 h-3 transition-transform ${
                            isGroupCollapsed ? 'rotate-180' : ''
                          }`} 
                        />
                      </button>
                    )}
                  </div>

                  {/* Filter Input */}
                  {!isGroupCollapsed && (
                    <div className="space-y-1">
                      {renderFilterInput(filter)}
                      {hasError && (
                        <div className="flex items-center text-xs text-red-600 dark:text-red-400">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          {hasError}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2">
                <button
                  onClick={handleReset}
                  disabled={disabled || appliedFilters.length === 0}
                  className="flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RotateCcw className="w-4 h-4 mr-1" />
                  Reset
                </button>
              </div>

              <div className="flex items-center gap-2">
                {onExport && (
                  <button
                    onClick={onExport}
                    disabled={disabled}
                    className="flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 disabled:opacity-50"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </button>
                )}
                
                {!autoApply && (
                  <button
                    onClick={handleApply}
                    disabled={disabled || Object.keys(errors).some(key => errors[key])}
                    className="flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <Check className="w-4 h-4 mr-1" />
                    Apply Filters
                  </button>
                )}
              </div>
            </div>
          )}
        </>
      )}

      {/* Save Preset Dialog */}
      {showSavePresetDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Save Filter Preset
            </h3>
            <input
              type="text"
              value={presetName}
              onChange={(e) => setPresetName(e.target.value)}
              placeholder="Enter preset name..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              autoFocus
            />
            <div className="flex justify-end gap-2 mt-4">
              <button
                onClick={() => setShowSavePresetDialog(false)}
                className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleSavePreset}
                disabled={!presetName.trim()}
                className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterPanel;
export type { FilterGroup, FilterOption, FilterState, AppliedFilter, FilterPanelProps };