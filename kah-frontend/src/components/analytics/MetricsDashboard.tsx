import React, { useMemo } from 'react';
import MetricCard from './MetricCard';
import {
  Eye,
  Users,
  Car,
  Activity,
  TrendingUp,
  TrendingDown,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  Zap,
  BarChart3,
  Camera,
  Shield,
  Cpu,
  Wifi
} from 'lucide-react';

interface MetricData {
  key: string;
  title: string;
  value: number | string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    percentage: number;
    label?: string;
  };
  icon: React.ComponentType<any>;
  variant: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'detection';
  subtitle?: string;
  format?: 'number' | 'currency' | 'percentage' | 'duration';
  precision?: number;
  onClick?: () => void;
}

interface MetricGroup {
  title: string;
  description?: string;
  metrics: MetricData[];
  layout?: 'grid' | 'row' | 'compact';
  priority?: 'high' | 'medium' | 'low';
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

interface MetricsDashboardProps {
  data: any;
  groups?: MetricGroup[];
  layout?: 'auto' | 'grid-2' | 'grid-3' | 'grid-4' | 'responsive';
  size?: 'sm' | 'md' | 'lg';
  showTrends?: boolean;
  showComparisons?: boolean;
  loading?: boolean;
  error?: string;
  emptyMessage?: string;
  className?: string;
  onMetricClick?: (metric: MetricData) => void;
  refreshInterval?: number;
  lastUpdate?: Date;
  autoRefresh?: boolean;
  preset?: 'analytics' | 'detection' | 'system' | 'performance' | 'custom';
  comparisonPeriod?: 'hour' | 'day' | 'week' | 'month';
  showAlerts?: boolean;
  alertThresholds?: Record<string, { min?: number; max?: number; critical?: boolean }>;
}

const MetricsDashboard: React.FC<MetricsDashboardProps> = ({
  data,
  groups,
  layout = 'responsive',
  size = 'md',
  showTrends = true,
  showComparisons = false,
  loading = false,
  error,
  emptyMessage = 'No metrics available',
  className = '',
  onMetricClick,
  refreshInterval,
  lastUpdate,
  autoRefresh = false,
  preset = 'analytics',
  comparisonPeriod = 'day',
  showAlerts = true,
  alertThresholds = {}
}) => {
  const [collapsedGroups, setCollapsedGroups] = React.useState<Set<string>>(new Set());

  // Auto-refresh functionality
  React.useEffect(() => {
    if (!autoRefresh || !refreshInterval) return;

    const interval = setInterval(() => {
      // Trigger data refresh
      window.location.reload(); // Simple refresh - could be replaced with data fetching
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // Calculate metrics based on data and preset
  const calculatedMetrics = useMemo(() => {
    if (!data) return [];

    switch (preset) {
      case 'analytics':
        return calculateAnalyticsMetrics(data, comparisonPeriod, showTrends);
      case 'detection':
        return calculateDetectionMetrics(data, comparisonPeriod, showTrends);
      case 'system':
        return calculateSystemMetrics(data, comparisonPeriod, showTrends);
      case 'performance':
        return calculatePerformanceMetrics(data, comparisonPeriod, showTrends);
      default:
        return [];
    }
  }, [data, preset, comparisonPeriod, showTrends]);

  // Check for alerts
  const checkAlerts = (metric: MetricData): 'normal' | 'warning' | 'critical' => {
    if (!showAlerts || !alertThresholds[metric.key]) return 'normal';

    const threshold = alertThresholds[metric.key];
    const value = typeof metric.value === 'number' ? metric.value : parseFloat(metric.value.toString());

    if (isNaN(value)) return 'normal';

    if (threshold.min !== undefined && value < threshold.min) {
      return threshold.critical ? 'critical' : 'warning';
    }
    if (threshold.max !== undefined && value > threshold.max) {
      return threshold.critical ? 'critical' : 'warning';
    }

    return 'normal';
  };

  // Format metric value
  const formatValue = (metric: MetricData): string => {
    if (typeof metric.value === 'string') return metric.value;

    const value = metric.value;
    const precision = metric.precision || 0;

    switch (metric.format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: precision
        }).format(value);
      case 'percentage':
        return `${value.toFixed(precision)}%`;
      case 'duration':
        return formatDuration(value);
      case 'number':
      default:
        return value >= 1000 ? value.toLocaleString() : value.toFixed(precision);
    }
  };

  // Toggle group collapse
  const toggleGroup = (groupTitle: string) => {
    const newCollapsed = new Set(collapsedGroups);
    if (newCollapsed.has(groupTitle)) {
      newCollapsed.delete(groupTitle);
    } else {
      newCollapsed.add(groupTitle);
    }
    setCollapsedGroups(newCollapsed);
  };

  // Get layout classes
  const getLayoutClasses = (groupLayout?: string) => {
    const effectiveLayout = groupLayout || layout;
    
    switch (effectiveLayout) {
      case 'grid-2':
        return 'grid grid-cols-1 sm:grid-cols-2 gap-4';
      case 'grid-3':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
      case 'grid-4':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4';
      case 'row':
        return 'flex flex-wrap gap-4';
      case 'compact':
        return 'grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2';
      case 'responsive':
      case 'auto':
      default:
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4';
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[1, 2, 3].map((group) => (
          <div key={group}>
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4 animate-pulse"></div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {[1, 2, 3, 4].map((card) => (
                <MetricCard
                  key={card}
                  title=""
                  value={0}
                  icon={Activity}
                  loading
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-red-200 dark:border-red-800 text-center ${className}`}>
        <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Failed to load metrics
        </h3>
        <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  // Empty state
  if (!data || (calculatedMetrics.length === 0 && (!groups || groups.length === 0))) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-8 shadow-sm border border-gray-200 dark:border-gray-700 text-center ${className}`}>
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No metrics available
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">{emptyMessage}</p>
      </div>
    );
  }

  // Use provided groups or create default group
  const metricsGroups = groups || [
    {
      title: 'Overview',
      metrics: calculatedMetrics,
      layout: layout as any
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with last update */}
      {(lastUpdate || autoRefresh) && (
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          {lastUpdate && (
            <span>Last updated: {lastUpdate.toLocaleTimeString()}</span>
          )}
          {autoRefresh && refreshInterval && (
            <span className="flex items-center">
              <Activity className="w-4 h-4 mr-1" />
              Auto-refresh: {refreshInterval}s
            </span>
          )}
        </div>
      )}

      {/* Metrics Groups */}
      {metricsGroups.map((group, groupIndex) => {
        const isCollapsed = collapsedGroups.has(group.title);
        
        return (
          <div key={groupIndex} className="space-y-4">
            {/* Group Header */}
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {group.title}
                </h3>
                {group.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {group.description}
                  </p>
                )}
              </div>
              
              {group.collapsible && (
                <button
                  onClick={() => toggleGroup(group.title)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <TrendingUp 
                    className={`w-4 h-4 text-gray-500 transition-transform ${
                      isCollapsed ? 'rotate-180' : ''
                    }`} 
                  />
                </button>
              )}
            </div>

            {/* Metrics Grid */}
            {!isCollapsed && (
              <div className={getLayoutClasses(group.layout)}>
                {group.metrics.map((metric, index) => {
                  const alertStatus = checkAlerts(metric);
                  
                  return (
                    <MetricCard
                      key={`${groupIndex}-${index}`}
                      title={metric.title}
                      value={formatValue(metric)}
                      trend={showTrends ? metric.trend : undefined}
                      icon={metric.icon}
                      variant={
                        alertStatus === 'critical' ? 'danger' :
                        alertStatus === 'warning' ? 'warning' :
                        metric.variant
                      }
                      size={size}
                      subtitle={metric.subtitle}
                      onClick={() => {
                        metric.onClick?.();
                        onMetricClick?.(metric);
                      }}
                      className={
                        alertStatus !== 'normal' 
                          ? 'ring-2 ring-opacity-50 ' + (
                              alertStatus === 'critical' 
                                ? 'ring-red-500' 
                                : 'ring-yellow-500'
                            )
                          : ''
                      }
                    />
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Helper functions for calculating metrics
function calculateAnalyticsMetrics(data: any, period: string, showTrends: boolean): MetricData[] {
  if (!data) return [];

  // Extract from your Analytics.tsx data structure
  const totalDetections = Array.isArray(data) 
    ? data.reduce((sum: number, item: any) => sum + (item.total || 0), 0)
    : 0;
  
  const totalPeople = Array.isArray(data)
    ? data.reduce((sum: number, item: any) => sum + (item.people || 0), 0)
    : 0;
    
  const totalVehicles = Array.isArray(data)
    ? data.reduce((sum: number, item: any) => sum + (item.vehicles || 0), 0)
    : 0;

  const avgPerHour = data.length > 0 ? Math.round(totalDetections / data.length) : 0;

  return [
    {
      key: 'total_detections',
      title: 'Total Detections',
      value: totalDetections,
      trend: showTrends ? { direction: 'up', percentage: 12.5 } : undefined,
      icon: Eye,
      variant: 'primary'
    },
    {
      key: 'people_detected',
      title: 'People Detected',
      value: totalPeople,
      trend: showTrends ? { direction: 'up', percentage: 8.2 } : undefined,
      icon: Users,
      variant: 'success'
    },
    {
      key: 'vehicles_detected',
      title: 'Vehicles Detected',
      value: totalVehicles,
      trend: showTrends ? { direction: 'down', percentage: -3.1 } : undefined,
      icon: Car,
      variant: 'warning'
    },
    {
      key: 'avg_per_hour',
      title: 'Avg per Hour',
      value: avgPerHour,
      trend: showTrends ? { direction: 'up', percentage: 5.7 } : undefined,
      icon: Activity,
      variant: 'detection'
    }
  ];
}

function calculateDetectionMetrics(data: any, period: string, showTrends: boolean): MetricData[] {
  return [
    {
      key: 'accuracy',
      title: 'Detection Accuracy',
      value: 94.8,
      format: 'percentage',
      precision: 1,
      trend: showTrends ? { direction: 'up', percentage: 2.1 } : undefined,
      icon: Target,
      variant: 'success'
    },
    {
      key: 'processing_speed',
      title: 'Processing Speed',
      value: 28.5,
      subtitle: 'FPS',
      trend: showTrends ? { direction: 'neutral', percentage: 0 } : undefined,
      icon: Zap,
      variant: 'info'
    },
    {
      key: 'active_cameras',
      title: 'Active Cameras',
      value: 8,
      subtitle: 'of 12 total',
      trend: showTrends ? { direction: 'down', percentage: -16.7 } : undefined,
      icon: Camera,
      variant: 'warning'
    },
    {
      key: 'uptime',
      title: 'System Uptime',
      value: 99.2,
      format: 'percentage',
      precision: 1,
      trend: showTrends ? { direction: 'up', percentage: 0.3 } : undefined,
      icon: Shield,
      variant: 'success'
    }
  ];
}

function calculateSystemMetrics(data: any, period: string, showTrends: boolean): MetricData[] {
  return [
    {
      key: 'cpu_usage',
      title: 'CPU Usage',
      value: 67.3,
      format: 'percentage',
      precision: 1,
      trend: showTrends ? { direction: 'up', percentage: 8.5 } : undefined,
      icon: Cpu,
      variant: 'warning'
    },
    {
      key: 'memory_usage',
      title: 'Memory Usage',
      value: 4.2,
      subtitle: 'GB',
      trend: showTrends ? { direction: 'down', percentage: -2.3 } : undefined,
      icon: BarChart3,
      variant: 'info'
    },
    {
      key: 'network_status',
      title: 'Network Status',
      value: 'Stable',
      trend: showTrends ? { direction: 'neutral', percentage: 0 } : undefined,
      icon: Wifi,
      variant: 'success'
    },
    {
      key: 'storage_used',
      title: 'Storage Used',
      value: 78.9,
      format: 'percentage',
      precision: 1,
      trend: showTrends ? { direction: 'up', percentage: 5.2 } : undefined,
      icon: BarChart3,
      variant: 'warning'
    }
  ];
}

function calculatePerformanceMetrics(data: any, period: string, showTrends: boolean): MetricData[] {
  return [
    {
      key: 'response_time',
      title: 'Avg Response Time',
      value: 145,
      subtitle: 'ms',
      trend: showTrends ? { direction: 'down', percentage: -12.3 } : undefined,
      icon: Clock,
      variant: 'success'
    },
    {
      key: 'throughput',
      title: 'Throughput',
      value: 1247,
      subtitle: 'detections/min',
      trend: showTrends ? { direction: 'up', percentage: 18.7 } : undefined,
      icon: TrendingUp,
      variant: 'primary'
    },
    {
      key: 'error_rate',
      title: 'Error Rate',
      value: 0.3,
      format: 'percentage',
      precision: 1,
      trend: showTrends ? { direction: 'down', percentage: -45.2 } : undefined,
      icon: AlertTriangle,
      variant: 'success'
    },
    {
      key: 'success_rate',
      title: 'Success Rate',
      value: 99.7,
      format: 'percentage',
      precision: 1,
      trend: showTrends ? { direction: 'up', percentage: 0.8 } : undefined,
      icon: CheckCircle,
      variant: 'success'
    }
  ];
}

function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

export default MetricsDashboard;
export type { MetricData, MetricGroup, MetricsDashboardProps };