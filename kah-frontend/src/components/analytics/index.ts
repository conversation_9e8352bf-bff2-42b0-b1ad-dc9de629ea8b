// ============================================================================
// Analytics Components Barrel Export
// ============================================================================
// 
// This file provides clean, organized exports for all analytics components
// in the Kaydan Analytic Hub (KAH) data analytics platform.
//
// Usage:
//   import { MetricCard, TrendChart, HeatmapChart } from '@/components/analytics';
//   import { FilterPanel, ExportButton } from '@/components/analytics';
//

// ============================================================================
// CORE COMPONENTS
// ============================================================================

// Metric Display Components
export { default as MetricCard } from './MetricCard';
export { default as MetricsDashboard } from './MetricsDashboard';

// Chart Components  
export { default as TrendChart } from './TrendChart';
export { default as DistributionChart } from './DistributionChart';
export { default as ComparisonChart } from './ComparisonChart';
export { default as HeatmapChart } from './HeatmapChart';

// UI Components
export { default as FilterPanel } from './FilterPanel';
export { default as ExportButton } from './ExportButton';
export { default as DateRangePicker } from './DateRangePicker';

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// MetricCard Types
export type {
  TrendData,
  MetricCardProps
} from './MetricCard';

// MetricsDashboard Types
export type {
  MetricData,
  MetricGroup,
  MetricsDashboardProps
} from './MetricsDashboard';

// TrendChart Types
export type {
  TrendDataPoint,
  LineConfig,
  TrendChartProps
} from './TrendChart';

// DistributionChart Types
export type {
  DistributionDataPoint,
  DistributionChartProps
} from './DistributionChart';

// ComparisonChart Types
export type {
  ComparisonDataPoint,
  MetricConfig,
  ComparisonChartProps
} from './ComparisonChart';

// HeatmapChart Types
export type {
  HeatmapDataPoint,
  HeatmapConfig,
  HeatmapChartProps,
  ColorScale
} from './HeatmapChart';

// FilterPanel Types
export type {
  FilterGroup,
  FilterOption,
  FilterState,
  AppliedFilter,
  FilterPanelProps
} from './FilterPanel';

// ExportButton Types
export type {
  ExportFormat,
  ExportOptions,
  ExportButtonProps
} from './ExportButton';

// DateRangePicker Types
export type {
  DateRange,
  DateRangePreset,
  DateRangePickerProps
} from './DateRangePicker';

// ============================================================================
// UTILITY EXPORTS
// ============================================================================

// Re-export commonly used types for convenience
export type AnalyticsVariant = 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'detection';
export type AnalyticsSize = 'sm' | 'md' | 'lg';
export type ChartLayout = 'horizontal' | 'vertical' | 'compact' | 'responsive';

// ============================================================================
// COMPONENT GROUPINGS FOR ORGANIZED IMPORTS
// ============================================================================

// Import the components first to avoid reference errors
import MetricCard from './MetricCard';
import MetricsDashboard from './MetricsDashboard';
import TrendChart from './TrendChart';
import DistributionChart from './DistributionChart';
import ComparisonChart from './ComparisonChart';
import HeatmapChart from './HeatmapChart';
import FilterPanel from './FilterPanel';
import ExportButton from './ExportButton';
import DateRangePicker from './DateRangePicker';

// Metric Components Group
export const MetricComponents = {
  MetricCard,
  MetricsDashboard
} as const;

// Chart Components Group  
export const ChartComponents = {
  TrendChart,
  DistributionChart,
  ComparisonChart,
  HeatmapChart
} as const;

// UI Components Group
export const UIComponents = {
  FilterPanel,
  ExportButton,
  DateRangePicker
} as const;

// All Components Group
export const AnalyticsComponents = {
  ...MetricComponents,
  ...ChartComponents,
  ...UIComponents
} as const;

// ============================================================================
// PRESET CONFIGURATIONS
// ============================================================================

// Common color schemes for charts
export const AnalyticsColorSchemes = {
  // Kaydan Analytics Colors
  analytics: {
    people: '#10b981',     // green-500
    vehicles: '#f59e0b',   // yellow-500  
    objects: '#8b5cf6',    // purple-500
    zones: '#06b6d4',      // cyan-500
    alerts: '#ef4444',     // red-500
    success: '#10b981',    // green-500
    warning: '#f59e0b',    // yellow-500
    error: '#ef4444',      // red-500
    info: '#f97316',       // kaydan-500
    primary: '#f97316'     // kaydan-500
  },
  
  // Professional Color Palette
  professional: {
    primary: '#f97316',    // kaydan-500
    secondary: '#64748b',  // kaydan-gray-500
    accent: '#fb923c',     // kaydan-400
    success: '#059669',    // emerald-600
    warning: '#d97706',    // amber-600
    error: '#dc2626',      // red-600
    info: '#0891b2'        // cyan-600
  },

  // Accessibility High Contrast
  highContrast: {
    primary: '#ea580c',    // kaydan-600
    secondary: '#475569',  // kaydan-gray-600
    accent: '#f97316',     // kaydan-500
    success: '#047857',
    warning: '#b45309',
    error: '#b91c1c',
    info: '#0e7490'
  }
} as const;

// Common chart configurations
export const AnalyticsPresets = {
  // Analytics Trends Presets
  analyticsTrends: {
    lines: [
      { dataKey: 'people', name: 'People', color: AnalyticsColorSchemes.analytics.people },
      { dataKey: 'vehicles', name: 'Vehicles', color: AnalyticsColorSchemes.analytics.vehicles },
      { dataKey: 'total', name: 'Total', color: AnalyticsColorSchemes.analytics.primary }
    ],
    height: 300,
    showTrendIndicator: true,
    animate: true
  },

  // Object Distribution Preset
  objectDistribution: {
    colorMapping: {
      'People': AnalyticsColorSchemes.analytics.people,
      'Cars': AnalyticsColorSchemes.analytics.vehicles,
      'Trucks': AnalyticsColorSchemes.analytics.objects,
      'Motorcycles': AnalyticsColorSchemes.analytics.zones,
      'Bicycles': AnalyticsColorSchemes.analytics.info
    },
    variant: 'donut' as const,
    showTotals: true,
    interactive: true
  },

  // Zone Performance Comparison
  zoneComparison: {
    metrics: [
      {
        key: 'people',
        name: 'People Detected',
        color: AnalyticsColorSchemes.analytics.people,
        format: 'number' as const
      },
      {
        key: 'vehicles',
        name: 'Vehicles Detected', 
        color: AnalyticsColorSchemes.analytics.vehicles,
        format: 'number' as const
      },
      {
        key: 'efficiency',
        name: 'Efficiency',
        color: AnalyticsColorSchemes.analytics.success,
        format: 'percentage' as const,
        target: 95
      }
    ],
    variant: 'grouped' as const,
    showTargets: true,
    allowSorting: true
  },

  // System Metrics Dashboard
  systemMetrics: [
    {
      key: 'cpu_usage',
      title: 'CPU Usage',
      format: 'percentage' as const,
      variant: 'warning' as const,
      icon: 'Cpu' as const
    },
    {
      key: 'memory_usage', 
      title: 'Memory Usage',
      format: 'number' as const,
      variant: 'info' as const,
      icon: 'BarChart3' as const,
      unit: 'GB'
    },
    {
      key: 'active_cameras',
      title: 'Active Cameras',
      format: 'number' as const,
      variant: 'success' as const,
      icon: 'Camera' as const
    },
    {
      key: 'detection_rate',
      title: 'Detection Rate',
      format: 'number' as const,
      variant: 'analytics' as const,
      icon: 'Eye' as const,
      unit: '/min'
    }
  ]
} as const;

// Common filter presets
export const FilterPresets = {
  // Time Range Filters
  timeRanges: [
    { id: 'last1h', label: 'Last Hour', icon: 'Clock' },
    { id: 'last24h', label: 'Last 24 Hours', icon: 'Calendar' },
    { id: 'last7d', label: 'Last 7 Days', icon: 'BarChart3' },
    { id: 'last30d', label: 'Last 30 Days', icon: 'TrendingUp' },
    { id: 'thisMonth', label: 'This Month', icon: 'CalendarDays' },
    { id: 'lastMonth', label: 'Last Month', icon: 'CalendarDays' }
  ],

  // Object Type Filters
  objectTypes: [
    { id: 'people', label: 'People', value: 'people', color: AnalyticsColorSchemes.analytics.people },
    { id: 'cars', label: 'Cars', value: 'cars', color: AnalyticsColorSchemes.analytics.vehicles },
    { id: 'trucks', label: 'Trucks', value: 'trucks', color: AnalyticsColorSchemes.analytics.objects },
    { id: 'motorcycles', label: 'Motorcycles', value: 'motorcycles', color: AnalyticsColorSchemes.analytics.zones },
    { id: 'bicycles', label: 'Bicycles', value: 'bicycles', color: AnalyticsColorSchemes.analytics.info }
  ],

  // Zone Filters (example - would be dynamic in real app)
  zones: [
    { id: 'entrance', label: 'Entrance', value: 'entrance' },
    { id: 'parking-a', label: 'Parking Lot A', value: 'parking-a' },
    { id: 'corridor', label: 'Main Corridor', value: 'corridor' },
    { id: 'exit', label: 'Exit Gate', value: 'exit' },
    { id: 'loading', label: 'Loading Bay', value: 'loading' }
  ]
} as const;

// Export format presets
export const ExportPresets = {
  formats: [
    {
      id: 'csv',
      name: 'CSV',
      icon: 'FileSpreadsheet',
      extension: 'csv',
      mimeType: 'text/csv',
      description: 'Comma-separated values for spreadsheets'
    },
    {
      id: 'xlsx',
      name: 'Excel',
      icon: 'FileSpreadsheet', 
      extension: 'xlsx',
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      description: 'Microsoft Excel format'
    },
    {
      id: 'json',
      name: 'JSON',
      icon: 'FileText',
      extension: 'json', 
      mimeType: 'application/json',
      description: 'JavaScript Object Notation'
    },
    {
      id: 'pdf',
      name: 'PDF Report',
      icon: 'File',
      extension: 'pdf',
      mimeType: 'application/pdf',
      description: 'Portable Document Format with charts'
    },
    {
      id: 'png',
      name: 'PNG Image',
      icon: 'FileImage',
      extension: 'png',
      mimeType: 'image/png', 
      description: 'Chart as PNG image'
    }
  ]
} as const;

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

// Color utility functions
export const AnalyticsUtils = {
  // Get color by variant
  getVariantColor: (variant: AnalyticsVariant): string => {
    const colorMap = {
      primary: AnalyticsColorSchemes.analytics.primary,
      success: AnalyticsColorSchemes.analytics.success,
      warning: AnalyticsColorSchemes.analytics.warning,
      danger: AnalyticsColorSchemes.analytics.error,
      info: AnalyticsColorSchemes.analytics.info,
      detection: AnalyticsColorSchemes.analytics.zones
    };
    return colorMap[variant] || colorMap.primary;
  },

  // Format numbers consistently
  formatNumber: (value: number, format: 'number' | 'percentage' | 'currency' | 'duration' = 'number', precision: number = 0): string => {
    switch (format) {
      case 'percentage':
        return `${value.toFixed(precision)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: precision
        }).format(value);
      case 'duration':
        const hours = Math.floor(value / 3600);
        const minutes = Math.floor((value % 3600) / 60);
        const seconds = value % 60;
        if (hours > 0) return `${hours}h ${minutes}m`;
        if (minutes > 0) return `${minutes}m ${seconds}s`;
        return `${seconds}s`;
      case 'number':
      default:
        return value >= 1000 ? value.toLocaleString() : value.toFixed(precision);
    }
  },

  // Generate time series data
  generateTimeSeriesData: (startDate: Date, endDate: Date, interval: 'hour' | 'day' | 'week' = 'day') => {
    const data = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      data.push({
        time: current.toISOString(),
        timestamp: current.getTime(),
        formatted: current.toLocaleDateString()
      });
      
      switch (interval) {
        case 'hour':
          current.setHours(current.getHours() + 1);
          break;
        case 'week':
          current.setDate(current.getDate() + 7);
          break;
        case 'day':
        default:
          current.setDate(current.getDate() + 1);
          break;
      }
    }
    
    return data;
  }
} as const;

// ============================================================================
// COMPONENT COMPOSITION HELPERS
// ============================================================================

// Higher-order component for consistent theming
export const withAnalyticsTheme = <P extends object>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => {
    return React.createElement(Component, props);
  };
};

// Hook for consistent analytics colors
export const useAnalyticsColors = () => {
  return AnalyticsColorSchemes.analytics;
};

// Hook for analytics formatting
export const useAnalyticsFormatters = () => {
  return {
    formatNumber: AnalyticsUtils.formatNumber,
    formatDuration: (seconds: number) => AnalyticsUtils.formatNumber(seconds, 'duration'),
    formatPercentage: (value: number, precision = 1) => AnalyticsUtils.formatNumber(value, 'percentage', precision),
    formatCurrency: (value: number, precision = 2) => AnalyticsUtils.formatNumber(value, 'currency', precision)
  };
};

// ============================================================================
// VERSION INFO
// ============================================================================

export const ANALYTICS_VERSION = '1.0.0';
export const ANALYTICS_BUILD_DATE = new Date().toISOString();

// ============================================================================
// DEFAULT EXPORT FOR CONVENIENCE
// ============================================================================

// Default export includes all components for simple import
const Analytics = {
  // Components
  ...AnalyticsComponents,
  
  // Presets
  ColorSchemes: AnalyticsColorSchemes,
  Presets: AnalyticsPresets,
  FilterPresets,
  ExportPresets,
  
  // Utils
  Utils: AnalyticsUtils,
  
  // Version
  version: ANALYTICS_VERSION
} as const;

export default Analytics;