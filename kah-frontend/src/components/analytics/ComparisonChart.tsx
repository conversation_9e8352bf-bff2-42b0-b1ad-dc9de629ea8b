import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>,
  Legend,
  ReferenceLine,
  Cell,
  LabelList
} from 'recharts';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  MoreHorizontal,
  ArrowUpDown,
  Eye,
  EyeOff,
  Download,
  Maximize2,
  Target,
  Award,
  AlertTriangle,
  Filter,
  RotateCcw
} from 'lucide-react';

interface ComparisonDataPoint {
  name: string;
  [key: string]: string | number;
}

interface MetricConfig {
  key: string;
  name: string;
  color: string;
  unit?: string;
  format?: 'number' | 'percentage' | 'currency' | 'duration';
  precision?: number;
  target?: number;
  visible?: boolean;
  stackId?: string;
  type?: 'value' | 'trend' | 'efficiency' | 'count';
}

interface ComparisonChartProps {
  data: ComparisonDataPoint[];
  metrics: MetricConfig[];
  title?: string;
  subtitle?: string;
  orientation?: 'vertical' | 'horizontal';
  variant?: 'grouped' | 'stacked' | 'normalized';
  size?: 'sm' | 'md' | 'lg';
  showValues?: boolean;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  showTargets?: boolean;
  showRankings?: boolean;
  allowSorting?: boolean;
  allowFiltering?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filterBy?: string;
  loading?: boolean;
  error?: string;
  emptyMessage?: string;
  className?: string;
  height?: number;
  animate?: boolean;
  interactive?: boolean;
  onBarClick?: (data: any, metric: string) => void;
  onMetricToggle?: (metricKey: string, visible: boolean) => void;
  showActions?: boolean;
  onExport?: () => void;
  onFullscreen?: () => void;
  highlightTop?: number;
  highlightBottom?: number;
  comparisonMode?: 'absolute' | 'relative' | 'percentage';
  baselineKey?: string;
  showDifference?: boolean;
  groupBy?: string;
  aggregation?: 'sum' | 'average' | 'max' | 'min';
  thresholds?: {
    [metricKey: string]: {
      good?: number;
      warning?: number;
      critical?: number;
    };
  };
}

const ComparisonChart: React.FC<ComparisonChartProps> = ({
  data,
  metrics,
  title,
  subtitle,
  orientation = 'vertical',
  variant = 'grouped',
  size = 'md',
  showValues = false,
  showLegend = true,
  showGrid = true,
  showTooltip = true,
  showTargets = false,
  showRankings = false,
  allowSorting = true,
  allowFiltering = true,
  sortBy,
  sortOrder = 'desc',
  filterBy,
  loading = false,
  error,
  emptyMessage = 'No data available for comparison',
  className = '',
  height,
  animate = true,
  interactive = true,
  onBarClick,
  onMetricToggle,
  showActions = false,
  onExport,
  onFullscreen,
  highlightTop = 0,
  highlightBottom = 0,
  comparisonMode = 'absolute',
  baselineKey,
  showDifference = false,
  groupBy,
  aggregation = 'sum',
  thresholds = {}
}) => {
  const [visibleMetrics, setVisibleMetrics] = useState<Set<string>>(
    new Set(metrics.filter(m => m.visible !== false).map(m => m.key))
  );
  const [localSortBy, setLocalSortBy] = useState(sortBy);
  const [localSortOrder, setLocalSortOrder] = useState<'asc' | 'desc'>(sortOrder);
  const [localFilterBy, setLocalFilterBy] = useState(filterBy);

  // Size configurations
  const sizeConfigs = {
    sm: {
      height: height || 200,
      fontSize: 'text-xs',
      titleSize: 'text-base',
      padding: 'p-4'
    },
    md: {
      height: height || 300,
      fontSize: 'text-sm',
      titleSize: 'text-lg',
      padding: 'p-6'
    },
    lg: {
      height: height || 400,
      fontSize: 'text-base',
      titleSize: 'text-xl',
      padding: 'p-8'
    }
  };

  const config = sizeConfigs[size];

  // Process and transform data
  const processedData = useMemo(() => {
    let result = [...data];

    // Apply filtering
    if (localFilterBy && localFilterBy !== 'all') {
      result = result.filter(item => {
        return Object.values(item).some(value => 
          value.toString().toLowerCase().includes(localFilterBy.toLowerCase())
        );
      });
    }

    // Apply grouping if specified
    if (groupBy) {
      const grouped = result.reduce((acc, item) => {
        const groupKey = item[groupBy] as string;
        if (!acc[groupKey]) {
          acc[groupKey] = [];
        }
        acc[groupKey].push(item);
        return acc;
      }, {} as Record<string, ComparisonDataPoint[]>);

      result = Object.entries(grouped).map(([groupKey, items]) => {
        const aggregated: ComparisonDataPoint = { name: groupKey };
        
        metrics.forEach(metric => {
          const values = items.map(item => Number(item[metric.key]) || 0);
          switch (aggregation) {
            case 'sum':
              aggregated[metric.key] = values.reduce((sum, val) => sum + val, 0);
              break;
            case 'average':
              aggregated[metric.key] = values.reduce((sum, val) => sum + val, 0) / values.length;
              break;
            case 'max':
              aggregated[metric.key] = Math.max(...values);
              break;
            case 'min':
              aggregated[metric.key] = Math.min(...values);
              break;
          }
        });
        
        return aggregated;
      });
    }

    // Apply comparison mode transformations
    if (comparisonMode === 'relative' && baselineKey) {
      const baseline = result.find(item => item.name === baselineKey);
      if (baseline) {
        result = result.map(item => {
          const transformed = { ...item };
          metrics.forEach(metric => {
            const baseValue = Number(baseline[metric.key]) || 0;
            const currentValue = Number(item[metric.key]) || 0;
            if (baseValue !== 0) {
              transformed[metric.key] = ((currentValue - baseValue) / baseValue) * 100;
            }
          });
          return transformed;
        });
      }
    } else if (comparisonMode === 'percentage') {
      result = result.map(item => {
        const transformed = { ...item };
        const total = metrics.reduce((sum, metric) => sum + (Number(item[metric.key]) || 0), 0);
        if (total > 0) {
          metrics.forEach(metric => {
            const value = Number(item[metric.key]) || 0;
            transformed[metric.key] = (value / total) * 100;
          });
        }
        return transformed;
      });
    }

    // Apply sorting
    if (localSortBy) {
      result.sort((a, b) => {
        const aVal = Number(a[localSortBy]) || 0;
        const bVal = Number(b[localSortBy]) || 0;
        return localSortOrder === 'asc' ? aVal - bVal : bVal - aVal;
      });
    }

    return result;
  }, [data, metrics, localFilterBy, localSortBy, localSortOrder, groupBy, aggregation, comparisonMode, baselineKey]);

  // Get visible metrics
  const activeMetrics = metrics.filter(metric => visibleMetrics.has(metric.key));

  // Format values based on metric configuration
  const formatValue = (value: number, metric: MetricConfig): string => {
    if (isNaN(value)) return '0';

    const precision = metric.precision || 0;

    switch (metric.format) {
      case 'percentage':
        return `${value.toFixed(precision)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: precision
        }).format(value);
      case 'duration':
        return formatDuration(value);
      case 'number':
      default:
        if (value >= 1000) {
          return value.toLocaleString(undefined, { 
            minimumFractionDigits: precision,
            maximumFractionDigits: precision 
          });
        }
        return value.toFixed(precision);
    }
  };

  // Get color for threshold-based styling
  const getThresholdColor = (value: number, metricKey: string): string => {
    const threshold = thresholds[metricKey];
    if (!threshold) return '';

    if (threshold.critical !== undefined && value <= threshold.critical) {
      return '#ef4444'; // red-500
    }
    if (threshold.warning !== undefined && value <= threshold.warning) {
      return '#f59e0b'; // yellow-500
    }
    if (threshold.good !== undefined && value >= threshold.good) {
      return '#10b981'; // green-500
    }

    return '';
  };

  // Get ranking for an item
  const getRanking = (itemName: string, metricKey: string): number => {
    const sorted = [...processedData].sort((a, b) => {
      const aVal = Number(a[metricKey]) || 0;
      const bVal = Number(b[metricKey]) || 0;
      return bVal - aVal; // Descending order
    });
    return sorted.findIndex(item => item.name === itemName) + 1;
  };

  // Handle metric toggle
  const handleMetricToggle = (metricKey: string) => {
    const newVisible = new Set(visibleMetrics);
    if (newVisible.has(metricKey)) {
      newVisible.delete(metricKey);
    } else {
      newVisible.add(metricKey);
    }
    setVisibleMetrics(newVisible);
    onMetricToggle?.(metricKey, newVisible.has(metricKey));
  };

  // Handle sorting
  const handleSort = (metricKey: string) => {
    if (localSortBy === metricKey) {
      setLocalSortOrder(localSortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setLocalSortBy(metricKey);
      setLocalSortOrder('desc');
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-xs">
        <p className="font-medium text-gray-900 dark:text-white mb-2">{label}</p>
        <div className="space-y-2">
          {payload.map((entry: any, index: number) => {
            const metric = metrics.find(m => m.key === entry.dataKey);
            if (!metric) return null;

            return (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div 
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {metric.name}:
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white ml-2">
                  {formatValue(entry.value, metric)}
                  {metric.unit && ` ${metric.unit}`}
                </span>
              </div>
            );
          })}
          
          {showRankings && payload.length > 0 && (
            <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Ranking: #{getRanking(label, payload[0].dataKey)}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Custom label for values on bars
  const CustomLabel = (props: any) => {
    if (!showValues) return null;

    const { x, y, width, height, value, dataKey } = props;
    const metric = metrics.find(m => m.key === dataKey);
    if (!metric) return null;

    const formattedValue = formatValue(value, metric);
    
    return (
      <text
        x={orientation === 'vertical' ? x + width / 2 : x + width + 5}
        y={orientation === 'vertical' ? y - 5 : y + height / 2}
        fill="currentColor"
        textAnchor={orientation === 'vertical' ? 'middle' : 'start'}
        dy={orientation === 'vertical' ? 0 : '0.3em'}
        className="text-xs font-medium fill-gray-600 dark:fill-gray-300"
      >
        {formattedValue}
      </text>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${config.padding} ${className}`}>
        <div className="animate-pulse">
          {title && <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>}
          {subtitle && <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>}
          <div className="bg-gray-200 dark:bg-gray-700 rounded" style={{ height: config.height }}></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-red-200 dark:border-red-800 ${config.padding} ${className}`}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Failed to load comparison chart
          </h3>
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!processedData || processedData.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${config.padding} ${className}`}>
        <div className="text-center">
          <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No data to compare
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${config.padding} ${className}`}>
      {/* Header */}
      {(title || subtitle || showActions || allowSorting || allowFiltering) && (
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-6">
          <div className="flex-1">
            {title && (
              <h3 className={`${config.titleSize} font-semibold text-gray-900 dark:text-white`}>
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
          </div>

          {/* Controls */}
          <div className="flex flex-wrap items-center gap-2">
            {/* Filter */}
            {allowFiltering && (
              <div className="relative">
                <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Filter items..."
                  value={localFilterBy || ''}
                  onChange={(e) => setLocalFilterBy(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            )}

            {/* Sort */}
            {allowSorting && (
              <select
                value={localSortBy || ''}
                onChange={(e) => handleSort(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="">Sort by...</option>
                {metrics.map(metric => (
                  <option key={metric.key} value={metric.key}>
                    {metric.name}
                  </option>
                ))}
              </select>
            )}

            {/* Reset */}
            {(localFilterBy || localSortBy) && (
              <button
                onClick={() => {
                  setLocalFilterBy('');
                  setLocalSortBy(undefined);
                  setLocalSortOrder('desc');
                }}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                title="Reset filters"
              >
                <RotateCcw className="w-4 h-4 text-gray-500" />
              </button>
            )}

            {/* Actions */}
            {showActions && (
              <div className="flex items-center gap-1">
                {onExport && (
                  <button
                    onClick={onExport}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Export Chart"
                  >
                    <Download className="w-4 h-4 text-gray-500" />
                  </button>
                )}
                {onFullscreen && (
                  <button
                    onClick={onFullscreen}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Fullscreen"
                  >
                    <Maximize2 className="w-4 h-4 text-gray-500" />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Metric toggles */}
      {interactive && metrics.length > 1 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {metrics.map((metric) => (
            <button
              key={metric.key}
              onClick={() => handleMetricToggle(metric.key)}
              className={`flex items-center px-3 py-1.5 rounded-md text-sm transition-colors ${
                visibleMetrics.has(metric.key)
                  ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }`}
            >
              <div 
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: metric.color }}
              />
              {metric.name}
              {visibleMetrics.has(metric.key) ? (
                <Eye className="w-3 h-3 ml-2" />
              ) : (
                <EyeOff className="w-3 h-3 ml-2" />
              )}
            </button>
          ))}
        </div>
      )}

      {/* Chart */}
      <div style={{ height: config.height }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={processedData}
            layout={orientation === 'horizontal' ? 'horizontal' : 'vertical'}
            margin={{
              top: showValues ? 20 : 5,
              right: orientation === 'horizontal' && showValues ? 50 : 5,
              left: 5,
              bottom: 5,
            }}
          >
            {showGrid && (
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="opacity-30"
                stroke="currentColor"
              />
            )}
            
            {orientation === 'horizontal' ? (
              <>
                <XAxis 
                  type="number"
                  className={config.fontSize}
                  tick={{ fill: 'currentColor' }}
                  axisLine={{ stroke: 'currentColor' }}
                  tickLine={{ stroke: 'currentColor' }}
                />
                <YAxis 
                  dataKey="name"
                  type="category"
                  width={100}
                  className={config.fontSize}
                  tick={{ fill: 'currentColor' }}
                  axisLine={{ stroke: 'currentColor' }}
                  tickLine={{ stroke: 'currentColor' }}
                />
              </>
            ) : (
              <>
                <XAxis 
                  dataKey="name"
                  className={config.fontSize}
                  tick={{ fill: 'currentColor' }}
                  axisLine={{ stroke: 'currentColor' }}
                  tickLine={{ stroke: 'currentColor' }}
                />
                <YAxis 
                  className={config.fontSize}
                  tick={{ fill: 'currentColor' }}
                  axisLine={{ stroke: 'currentColor' }}
                  tickLine={{ stroke: 'currentColor' }}
                />
              </>
            )}

            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend />}

            {/* Reference lines for targets */}
            {showTargets && activeMetrics.map(metric => (
              metric.target && (
                <ReferenceLine 
                  key={`target-${metric.key}`}
                  y={metric.target}
                  stroke={metric.color}
                  strokeDasharray="5 5"
                  label={{ value: `${metric.name} Target`, position: 'right' }}
                />
              )
            ))}

            {/* Bars */}
            {activeMetrics.map((metric, index) => (
              <Bar
                key={metric.key}
                dataKey={metric.key}
                name={metric.name}
                fill={metric.color}
                stackId={variant === 'stacked' ? metric.stackId || 'stack' : undefined}
                onClick={interactive ? (data) => onBarClick?.(data, metric.key) : undefined}
                isAnimationActive={animate}
                radius={variant === 'grouped' ? [2, 2, 0, 0] : undefined}
              >
                {/* Custom coloring based on thresholds */}
                {processedData.map((entry, cellIndex) => {
                  const value = Number(entry[metric.key]) || 0;
                  const thresholdColor = getThresholdColor(value, metric.key);
                  const isHighlighted = 
                    (highlightTop > 0 && cellIndex < highlightTop) ||
                    (highlightBottom > 0 && cellIndex >= processedData.length - highlightBottom);
                  
                  return (
                    <Cell 
                      key={`cell-${cellIndex}`}
                      fill={thresholdColor || metric.color}
                      opacity={isHighlighted ? 1 : 0.8}
                      stroke={isHighlighted ? '#374151' : 'none'}
                      strokeWidth={isHighlighted ? 2 : 0}
                    />
                  );
                })}
                
                {showValues && <LabelList content={<CustomLabel />} />}
              </Bar>
            ))}
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Footer with summary */}
      {(showRankings || comparisonMode !== 'absolute') && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400">
            {comparisonMode === 'relative' && baselineKey && (
              <span>Relative to: {baselineKey}</span>
            )}
            {comparisonMode === 'percentage' && (
              <span>Showing: Percentage distribution</span>
            )}
            {showRankings && (
              <span>Rankings: Based on {localSortBy || 'original order'}</span>
            )}
            {groupBy && (
              <span>Grouped by: {groupBy} ({aggregation})</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function for duration formatting
function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

export default ComparisonChart;
export type { ComparisonDataPoint, MetricConfig, ComparisonChartProps };