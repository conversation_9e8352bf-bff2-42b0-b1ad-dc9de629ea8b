// Complete fixed HeatmapChart.tsx - Combined file

import React, { useMemo, useState } from 'react';
import {
  Thermometer,
  Download,
  Maximize2,
  Eye,
  <PERSON>Off,
  Filter,
  RotateCcw,
  AlertTriangle
} from 'lucide-react';

interface HeatmapDataPoint {
  x: string | number;
  y: string | number;
  value: number;
  metadata?: {
    [key: string]: any;
  };
}

interface HeatmapConfig {
  xAxis: {
    label: string;
    type: 'category' | 'time' | 'numeric';
    format?: string;
  };
  yAxis: {
    label: string;
    type: 'category' | 'time' | 'numeric';
    format?: string;
  };
  value: {
    label: string;
    format: 'number' | 'percentage' | 'duration' | 'currency';
    precision?: number;
    unit?: string;
  };
}

interface ColorScale {
  min: string;
  max: string;
  steps?: string[];
  neutral?: string;
}

interface HeatmapChartProps {
  data: HeatmapDataPoint[];
  config: HeatmapConfig;
  title?: string;
  subtitle?: string;
  colorScale?: ColorScale;
  showLegend?: boolean;
  showValues?: boolean;
  showTooltip?: boolean;
  showGrid?: boolean;
  cellSize?: 'auto' | 'small' | 'medium' | 'large';
  cellShape?: 'square' | 'circle' | 'rounded';
  loading?: boolean;
  error?: string;
  emptyMessage?: string;
  className?: string;
  height?: number;
  width?: number;
  interactive?: boolean;
  onCellClick?: (data: HeatmapDataPoint) => void;
  onCellHover?: (data: HeatmapDataPoint | null) => void;
  showActions?: boolean;
  onExport?: () => void;
  onFullscreen?: () => void;
  aggregation?: 'sum' | 'average' | 'max' | 'min' | 'count';
  threshold?: {
    min?: number;
    max?: number;
    highlight?: number;
  };
  showStats?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
  preset?: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'zones' | 'custom';
  filterBy?: string;
  sortBy?: 'x' | 'y' | 'value';
  sortOrder?: 'asc' | 'desc';
  showPatterns?: boolean;
  patternDetection?: {
    detectTrends?: boolean;
    detectAnomalies?: boolean;
    detectClusters?: boolean;
  };
}

// Helper functions
function interpolateColor(color1: string, color2: string, factor: number): string {
  // Add null/undefined checks and default values
  if (!color1 || !color2) {
    return '#f3f4f6'; // Default neutral color
  }
  
  // Ensure colors start with #
  const c1 = color1.startsWith('#') ? color1 : `#${color1}`;
  const c2 = color2.startsWith('#') ? color2 : `#${color2}`;
  
  const hex1 = c1.replace('#', '');
  const hex2 = c2.replace('#', '');
  
  // Validate hex format
  if (hex1.length !== 6 || hex2.length !== 6) {
    return '#f3f4f6'; // Default neutral color
  }
  
  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);
  
  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);
  
  // Check for NaN values
  if (isNaN(r1) || isNaN(g1) || isNaN(b1) || isNaN(r2) || isNaN(g2) || isNaN(b2)) {
    return '#f3f4f6'; // Default neutral color
  }
  
  const r = Math.round(r1 + (r2 - r1) * factor);
  const g = Math.round(g1 + (g2 - g1) * factor);
  const b = Math.round(b1 + (b2 - b1) * factor);
  
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

function getTextColor(backgroundColor: string): string {
  if (!backgroundColor) return '#374151';
  
  const hex = backgroundColor.replace('#', '');
  
  if (hex.length !== 6) return '#374151';
  
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  
  if (isNaN(r) || isNaN(g) || isNaN(b)) return '#374151';
  
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#374151' : '#ffffff';
}

function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

function detectPatterns(
  data: HeatmapDataPoint[], 
  config: { detectTrends?: boolean; detectAnomalies?: boolean; detectClusters?: boolean }
): any {
  const patterns: any = {};

  if (config.detectTrends) {
    patterns.trends = detectTrends(data);
  }

  if (config.detectAnomalies) {
    patterns.anomalies = detectAnomalies(data);
  }

  if (config.detectClusters) {
    patterns.clusters = detectClusters(data);
  }

  return patterns;
}

function detectTrends(data: HeatmapDataPoint[]): boolean {
  if (data.length < 3) return false;

  const values = data.map(d => d.value);
  let increases = 0;
  let decreases = 0;

  for (let i = 1; i < values.length; i++) {
    if (values[i] > values[i - 1]) increases++;
    if (values[i] < values[i - 1]) decreases++;
  }

  const trendThreshold = values.length * 0.6;
  return increases > trendThreshold || decreases > trendThreshold;
}

function detectAnomalies(data: HeatmapDataPoint[]): boolean {
  if (data.length < 4) return false;

  const values = data.map(d => d.value).sort((a, b) => a - b);
  const q1Index = Math.floor(values.length * 0.25);
  const q3Index = Math.floor(values.length * 0.75);
  
  const q1 = values[q1Index];
  const q3 = values[q3Index];
  const iqr = q3 - q1;
  
  const lowerBound = q1 - 1.5 * iqr;
  const upperBound = q3 + 1.5 * iqr;
  
  return data.some(d => d.value < lowerBound || d.value > upperBound);
}

function detectClusters(data: HeatmapDataPoint[]): boolean {
  if (data.length < 5) return false;

  const values = data.map(d => d.value);
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  const stdDev = Math.sqrt(variance);

  return stdDev > mean * 0.3;
}

const HeatmapChart: React.FC<HeatmapChartProps> = ({
  data = [],
  config,
  title,
  subtitle,
  colorScale = {
    min: '#f0f9ff',
    max: '#1e40af',
    neutral: '#f3f4f6'
  },
  showLegend = true,
  showValues = false,
  showTooltip = true,
  showGrid = true,
  cellSize = 'auto',
  cellShape = 'rounded',
  loading = false,
  error,
  emptyMessage = 'No heatmap data available',
  className = '',
  height = 400,
  width,
  interactive = true,
  onCellClick,
  onCellHover,
  showActions = false,
  onExport,
  onFullscreen,
  aggregation = 'sum',
  threshold,
  showStats = false,
  variant = 'default',
  preset = 'custom',
  filterBy,
  sortBy,
  sortOrder = 'asc',
  showPatterns = false,
  patternDetection = {}
}) => {
  const [hoveredCell, setHoveredCell] = useState<HeatmapDataPoint | null>(null);
  const [selectedCell, setSelectedCell] = useState<HeatmapDataPoint | null>(null);
  const [localFilterBy, setLocalFilterBy] = useState(filterBy || '');
  const [showValuesOverride, setShowValuesOverride] = useState(showValues);

  // Ensure config has valid default values
  const safeConfig = useMemo(() => ({
    xAxis: {
      label: config?.xAxis?.label || 'X Axis',
      type: config?.xAxis?.type || 'category',
      format: config?.xAxis?.format
    },
    yAxis: {
      label: config?.yAxis?.label || 'Y Axis',
      type: config?.yAxis?.type || 'category',
      format: config?.yAxis?.format
    },
    value: {
      label: config?.value?.label || 'Value',
      format: config?.value?.format || 'number',
      precision: config?.value?.precision || 0,
      unit: config?.value?.unit
    }
  }), [config]);

  // Ensure colorScale has valid default values
  const safeColorScale = useMemo(() => ({
    min: colorScale?.min || '#f0f9ff',
    max: colorScale?.max || '#1e40af',
    neutral: colorScale?.neutral || '#f3f4f6',
    steps: colorScale?.steps
  }), [colorScale]);

  // Process and normalize data
  const processedData = useMemo(() => {
    if (!Array.isArray(data)) return [];
    
    let result = [...data];

    // Apply filtering
    if (localFilterBy) {
      result = result.filter(item => 
        item.x.toString().toLowerCase().includes(localFilterBy.toLowerCase()) ||
        item.y.toString().toLowerCase().includes(localFilterBy.toLowerCase())
      );
    }

    // Apply sorting
    if (sortBy) {
      result.sort((a, b) => {
        let aVal, bVal;
        switch (sortBy) {
          case 'x':
            aVal = a.x;
            bVal = b.x;
            break;
          case 'y':
            aVal = a.y;
            bVal = b.y;
            break;
          case 'value':
            aVal = a.value;
            bVal = b.value;
            break;
          default:
            return 0;
        }

        if (typeof aVal === 'string' && typeof bVal === 'string') {
          const comparison = aVal.localeCompare(bVal);
          return sortOrder === 'desc' ? -comparison : comparison;
        }

        const numA = Number(aVal);
        const numB = Number(bVal);
        const comparison = numA - numB;
        return sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return result;
  }, [data, localFilterBy, sortBy, sortOrder]);

  // Get unique X and Y values for grid
  const { xValues, yValues } = useMemo(() => {
    const xSet = new Set(processedData.map(d => d.x));
    const ySet = new Set(processedData.map(d => d.y));
    
    const xArray = Array.from(xSet).sort((a, b) => {
      if (safeConfig.xAxis.type === 'numeric') {
        return Number(a) - Number(b);
      }
      return a.toString().localeCompare(b.toString());
    });
    
    const yArray = Array.from(ySet).sort((a, b) => {
      if (safeConfig.yAxis.type === 'numeric') {
        return Number(a) - Number(b);
      }
      return a.toString().localeCompare(b.toString());
    });

    return { xValues: xArray, yValues: yArray };
  }, [processedData, safeConfig]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (processedData.length === 0) return null;

    const values = processedData.map(d => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const sum = values.reduce((acc, val) => acc + val, 0);
    const avg = sum / values.length;
    const median = [...values].sort((a, b) => a - b)[Math.floor(values.length / 2)];

    // Detect patterns if enabled
    let patterns: any = {};
    if (showPatterns) {
      patterns = detectPatterns(processedData, patternDetection);
    }

    return { min, max, sum, avg, median, count: values.length, patterns };
  }, [processedData, showPatterns, patternDetection]);

  // Create a map for quick data lookup
  const dataMap = useMemo(() => {
    const map = new Map<string, HeatmapDataPoint>();
    processedData.forEach(item => {
      const key = `${item.x}-${item.y}`;
      map.set(key, item);
    });
    return map;
  }, [processedData]);

  // Generate color based on value
  const getColor = (value: number): string => {
    if (stats === null) return safeColorScale.neutral;

    const { min, max } = stats;
    const range = max - min;
    
    if (range === 0) return safeColorScale.min;

    const normalizedValue = Math.max(0, Math.min(1, (value - min) / range));
    
    // Handle threshold highlighting
    if (threshold?.highlight && value >= threshold.highlight) {
      return '#dc2626';
    }

    // Use color steps if provided
    if (safeColorScale.steps && safeColorScale.steps.length > 0) {
      const stepIndex = Math.floor(normalizedValue * (safeColorScale.steps.length - 1));
      return safeColorScale.steps[Math.min(stepIndex, safeColorScale.steps.length - 1)];
    }

    // Interpolate between min and max colors
    return interpolateColor(safeColorScale.min, safeColorScale.max, normalizedValue);
  };

  // Get cell size based on variant and data density
  const getCellSize = (): { width: number; height: number } => {
    const containerWidth = width || 800;
    const containerHeight = height;
    
    const availableWidth = containerWidth - 100;
    const availableHeight = containerHeight - 100;
    
    const cellWidth = Math.max(20, Math.min(60, availableWidth / Math.max(xValues.length, 1)));
    const cellHeight = Math.max(20, Math.min(60, availableHeight / Math.max(yValues.length, 1)));
    
    switch (cellSize) {
      case 'small':
        return { width: Math.min(cellWidth, 30), height: Math.min(cellHeight, 30) };
      case 'medium':
        return { width: Math.min(cellWidth, 45), height: Math.min(cellHeight, 45) };
      case 'large':
        return { width: Math.min(cellWidth, 60), height: Math.min(cellHeight, 60) };
      case 'auto':
      default:
        return { width: cellWidth, height: cellHeight };
    }
  };

  const cellDimensions = getCellSize();

  // Format value for display
  const formatValue = (value: number): string => {
    const precision = safeConfig.value.precision || 0;

    switch (safeConfig.value.format) {
      case 'percentage':
        return `${value.toFixed(precision)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: precision
        }).format(value);
      case 'duration':
        return formatDuration(value);
      case 'number':
      default:
        if (value >= 1000) {
          return value.toLocaleString(undefined, { 
            minimumFractionDigits: precision,
            maximumFractionDigits: precision 
          });
        }
        return value.toFixed(precision);
    }
  };

  // Handle cell interactions
  const handleCellClick = (cellData: HeatmapDataPoint | null) => {
    if (!interactive || !cellData) return;
    setSelectedCell(cellData);
    onCellClick?.(cellData);
  };

  const handleCellHover = (cellData: HeatmapDataPoint | null) => {
    if (!interactive) return;
    setHoveredCell(cellData);
    onCellHover?.(cellData);
  };

  // Custom tooltip component
  const Tooltip = () => {
    if (!showTooltip || !hoveredCell) return null;

    return (
      <div className="absolute z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 pointer-events-none">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {safeConfig.xAxis.label}: {hoveredCell.x}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {safeConfig.yAxis.label}: {hoveredCell.y}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {safeConfig.value.label}:
            </span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {formatValue(hoveredCell.value)}
              {safeConfig.value.unit && ` ${safeConfig.value.unit}`}
            </span>
          </div>
          
          {hoveredCell.metadata && Object.keys(hoveredCell.metadata).length > 0 && (
            <div className="border-t border-gray-200 dark:border-gray-600 pt-2">
              {Object.entries(hoveredCell.metadata).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {key}:
                  </span>
                  <span className="text-xs text-gray-700 dark:text-gray-300">
                    {value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Color legend component
  const ColorLegend = () => {
    if (!showLegend || !stats) return null;

    const legendSteps = 10;
    const step = (stats.max - stats.min) / legendSteps;

    return (
      <div className="flex flex-col space-y-2">
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {safeConfig.value.label}
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {formatValue(stats.min)}
          </span>
          <div className="flex h-4 w-32 rounded overflow-hidden">
            {Array.from({ length: legendSteps }, (_, i) => {
              const value = stats.min + (step * i);
              return (
                <div
                  key={i}
                  className="flex-1"
                  style={{ backgroundColor: getColor(value) }}
                />
              );
            })}
          </div>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {formatValue(stats.max)}
          </span>
        </div>
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse">
          {title && <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>}
          {subtitle && <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>}
          <div className="bg-gray-200 dark:bg-gray-700 rounded" style={{ height }}></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-red-200 dark:border-red-800 ${className}`}>
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Failed to load heatmap
          </h3>
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (processedData.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center">
          <Thermometer className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No data available
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      {(title || subtitle || showActions) && (
        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 mb-6">
          <div className="flex-1">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
          </div>

          {/* Controls */}
          <div className="flex flex-wrap items-center gap-2">
            {/* Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Filter..."
                value={localFilterBy}
                onChange={(e) => setLocalFilterBy(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent w-32"
              />
            </div>

            {/* Toggle Values */}
            <button
              onClick={() => setShowValuesOverride(!showValuesOverride)}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title={showValuesOverride ? 'Hide values' : 'Show values'}
            >
              {showValuesOverride ? (
                <EyeOff className="w-4 h-4 text-gray-500" />
              ) : (
                <Eye className="w-4 h-4 text-gray-500" />
              )}
            </button>

            {/* Reset */}
            {localFilterBy && (
              <button
                onClick={() => setLocalFilterBy('')}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                title="Reset filter"
              >
                <RotateCcw className="w-4 h-4 text-gray-500" />
              </button>
            )}

            {/* Actions */}
            {showActions && (
              <div className="flex items-center gap-1">
                {onExport && (
                  <button
                    onClick={onExport}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Export Chart"
                  >
                    <Download className="w-4 h-4 text-gray-500" />
                  </button>
                )}
                {onFullscreen && (
                  <button
                    onClick={onFullscreen}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Fullscreen"
                  >
                    <Maximize2 className="w-4 h-4 text-gray-500" />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Stats Panel */}
      {showStats && stats && (
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
          <div className="text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400">Total</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatValue(stats.sum)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400">Average</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatValue(stats.avg)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400">Maximum</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatValue(stats.max)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400">Minimum</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {formatValue(stats.min)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400">Data Points</div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {stats.count}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Heatmap */}
        <div className="flex-1">
          <div className="relative overflow-auto" style={{ maxHeight: height }}>
            <svg
              width={xValues.length * cellDimensions.width + 80}
              height={yValues.length * cellDimensions.height + 60}
              className="select-none"
            >
              {/* Y-axis labels */}
              {yValues.map((yValue, yIndex) => (
                <text
                  key={`y-${yIndex}`}
                  x={75}
                  y={yIndex * cellDimensions.height + 40 + cellDimensions.height / 2}
                  textAnchor="end"
                  dominantBaseline="middle"
                  className="text-xs fill-gray-600 dark:fill-gray-400"
                >
                  {yValue}
                </text>
              ))}

              {/* X-axis labels */}
              {xValues.map((xValue, xIndex) => (
                <text
                  key={`x-${xIndex}`}
                  x={xIndex * cellDimensions.width + 80 + cellDimensions.width / 2}
                  y={yValues.length * cellDimensions.height + 55}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="text-xs fill-gray-600 dark:fill-gray-400"
                >
                  {xValue}
                </text>
              ))}

              {/* Grid */}
              {showGrid && (
                <g className="opacity-20">
                  {/* Vertical lines */}
                  {xValues.map((_, xIndex) => (
                    <line
                      key={`v-${xIndex}`}
                      x1={xIndex * cellDimensions.width + 80}
                      y1={30}
                      x2={xIndex * cellDimensions.width + 80}
                      y2={yValues.length * cellDimensions.height + 30}
                      stroke="currentColor"
                      strokeWidth={1}
                    />
                  ))}
                  {/* Horizontal lines */}
                  {yValues.map((_, yIndex) => (
                    <line
                      key={`h-${yIndex}`}
                      x1={80}
                      y1={yIndex * cellDimensions.height + 30}
                      x2={xValues.length * cellDimensions.width + 80}
                      y2={yIndex * cellDimensions.height + 30}
                      stroke="currentColor"
                      strokeWidth={1}
                    />
                  ))}
                </g>
              )}

              {/* Cells */}
              {yValues.map((yValue, yIndex) =>
                xValues.map((xValue, xIndex) => {
                  const cellData = dataMap.get(`${xValue}-${yValue}`);
                  const isHovered = hoveredCell === cellData;
                  const isSelected = selectedCell === cellData;
                  
                  if (!cellData) return null;

                  const cellX = xIndex * cellDimensions.width + 80;
                  const cellY = yIndex * cellDimensions.height + 30;
                  const color = getColor(cellData.value);

                  return (
                    <g key={`cell-${xIndex}-${yIndex}`}>
                      {/* Cell background */}
                      {cellShape === 'circle' ? (
                        <circle
                          cx={cellX + cellDimensions.width / 2}
                          cy={cellY + cellDimensions.height / 2}
                          r={Math.min(cellDimensions.width, cellDimensions.height) / 2 - 2}
                          fill={color}
                          stroke={isSelected ? '#374151' : isHovered ? '#6b7280' : 'none'}
                          strokeWidth={isSelected ? 2 : isHovered ? 1 : 0}
                          className={interactive ? 'cursor-pointer' : ''}
                          onClick={() => handleCellClick(cellData)}
                          onMouseEnter={() => handleCellHover(cellData)}
                          onMouseLeave={() => handleCellHover(null)}
                        />
                      ) : (
                        <rect
                          x={cellX + 1}
                          y={cellY + 1}
                          width={cellDimensions.width - 2}
                          height={cellDimensions.height - 2}
                          fill={color}
                          stroke={isSelected ? '#374151' : isHovered ? '#6b7280' : 'none'}
                          strokeWidth={isSelected ? 2 : isHovered ? 1 : 0}
                          rx={cellShape === 'rounded' ? 4 : 0}
                          className={interactive ? 'cursor-pointer' : ''}
                          onClick={() => handleCellClick(cellData)}
                          onMouseEnter={() => handleCellHover(cellData)}
                          onMouseLeave={() => handleCellHover(null)}
                        />
                      )}

                      {/* Cell value */}
                      {showValuesOverride && (
                        <text
                          x={cellX + cellDimensions.width / 2}
                          y={cellY + cellDimensions.height / 2}
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="text-xs font-medium pointer-events-none"
                          fill={getTextColor(color)}
                        >
                          {cellDimensions.width > 40 ? formatValue(cellData.value) : cellData.value.toFixed(0)}
                        </text>
                      )}
                    </g>
                  );
                })
              )}
            </svg>
          </div>
        </div>

        {/* Legend */}
        {showLegend && (
          <div className="lg:w-48 space-y-4">
            <ColorLegend />
            
            {/* Pattern indicators */}
            {showPatterns && stats?.patterns && Object.keys(stats.patterns).length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Patterns Detected
                </div>
                {Object.entries(stats.patterns).map(([pattern, detected]) => (
                  detected && (
                    <div key={pattern} className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2" />
                      {pattern.charAt(0).toUpperCase() + pattern.slice(1)}
                    </div>
                  )
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      <Tooltip />
    </div>
  );
};

export default HeatmapChart;
export type { HeatmapDataPoint, HeatmapConfig, HeatmapChartProps, ColorScale };