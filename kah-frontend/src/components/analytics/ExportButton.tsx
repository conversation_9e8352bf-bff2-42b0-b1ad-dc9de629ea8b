import React, { useState, useRef } from 'react';
import {
  Download,
  FileText,
  FileSpreadsheet,
  FileImage,
  File,
  Printer,
  Share2,
  <PERSON><PERSON>s,
  CheckCircle,
  AlertCircle,
  Loader2,
  ChevronDown,
  Calendar,
  Filter,
  X
} from 'lucide-react';

export interface ExportFormat {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  extension: string;
  mimeType: string;
  description?: string;
  available?: boolean;
}

export interface ExportOptions {
  format: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeCharts?: boolean;
  includeRawData?: boolean;
  includeMetadata?: boolean;
  compression?: 'none' | 'zip' | 'gzip';
  quality?: 'low' | 'medium' | 'high';
  customFields?: string[];
}

interface ExportProgress {
  status: 'idle' | 'preparing' | 'processing' | 'generating' | 'complete' | 'error';
  progress: number;
  message: string;
  downloadUrl?: string;
  fileName?: string;
}

interface ExportButtonProps {
  data?: any;
  filename?: string;
  title?: string;
  variant?: 'primary' | 'secondary' | 'ghost' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  showDropdown?: boolean;
  showProgress?: boolean;
  formats?: ExportFormat[];
  defaultFormat?: string;
  onExport?: (options: ExportOptions) => Promise<void> | void;
  onFormatChange?: (format: string) => void;
  className?: string;
  children?: React.ReactNode;
  quickExport?: boolean;
  showAdvancedOptions?: boolean;
  allowCustomRange?: boolean;
  allowCustomFields?: boolean;
  presetRanges?: Array<{
    label: string;
    value: string;
    start: Date;
    end: Date;
  }>;
}

const defaultFormats: ExportFormat[] = [
  {
    id: 'csv',
    name: 'CSV',
    icon: FileSpreadsheet,
    extension: 'csv',
    mimeType: 'text/csv',
    description: 'Comma-separated values for spreadsheets',
    available: true
  },
  {
    id: 'xlsx',
    name: 'Excel',
    icon: FileSpreadsheet,
    extension: 'xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    description: 'Microsoft Excel format',
    available: true
  },
  {
    id: 'json',
    name: 'JSON',
    icon: FileText,
    extension: 'json',
    mimeType: 'application/json',
    description: 'JavaScript Object Notation',
    available: true
  },
  {
    id: 'pdf',
    name: 'PDF',
    icon: File,
    extension: 'pdf',
    mimeType: 'application/pdf',
    description: 'Portable Document Format with charts',
    available: true
  },
  {
    id: 'png',
    name: 'PNG',
    icon: FileImage,
    extension: 'png',
    mimeType: 'image/png',
    description: 'Chart as PNG image',
    available: true
  }
];

const ExportButton: React.FC<ExportButtonProps> = ({
  data,
  filename = 'export',
  title = 'Export Data',
  variant = 'secondary',
  size = 'md',
  disabled = false,
  loading = false,
  showDropdown = true,
  showProgress = true,
  formats = defaultFormats,
  defaultFormat = 'csv',
  onExport,
  onFormatChange,
  className = '',
  children,
  quickExport = false,
  showAdvancedOptions = false,
  allowCustomRange = true,
  allowCustomFields = false,
  presetRanges = []
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState(defaultFormat);
  const [progress, setProgress] = useState<ExportProgress>({
    status: 'idle',
    progress: 0,
    message: ''
  });
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: defaultFormat,
    includeCharts: true,
    includeRawData: true,
    includeMetadata: true,
    compression: 'none',
    quality: 'high'
  });

  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Variant styles
  const variantStyles = {
    primary: 'bg-primary-500 hover:bg-primary-600 text-white border-transparent',
    secondary: 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600',
    ghost: 'bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 border-transparent',
    icon: 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 border-transparent p-2'
  };

  // Size styles
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  // Handle quick export
  const handleQuickExport = async () => {
    if (!onExport) return;

    setProgress({
      status: 'preparing',
      progress: 10,
      message: 'Preparing export...'
    });

    try {
      // Simulate progress steps
      setProgress(prev => ({ ...prev, status: 'processing', progress: 30, message: 'Processing data...' }));
      
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProgress(prev => ({ ...prev, status: 'generating', progress: 70, message: 'Generating file...' }));
      
      await onExport({
        format: selectedFormat,
        ...exportOptions
      });

      setProgress({
        status: 'complete',
        progress: 100,
        message: 'Export completed successfully!',
        fileName: `${filename}.${formats.find(f => f.id === selectedFormat)?.extension}`,
        downloadUrl: '#' // Would be actual download URL
      });

      // Reset after success
      setTimeout(() => {
        setProgress({ status: 'idle', progress: 0, message: '' });
      }, 3000);

    } catch (error) {
      setProgress({
        status: 'error',
        progress: 0,
        message: error instanceof Error ? error.message : 'Export failed'
      });

      setTimeout(() => {
        setProgress({ status: 'idle', progress: 0, message: '' });
      }, 5000);
    }
  };

  // Handle format selection
  const handleFormatSelect = (formatId: string) => {
    setSelectedFormat(formatId);
    setExportOptions(prev => ({ ...prev, format: formatId }));
    onFormatChange?.(formatId);
    
    if (quickExport) {
      setIsOpen(false);
      handleQuickExport();
    }
  };

  // Get current format info
  const currentFormat = formats.find(f => f.id === selectedFormat);

  // Progress indicator
  const ProgressIndicator = () => {
    if (!showProgress || progress.status === 'idle') return null;

    const getStatusIcon = () => {
      switch (progress.status) {
        case 'preparing':
        case 'processing':
        case 'generating':
          return <Loader2 className="w-4 h-4 animate-spin" />;
        case 'complete':
          return <CheckCircle className="w-4 h-4 text-green-500" />;
        case 'error':
          return <AlertCircle className="w-4 h-4 text-red-500" />;
        default:
          return null;
      }
    };

    return (
      <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 z-50">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            {getStatusIcon()}
            <span className="text-sm font-medium text-gray-900 dark:text-white ml-2">
              {progress.message}
            </span>
          </div>
          {progress.status !== 'complete' && progress.status !== 'error' && (
            <button
              onClick={() => setProgress({ status: 'idle', progress: 0, message: '' })}
              className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          )}
        </div>
        
        {progress.status !== 'error' && (
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                progress.status === 'complete' ? 'bg-green-500' : 'bg-primary-500'
              }`}
              style={{ width: `${progress.progress}%` }}
            />
          </div>
        )}

        {progress.status === 'complete' && progress.downloadUrl && (
          <a
            href={progress.downloadUrl}
            download={progress.fileName}
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400"
          >
            <Download className="w-4 h-4 mr-1" />
            Download {progress.fileName}
          </a>
        )}
      </div>
    );
  };

  // Render icon-only variant
  if (variant === 'icon') {
    return (
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={quickExport ? handleQuickExport : () => setIsOpen(!isOpen)}
          disabled={disabled || loading || progress.status !== 'idle'}
          className={`
            rounded-lg border transition-colors focus:ring-2 focus:ring-primary-500 focus:border-transparent
            ${variantStyles[variant]} 
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${className}
          `}
          title={title}
        >
          {loading || progress.status !== 'idle' ? (
            <Loader2 className={`${iconSizes[size]} animate-spin`} />
          ) : (
            <Download className={iconSizes[size]} />
          )}
        </button>
        <ProgressIndicator />
      </div>
    );
  }

  // Render full button
  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex">
        {/* Main button */}
        <button
          onClick={quickExport ? handleQuickExport : () => setIsOpen(!isOpen)}
          disabled={disabled || loading || progress.status !== 'idle'}
          className={`
            ${showDropdown ? 'rounded-l-lg border-r-0' : 'rounded-lg'} 
            border transition-colors focus:ring-2 focus:ring-primary-500 focus:border-transparent
            ${variantStyles[variant]} 
            ${sizeStyles[size]}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${className}
          `}
        >
          <div className="flex items-center">
            {loading || progress.status !== 'idle' ? (
              <Loader2 className={`${iconSizes[size]} animate-spin mr-2`} />
            ) : (
              <Download className={`${iconSizes[size]} mr-2`} />
            )}
            {children || (
              <span>
                Export{currentFormat && ` ${currentFormat.name}`}
              </span>
            )}
          </div>
        </button>

        {/* Dropdown trigger */}
        {showDropdown && (
          <button
            onClick={() => setIsOpen(!isOpen)}
            disabled={disabled || loading}
            className={`
              rounded-r-lg border border-l-0 transition-colors focus:ring-2 focus:ring-primary-500 focus:border-transparent
              ${variantStyles[variant]}
              ${sizeStyles[size]}
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              px-2
            `}
          >
            <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>
        )}
      </div>

      {/* Dropdown menu */}
      {isOpen && showDropdown && (
        <div className="absolute top-full left-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-64">
          {/* Format selection */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Export Format</h4>
            <div className="space-y-2">
              {formats.filter(f => f.available !== false).map((format) => {
                const Icon = format.icon;
                return (
                  <button
                    key={format.id}
                    onClick={() => handleFormatSelect(format.id)}
                    className={`w-full flex items-center p-2 rounded-md transition-colors ${
                      selectedFormat === format.id
                        ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">{format.name}</div>
                      {format.description && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {format.description}
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Advanced options */}
          {showAdvancedOptions && (
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center justify-between w-full text-sm font-medium text-gray-900 dark:text-white mb-3"
              >
                Advanced Options
                <ChevronDown className={`w-4 h-4 transition-transform ${showAdvanced ? 'rotate-180' : ''}`} />
              </button>
              
              {showAdvanced && (
                <div className="space-y-3">
                  {/* Include options */}
                  <div>
                    <label className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2 block">
                      Include in Export
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={exportOptions.includeCharts}
                          onChange={(e) => setExportOptions(prev => ({ ...prev, includeCharts: e.target.checked }))}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Charts and visualizations</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={exportOptions.includeRawData}
                          onChange={(e) => setExportOptions(prev => ({ ...prev, includeRawData: e.target.checked }))}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Raw data</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={exportOptions.includeMetadata}
                          onChange={(e) => setExportOptions(prev => ({ ...prev, includeMetadata: e.target.checked }))}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">Metadata and timestamps</span>
                      </label>
                    </div>
                  </div>

                  {/* Quality/Compression */}
                  <div>
                    <label className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2 block">
                      Quality
                    </label>
                    <select
                      value={exportOptions.quality}
                      onChange={(e) => setExportOptions(prev => ({ ...prev, quality: e.target.value as any }))}
                      className="w-full px-3 py-1.5 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="low">Low (faster, smaller file)</option>
                      <option value="medium">Medium (balanced)</option>
                      <option value="high">High (slower, larger file)</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Action buttons */}
          <div className="p-4 flex justify-end space-x-2">
            <button
              onClick={() => setIsOpen(false)}
              className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                setIsOpen(false);
                handleQuickExport();
              }}
              disabled={progress.status !== 'idle'}
              className="px-4 py-1.5 text-sm bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors disabled:opacity-50"
            >
              Export {currentFormat?.name}
            </button>
          </div>
        </div>
      )}

      <ProgressIndicator />
    </div>
  );
};

export default ExportButton;