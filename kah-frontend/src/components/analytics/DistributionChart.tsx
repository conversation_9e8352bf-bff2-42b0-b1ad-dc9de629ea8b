import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveC<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid
} from 'recharts';
import { 
  PieChart as Pie<PERSON>hartIcon, 
  BarChart3, 
  MoreHorizontal, 
  <PERSON><PERSON>dingUp,
  Eye,
  EyeOff,
  Download
} from 'lucide-react';

interface DistributionDataPoint {
  name: string;
  value: number;
  color: string;
  percentage?: number;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    percentage: number;
  };
}

interface DistributionChartProps {
  data: DistributionDataPoint[];
  title?: string;
  subtitle?: string;
  variant?: 'pie' | 'donut' | 'bar' | 'horizontal-bar';
  size?: 'sm' | 'md' | 'lg';
  showValues?: boolean;
  showPercentages?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  showLabels?: boolean;
  showTotals?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  paddingAngle?: number;
  startAngle?: number;
  endAngle?: number;
  loading?: boolean;
  error?: string;
  emptyMessage?: string;
  className?: string;
  animate?: boolean;
  interactive?: boolean;
  onSegmentClick?: (data: DistributionDataPoint, index: number) => void;
  onSegmentHover?: (data: DistributionDataPoint | null, index: number) => void;
  showActions?: boolean;
  onExport?: () => void;
  onToggleView?: () => void;
  customColors?: string[];
  minSliceAngle?: number;
  sortBy?: 'value' | 'name' | 'none';
  sortOrder?: 'asc' | 'desc';
}

const DistributionChart: React.FC<DistributionChartProps> = ({
  data,
  title,
  subtitle,
  variant = 'donut',
  size = 'md',
  showValues = true,
  showPercentages = true,
  showLegend = true,
  showTooltip = true,
  showLabels = false,
  showTotals = true,
  innerRadius,
  outerRadius,
  paddingAngle = 2,
  startAngle = 90,
  endAngle = 450,
  loading = false,
  error,
  emptyMessage = 'No data to display',
  className = '',
  animate = true,
  interactive = true,
  onSegmentClick,
  onSegmentHover,
  showActions = false,
  onExport,
  onToggleView,
  customColors,
  minSliceAngle = 2,
  sortBy = 'none',
  sortOrder = 'desc'
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [hiddenSegments, setHiddenSegments] = useState<Set<number>>(new Set());

  // Size configurations
  const sizeConfigs = {
    sm: {
      container: 'h-64',
      innerRadius: innerRadius ?? (variant === 'donut' ? 40 : 0),
      outerRadius: outerRadius ?? 80,
      legendSize: 'text-xs',
      titleSize: 'text-base',
      totalSize: 'text-lg'
    },
    md: {
      container: 'h-80',
      innerRadius: innerRadius ?? (variant === 'donut' ? 60 : 0),
      outerRadius: outerRadius ?? 100,
      legendSize: 'text-sm',
      titleSize: 'text-lg',
      totalSize: 'text-xl'
    },
    lg: {
      container: 'h-96',
      innerRadius: innerRadius ?? (variant === 'donut' ? 80 : 0),
      outerRadius: outerRadius ?? 120,
      legendSize: 'text-base',
      titleSize: 'text-xl',
      totalSize: 'text-2xl'
    }
  };

  const config = sizeConfigs[size];

  // Sort and process data
  const processedData = React.useMemo(() => {
    let sortedData = [...data];
    
    // Apply sorting
    if (sortBy !== 'none') {
      sortedData.sort((a, b) => {
        let comparison = 0;
        if (sortBy === 'value') {
          comparison = a.value - b.value;
        } else if (sortBy === 'name') {
          comparison = a.name.localeCompare(b.name);
        }
        return sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    // Calculate percentages and apply custom colors
    const total = sortedData.reduce((sum, item) => sum + item.value, 0);
    
    return sortedData.map((item, index) => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0,
      color: customColors?.[index] || item.color,
      originalIndex: data.findIndex(d => d.name === item.name)
    }));
  }, [data, sortBy, sortOrder, customColors]);

  // Filter out hidden segments and small slices
  const visibleData = processedData.filter((item, index) => {
    if (hiddenSegments.has(index)) return false;
    return item.percentage >= minSliceAngle;
  });

  // Calculate totals
  const totalValue = visibleData.reduce((sum, item) => sum + item.value, 0);
  const totalItems = visibleData.length;

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) return null;

    const data = payload[0].payload;
    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
        <div className="flex items-center mb-2">
          <div 
            className="w-3 h-3 rounded-full mr-2"
            style={{ backgroundColor: data.color }}
          />
          <span className="font-medium text-gray-900 dark:text-white">
            {data.name}
          </span>
        </div>
        <div className="space-y-1">
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Value:</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {data.value.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-gray-600 dark:text-gray-400">Percentage:</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {data.percentage.toFixed(1)}%
            </span>
          </div>
          {data.trend && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Trend:</span>
              <span className={`text-sm font-medium ${
                data.trend.direction === 'up' ? 'text-green-600 dark:text-green-400' :
                data.trend.direction === 'down' ? 'text-red-600 dark:text-red-400' :
                'text-gray-600 dark:text-gray-400'
              }`}>
                {data.trend.direction !== 'neutral' && (data.trend.percentage > 0 ? '+' : '')}{data.trend.percentage}%
              </span>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Custom legend
  const CustomLegend = ({ payload }: any) => {
    if (!payload) return null;

    return (
      <div className="flex flex-wrap gap-2 justify-center mt-4">
        {payload.map((entry: any, index: number) => {
          const isHidden = hiddenSegments.has(index);
          return (
            <button
              key={index}
              className={`flex items-center px-2 py-1 rounded-md transition-colors ${
                isHidden 
                  ? 'opacity-50 bg-gray-100 dark:bg-gray-700' 
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => {
                if (interactive) {
                  const newHidden = new Set(hiddenSegments);
                  if (isHidden) {
                    newHidden.delete(index);
                  } else {
                    newHidden.add(index);
                  }
                  setHiddenSegments(newHidden);
                }
              }}
            >
              <div 
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className={`${config.legendSize} ${
                isHidden ? 'line-through text-gray-400' : 'text-gray-700 dark:text-gray-300'
              }`}>
                {entry.value}
              </span>
              {showPercentages && (
                <span className={`${config.legendSize} ml-1 ${
                  isHidden ? 'text-gray-400' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  ({entry.payload.percentage.toFixed(1)}%)
                </span>
              )}
            </button>
          );
        })}
      </div>
    );
  };

  // Handle segment interactions
  const handleSegmentClick = (data: any, index: number) => {
    if (!interactive) return;
    
    setActiveIndex(activeIndex === index ? null : index);
    onSegmentClick?.(data, index);
  };

  const handleSegmentHover = (data: any, index: number) => {
    if (!interactive) return;
    
    onSegmentHover?.(data, index);
  };

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse">
          {title && <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>}
          {subtitle && <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>}
          <div className={`bg-gray-200 dark:bg-gray-700 rounded-full mx-auto ${config.container}`} 
               style={{ width: config.outerRadius * 2, height: config.outerRadius * 2 }}></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-red-200 dark:border-red-800 ${className}`}>
        <div className="text-center">
          <PieChartIcon className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Failed to load chart
          </h3>
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center">
          <PieChartIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No data available
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  // Render bar chart variant
  if (variant === 'bar' || variant === 'horizontal-bar') {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        {/* Header */}
        {(title || subtitle || showActions) && (
          <div className="flex items-start justify-between mb-6">
            <div>
              {title && (
                <h3 className={`${config.titleSize} font-semibold text-gray-900 dark:text-white`}>
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {subtitle}
                </p>
              )}
            </div>
            {showActions && (
              <div className="flex items-center gap-2">
                {onToggleView && (
                  <button
                    onClick={onToggleView}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Toggle View"
                  >
                    <PieChartIcon className="w-4 h-4 text-gray-500" />
                  </button>
                )}
                {onExport && (
                  <button
                    onClick={onExport}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="Export Chart"
                  >
                    <Download className="w-4 h-4 text-gray-500" />
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        <div className={config.container}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart 
              data={visibleData}
              layout={variant === 'horizontal-bar' ? 'horizontal' : 'vertical'}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              {variant === 'horizontal-bar' ? (
                <>
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={100} />
                </>
              ) : (
                <>
                  <XAxis dataKey="name" />
                  <YAxis />
                </>
              )}
              {showTooltip && <Tooltip content={<CustomTooltip />} />}
              <Bar 
                dataKey="value" 
                fill={(data: any) => data.color}
                onClick={handleSegmentClick}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    );
  }

  // Render pie/donut chart
  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      {(title || subtitle || showActions) && (
        <div className="flex items-start justify-between mb-6">
          <div>
            {title && (
              <h3 className={`${config.titleSize} font-semibold text-gray-900 dark:text-white`}>
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {showActions && (
            <div className="flex items-center gap-2">
              {onToggleView && (
                <button
                  onClick={onToggleView}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Toggle View"
                >
                  <BarChart3 className="w-4 h-4 text-gray-500" />
                </button>
              )}
              {onExport && (
                <button
                  onClick={onExport}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Export Chart"
                >
                  <Download className="w-4 h-4 text-gray-500" />
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Chart Container */}
      <div className="relative">
        <div className={config.container}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={visibleData}
                cx="50%"
                cy="50%"
                innerRadius={config.innerRadius}
                outerRadius={config.outerRadius}
                paddingAngle={paddingAngle}
                startAngle={startAngle}
                endAngle={endAngle}
                dataKey="value"
                onClick={handleSegmentClick}
                onMouseEnter={handleSegmentHover}
                onMouseLeave={() => handleSegmentHover(null, -1)}
                isAnimationActive={animate}
              >
                {visibleData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color}
                    stroke={activeIndex === index ? "#374151" : "none"}
                    strokeWidth={activeIndex === index ? 2 : 0}
                    style={{ 
                      filter: activeIndex === index ? 'brightness(1.1)' : 'none',
                      cursor: interactive ? 'pointer' : 'default'
                    }}
                  />
                ))}
              </Pie>
              {showTooltip && <Tooltip content={<CustomTooltip />} />}
              {showLegend && <Legend content={<CustomLegend />} />}
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Center Total (for donut charts) */}
        {variant === 'donut' && showTotals && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-center">
              <div className={`${config.totalSize} font-bold text-gray-900 dark:text-white`}>
                {totalValue.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {totalItems} item{totalItems !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DistributionChart;

// Export types
export type { DistributionDataPoint, DistributionChartProps };