import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  Legend,
  ReferenceLine,
  Area,
  ComposedChart,
  Bar
} from 'recharts';
import { TrendingUp, TrendingDown, Activity, MoreHorizontal } from 'lucide-react';

interface TrendDataPoint {
  time: string;
  [key: string]: number | string;
}

interface LineConfig {
  dataKey: string;
  name: string;
  color: string;
  strokeWidth?: number;
  strokeDasharray?: string;
  type?: 'monotone' | 'linear' | 'step';
  hide?: boolean;
}

interface TrendChartProps {
  data: TrendDataPoint[];
  lines: LineConfig[];
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  timeFormat?: 'HH:mm' | 'MM/DD' | 'DD/MM' | 'auto';
  yAxisFormat?: 'number' | 'percentage' | 'currency';
  title?: string;
  subtitle?: string;
  loading?: boolean;
  error?: string;
  showTrendIndicator?: boolean;
  showReferenceLine?: boolean;
  referenceValue?: number;
  referenceLabel?: string;
  variant?: 'line' | 'area' | 'composed';
  smoothCurve?: boolean;
  showPoints?: boolean;
  animate?: boolean;
  onDataPointClick?: (data: any, index: number) => void;
  className?: string;
  emptyMessage?: string;
  showActions?: boolean;
  onExport?: () => void;
  onFullscreen?: () => void;
}

const TrendChart: React.FC<TrendChartProps> = ({
  data,
  lines,
  height = 300,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  timeFormat = 'auto',
  yAxisFormat = 'number',
  title,
  subtitle,
  loading = false,
  error,
  showTrendIndicator = false,
  showReferenceLine = false,
  referenceValue,
  referenceLabel = 'Target',
  variant = 'line',
  smoothCurve = true,
  showPoints = false,
  animate = true,
  onDataPointClick,
  className = '',
  emptyMessage = 'No data available',
  showActions = false,
  onExport,
  onFullscreen
}) => {
  // Calculate trend for indicator
  const calculateTrend = () => {
    if (!data.length || !lines.length) return null;
    
    const primaryLine = lines[0];
    const firstValue = data[0][primaryLine.dataKey] as number;
    const lastValue = data[data.length - 1][primaryLine.dataKey] as number;
    
    if (typeof firstValue !== 'number' || typeof lastValue !== 'number') return null;
    
    const change = lastValue - firstValue;
    const percentage = firstValue !== 0 ? (change / firstValue) * 100 : 0;
    
    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral',
      percentage: Math.abs(percentage),
      change
    };
  };

  // Format Y-axis values
  const formatYAxisValue = (value: number) => {
    switch (yAxisFormat) {
      case 'percentage':
        return `${value}%`;
      case 'currency':
        return `$${value.toLocaleString()}`;
      case 'number':
      default:
        if (value >= 1000) {
          return `${(value / 1000).toFixed(1)}k`;
        }
        return value.toString();
    }
  };

  // Format tooltip values
  const formatTooltipValue = (value: number, name: string) => {
    switch (yAxisFormat) {
      case 'percentage':
        return [`${value}%`, name];
      case 'currency':
        return [`$${value.toLocaleString()}`, name];
      case 'number':
      default:
        return [value.toLocaleString(), name];
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
        <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          {label}
        </p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between min-w-[120px]">
            <div className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {entry.name}:
              </span>
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-white ml-2">
              {formatTooltipValue(entry.value, entry.name)[0]}
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Trend indicator
  const trend = showTrendIndicator ? calculateTrend() : null;

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="animate-pulse">
          {title && <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>}
          {subtitle && <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>}
          <div className={`bg-gray-200 dark:bg-gray-700 rounded`} style={{ height: `${height}px` }}></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-red-200 dark:border-red-800 ${className}`}>
        <div className="text-center">
          <TrendingDown className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Failed to load chart data
          </h3>
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="text-center">
          <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No data available
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  // Chart component selection
  const ChartComponent = variant === 'composed' ? ComposedChart : LineChart;
  const curveType = smoothCurve ? 'monotone' : 'linear';

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      {(title || subtitle || showTrendIndicator || showActions) && (
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {subtitle}
              </p>
            )}
            
            {/* Trend Indicator */}
            {showTrendIndicator && trend && (
              <div className="flex items-center mt-2">
                {trend.direction === 'up' ? (
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                ) : trend.direction === 'down' ? (
                  <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                ) : (
                  <Activity className="w-4 h-4 text-gray-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  trend.direction === 'up' ? 'text-green-600 dark:text-green-400' :
                  trend.direction === 'down' ? 'text-red-600 dark:text-red-400' :
                  'text-gray-600 dark:text-gray-400'
                }`}>
                  {trend.direction !== 'neutral' && (trend.change > 0 ? '+' : '')}{trend.percentage.toFixed(1)}%
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                  vs previous period
                </span>
              </div>
            )}
          </div>
          
          {/* Actions */}
          {showActions && (
            <div className="flex items-center gap-2 ml-4">
              {onExport && (
                <button
                  onClick={onExport}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Export Chart"
                >
                  <TrendingUp className="w-4 h-4 text-gray-500" />
                </button>
              )}
              {onFullscreen && (
                <button
                  onClick={onFullscreen}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  title="Fullscreen"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500" />
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Chart */}
      <ResponsiveContainer width="100%" height={height}>
        <ChartComponent 
          data={data}
          onClick={onDataPointClick}
        >
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              className="opacity-30"
              stroke="currentColor"
            />
          )}
          
          <XAxis 
            dataKey="time"
            className="text-xs"
            tick={{ 
              fill: 'currentColor', 
              fontSize: 12 
            }}
            axisLine={{ stroke: 'currentColor', strokeWidth: 1 }}
            tickLine={{ stroke: 'currentColor' }}
          />
          
          <YAxis 
            className="text-xs"
            tick={{ 
              fill: 'currentColor', 
              fontSize: 12 
            }}
            tickFormatter={formatYAxisValue}
            axisLine={{ stroke: 'currentColor', strokeWidth: 1 }}
            tickLine={{ stroke: 'currentColor' }}
          />
          
          {showTooltip && (
            <Tooltip 
              content={<CustomTooltip />}
            />
          )}
          
          {showLegend && <Legend />}
          
          {/* Reference Line */}
          {showReferenceLine && referenceValue && (
            <ReferenceLine 
              y={referenceValue} 
              stroke="#94a3b8" 
              strokeDasharray="5 5"
              label={{ value: referenceLabel, position: 'right' }}
            />
          )}
          
          {/* Lines */}
          {lines.map((line) => {
            if (line.hide) return null;
            
            if (variant === 'area') {
              return (
                <Area
                  key={line.dataKey}
                  type={line.type || curveType}
                  dataKey={line.dataKey}
                  stroke={line.color}
                  fill={line.color}
                  fillOpacity={0.1}
                  strokeWidth={line.strokeWidth || 2}
                  strokeDasharray={line.strokeDasharray}
                  name={line.name}
                  dot={showPoints}
                  isAnimationActive={animate}
                />
              );
            }
            
            if (variant === 'composed' && line.dataKey.includes('bar')) {
              return (
                <Bar
                  key={line.dataKey}
                  dataKey={line.dataKey}
                  fill={line.color}
                  name={line.name}
                  opacity={0.7}
                />
              );
            }
            
            return (
              <Line
                key={line.dataKey}
                type={line.type || curveType}
                dataKey={line.dataKey}
                stroke={line.color}
                strokeWidth={line.strokeWidth || 2}
                strokeDasharray={line.strokeDasharray}
                name={line.name}
                dot={showPoints}
                isAnimationActive={animate}
              />
            );
          })}
        </ChartComponent>
      </ResponsiveContainer>
    </div>
  );
};

export default TrendChart;

// Export types
export type { TrendDataPoint, LineConfig, TrendChartProps };