import React, { forwardRef } from 'react';
import { Loader2 } from 'lucide-react';

// Button variant types
type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'danger' 
  | 'ghost' 
  | 'person' 
  | 'vehicle' 
  | 'object' 
  | 'zone';

type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Base button props
interface BaseButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

// Polymorphic button props - can render as button, a, or Link
type ButtonAsButton = BaseButtonProps & 
  Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, keyof BaseButtonProps> & {
    as?: 'button';
  };

type ButtonAsAnchor = BaseButtonProps & 
  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof BaseButtonProps> & {
    as: 'a';
    href: string;
  };

type ButtonAsLink = BaseButtonProps & {
  as: 'link';
  to: string;
  [key: string]: any; // For router link props
};

type ButtonProps = ButtonAsButton | ButtonAsAnchor | ButtonAsLink;

// Variant styles using Tailwind classes
const variantStyles: Record<ButtonVariant, string> = {
  primary: `
    bg-primary-500 hover:bg-primary-600 active:bg-primary-700
    text-white border border-primary-500 hover:border-primary-600
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-primary-600 dark:hover:bg-primary-700 dark:border-primary-600
  `,
  secondary: `
    bg-white hover:bg-gray-50 active:bg-gray-100
    text-gray-700 border border-gray-300 hover:border-gray-400
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-200 
    dark:border-gray-600 dark:hover:border-gray-500
  `,
  danger: `
    bg-red-500 hover:bg-red-600 active:bg-red-700
    text-white border border-red-500 hover:border-red-600
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-red-600 dark:hover:bg-red-700 dark:border-red-600
  `,
  ghost: `
    bg-transparent hover:bg-gray-100 active:bg-gray-200
    text-gray-700 border border-transparent hover:border-gray-300
    dark:hover:bg-gray-800 dark:text-gray-300 dark:hover:border-gray-600
  `,
  person: `
    bg-green-500 hover:bg-green-600 active:bg-green-700
    text-white border border-green-500 hover:border-green-600
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-green-600 dark:hover:bg-green-700 dark:border-green-600
  `,
  vehicle: `
    bg-orange-500 hover:bg-orange-600 active:bg-orange-700
    text-white border border-orange-500 hover:border-orange-600
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-orange-600 dark:hover:bg-orange-700 dark:border-orange-600
  `,
  object: `
    bg-purple-500 hover:bg-purple-600 active:bg-purple-700
    text-white border border-purple-500 hover:border-purple-600
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-purple-600 dark:hover:bg-purple-700 dark:border-purple-600
  `,
  zone: `
    bg-cyan-500 hover:bg-cyan-600 active:bg-cyan-700
    text-white border border-cyan-500 hover:border-cyan-600
    shadow-sm hover:shadow focus:shadow-md
    dark:bg-cyan-600 dark:hover:bg-cyan-700 dark:border-cyan-600
  `,
};

// Size styles
const sizeStyles: Record<ButtonSize, string> = {
  xs: 'h-6 px-2 text-xs font-medium gap-1',
  sm: 'h-8 px-3 text-sm font-medium gap-1.5',
  md: 'h-10 px-4 text-sm font-medium gap-2',
  lg: 'h-12 px-6 text-base font-medium gap-2',
  xl: 'h-14 px-8 text-lg font-medium gap-2.5',
};

// Disabled styles
const disabledStyles = `
  opacity-50 cursor-not-allowed pointer-events-none
  dark:opacity-40
`;

// Loading styles
const loadingStyles = `
  cursor-wait pointer-events-none
`;

// Base button classes
const baseStyles = `
  inline-flex items-center justify-center
  rounded-md font-medium
  transition-all duration-200 ease-in-out
  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
  dark:focus:ring-offset-gray-900
  disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none
  active:scale-[0.98] hover:scale-[1.02]
  select-none
`;

// High contrast mode styles
const highContrastStyles = `
  contrast-more:border-2 contrast-more:border-current
  contrast-more:bg-transparent contrast-more:text-current
`;

/**
 * Button Component
 * 
 * A versatile button component that supports multiple variants, sizes, and states.
 * Can be rendered as a button, anchor, or router link element.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Loading state with spinner
 * - Left and right icon support
 * - Full width option
 * - Polymorphic rendering (button/a/Link)
 * - Theme-aware styling
 * - Accessibility compliant
 * 
 * @example
 * ```tsx
 * // Basic button
 * <Button variant="primary" size="md">Save Changes</Button>
 * 
 * // Loading button
 * <Button variant="primary" loading>Processing...</Button>
 * 
 * // Detection-themed button
 * <Button variant="person" leftIcon={<UserIcon />}>
 *   Track People
 * </Button>
 * 
 * // As anchor link
 * <Button as="a" href="/help" variant="ghost">
 *   Learn More
 * </Button>
 * ```
 */
const Button = forwardRef<
  HTMLButtonElement | HTMLAnchorElement,
  ButtonProps
>(({ 
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  children,
  className = '',
  as = 'button',
  ...props
}, ref) => {
  // Combine all styles
  const combinedClassName = [
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    highContrastStyles,
    disabled && disabledStyles,
    loading && loadingStyles,
    fullWidth && 'w-full',
    className,
  ]
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Determine if button should be disabled
  const isDisabled = disabled || loading;

  // Loading spinner component
  const LoadingSpinner = () => (
    <Loader2 
      className={`animate-spin ${
        size === 'xs' ? 'h-3 w-3' : 
        size === 'sm' ? 'h-3.5 w-3.5' : 
        size === 'md' ? 'h-4 w-4' : 
        size === 'lg' ? 'h-4 w-4' : 
        'h-5 w-5'
      }`}
      aria-hidden="true"
    />
  );

  // Button content with icons
  const ButtonContent = () => (
    <>
      {loading && <LoadingSpinner />}
      {!loading && leftIcon && (
        <span className="flex-shrink-0" aria-hidden="true">
          {leftIcon}
        </span>
      )}
      <span className={loading ? 'opacity-70' : ''}>
        {children}
      </span>
      {!loading && rightIcon && (
        <span className="flex-shrink-0" aria-hidden="true">
          {rightIcon}
        </span>
      )}
    </>
  );

  // Render as anchor
  if (as === 'a') {
    const { href, ...anchorProps } = props as ButtonAsAnchor;
    return (
      <a
        ref={ref as React.ForwardedRef<HTMLAnchorElement>}
        href={isDisabled ? undefined : href}
        className={combinedClassName}
        aria-disabled={isDisabled}
        tabIndex={isDisabled ? -1 : undefined}
        {...anchorProps}
      >
        <ButtonContent />
      </a>
    );
  }

  // Render as router link (assuming React Router)
  if (as === 'link') {
    const { to, ...linkProps } = props as ButtonAsLink;
    // Note: In a real implementation, you'd import Link from your router
    // For now, we'll render as a button with data attribute
    return (
      <button
        ref={ref as React.ForwardedRef<HTMLButtonElement>}
        type="button"
        disabled={isDisabled}
        className={combinedClassName}
        data-link-to={to}
        aria-disabled={isDisabled}
        {...linkProps}
      >
        <ButtonContent />
      </button>
    );
  }

  // Render as button (default)
  const { type = 'button', ...buttonProps } = props as ButtonAsButton;
  return (
    <button
      ref={ref as React.ForwardedRef<HTMLButtonElement>}
      type={type}
      disabled={isDisabled}
      className={combinedClassName}
      aria-disabled={isDisabled}
      {...buttonProps}
    >
      <ButtonContent />
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
export type { ButtonProps, ButtonVariant, ButtonSize };