import React, { forwardRef, useState, useRef, useEffect, useCallback, useId } from 'react';
import { 
  Info,
  HelpCircle,
  AlertCircle,
  CheckCircle,
  AlertTriangle,
  Zap,
  Eye,
  Camera,
  Settings,
  Users,
  Car,
  Box,
  MapPin,
  TrendingUp,
  BarChart3,
  Clock,
  Shield,
  Bell
} from 'lucide-react';

// Tooltip variant types
type TooltipVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'detection'
  | 'dark'
  | 'light';

type TooltipSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type TooltipPlacement = 
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end';

type TooltipTrigger = 'hover' | 'focus' | 'click' | 'manual';

// Help content presets for VisionGuard features
const helpPresets = {
  confidence: {
    title: 'Detection Confidence',
    content: 'The minimum confidence level required for the AI to report a detection. Higher values reduce false positives but may miss some detections.',
    icon: <Eye className="w-4 h-4" />,
    variant: 'detection' as const,
  },
  sensitivity: {
    title: 'Motion Sensitivity',
    content: 'Controls how sensitive the system is to motion changes. Higher values detect smaller movements but may increase false alarms.',
    icon: <Zap className="w-4 h-4" />,
    variant: 'detection' as const,
  },
  zones: {
    title: 'Detection Zones',
    content: 'Define specific areas where object detection should occur. This helps reduce false positives from irrelevant movement.',
    icon: <MapPin className="w-4 h-4" />,
    variant: 'detection' as const,
  },
  cameras: {
    title: 'Camera Management',
    content: 'Add, configure, and monitor your security cameras. Each camera can have individual settings and detection zones.',
    icon: <Camera className="w-4 h-4" />,
    variant: 'primary' as const,
  },
  analytics: {
    title: 'Analytics Dashboard',
    content: 'View detection trends, traffic patterns, and system performance metrics over time.',
    icon: <BarChart3 className="w-4 h-4" />,
    variant: 'info' as const,
  },
  recording: {
    title: 'Recording Settings',
    content: 'Configure automatic recording triggers, storage duration, and video quality settings.',
    icon: <Camera className="w-4 h-4" />,
    variant: 'primary' as const,
  },
  alerts: {
    title: 'Alert Notifications',
    content: 'Set up notifications for detection events, system issues, and maintenance reminders.',
    icon: <Bell className="w-4 h-4" />,
    variant: 'warning' as const,
  },
  privacy: {
    title: 'Privacy Protection',
    content: 'Enable privacy features like face blurring, restricted zones, and data anonymization.',
    icon: <Shield className="w-4 h-4" />,
    variant: 'success' as const,
  },
} as const;

// Base tooltip props
interface BaseTooltipProps {
  variant?: TooltipVariant;
  size?: TooltipSize;
  placement?: TooltipPlacement;
  trigger?: TooltipTrigger | TooltipTrigger[];
  content?: React.ReactNode;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  showArrow?: boolean;
  delay?: number;
  hideDelay?: number;
  offset?: [number, number];
  maxWidth?: number;
  interactive?: boolean;
  disabled?: boolean;
  open?: boolean;
  defaultOpen?: boolean;
  className?: string;
  tooltipClassName?: string;
  children: React.ReactNode;
  onOpenChange?: (open: boolean) => void;
}

type TooltipProps = BaseTooltipProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseTooltipProps>;

// Variant styles
const variantStyles: Record<TooltipVariant, string> = {
  default: `
    bg-gray-900 text-white border-gray-700
    dark:bg-gray-100 dark:text-gray-900 dark:border-gray-300
  `,
  primary: `
    bg-primary-600 text-white border-primary-500
    dark:bg-primary-700 dark:border-primary-600
  `,
  secondary: `
    bg-gray-600 text-white border-gray-500
    dark:bg-gray-300 dark:text-gray-900 dark:border-gray-400
  `,
  success: `
    bg-green-600 text-white border-green-500
    dark:bg-green-700 dark:border-green-600
  `,
  warning: `
    bg-yellow-600 text-white border-yellow-500
    dark:bg-yellow-700 dark:border-yellow-600
  `,
  error: `
    bg-red-600 text-white border-red-500
    dark:bg-red-700 dark:border-red-600
  `,
  info: `
    bg-blue-600 text-white border-blue-500
    dark:bg-blue-700 dark:border-blue-600
  `,
  detection: `
    bg-cyan-600 text-white border-cyan-500
    dark:bg-cyan-700 dark:border-cyan-600
  `,
  dark: `
    bg-gray-900 text-white border-gray-700
  `,
  light: `
    bg-white text-gray-900 border-gray-200
    shadow-lg
  `,
};

// Size styles
const sizeStyles: Record<TooltipSize, { tooltip: string; text: string; icon: string; arrow: string }> = {
  xs: {
    tooltip: 'px-2 py-1',
    text: 'text-xs',
    icon: 'w-3 h-3',
    arrow: 'w-2 h-2',
  },
  sm: {
    tooltip: 'px-2.5 py-1.5',
    text: 'text-sm',
    icon: 'w-3.5 h-3.5',
    arrow: 'w-2.5 h-2.5',
  },
  md: {
    tooltip: 'px-3 py-2',
    text: 'text-sm',
    icon: 'w-4 h-4',
    arrow: 'w-3 h-3',
  },
  lg: {
    tooltip: 'px-4 py-2.5',
    text: 'text-base',
    icon: 'w-5 h-5',
    arrow: 'w-3.5 h-3.5',
  },
  xl: {
    tooltip: 'px-5 py-3',
    text: 'text-lg',
    icon: 'w-6 h-6',
    arrow: 'w-4 h-4',
  },
};

// Base tooltip styles
const baseTooltipStyles = `
  absolute z-50 rounded-md border
  transition-all duration-200 ease-out
  transform origin-center
  max-w-xs break-words
`;

// Arrow styles for different placements
const arrowStyles = {
  top: 'bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full',
  'top-start': 'bottom-0 left-2 transform translate-y-full',
  'top-end': 'bottom-0 right-2 transform translate-y-full',
  bottom: 'top-0 left-1/2 transform -translate-x-1/2 -translate-y-full',
  'bottom-start': 'top-0 left-2 transform -translate-y-full',
  'bottom-end': 'top-0 right-2 transform -translate-y-full',
  left: 'right-0 top-1/2 transform translate-x-full -translate-y-1/2',
  'left-start': 'right-0 top-2 transform translate-x-full',
  'left-end': 'right-0 bottom-2 transform translate-x-full',
  right: 'left-0 top-1/2 transform -translate-x-full -translate-y-1/2',
  'right-start': 'left-0 top-2 transform -translate-x-full',
  'right-end': 'left-0 bottom-2 transform -translate-x-full',
};

/**
 * Tooltip Component
 * 
 * A versatile tooltip component for the VisionGuard Detection Analytics platform.
 * Provides contextual help, information, and guidance for detection features.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Flexible placement options
 * - Multiple trigger types (hover, focus, click, manual)
 * - Rich content with icons and descriptions
 * - Help presets for common VisionGuard features
 * - Interactive tooltips for complex content
 * - Customizable delays and animations
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Basic help tooltip
 * <Tooltip content="This setting controls detection accuracy">
 *   <Button>Detection Settings</Button>
 * </Tooltip>
 * 
 * // Rich tooltip with icon and description
 * <Tooltip 
 *   title="Detection Confidence"
 *   description="Higher values reduce false positives but may miss some detections"
 *   icon={<Eye />}
 *   variant="detection"
 *   placement="top"
 * >
 *   <Slider value={confidence} onChange={setConfidence} />
 * </Tooltip>
 * 
 * // Interactive tooltip
 * <Tooltip 
 *   interactive
 *   trigger="click"
 *   content={
 *     <div>
 *       <p>Complex help content</p>
 *       <Button size="sm">Learn More</Button>
 *     </div>
 *   }
 * >
 *   <HelpCircle className="w-4 h-4" />
 * </Tooltip>
 * 
 * // Help preset
 * <HelpTooltip preset="confidence">
 *   <Slider type="confidence" />
 * </HelpTooltip>
 * ```
 */
const Tooltip = forwardRef<HTMLDivElement, TooltipProps>(({
  variant = 'default',
  size = 'md',
  placement = 'top',
  trigger = 'hover',
  content,
  title,
  description,
  icon,
  showArrow = true,
  delay = 500,
  hideDelay = 200,
  offset = [0, 8],
  maxWidth = 320,
  interactive = false,
  disabled = false,
  open: controlledOpen,
  defaultOpen = false,
  className = '',
  tooltipClassName = '',
  children,
  onOpenChange,
  ...props
}, ref) => {
  const [internalOpen, setInternalOpen] = useState(defaultOpen);
  const [isVisible, setIsVisible] = useState(false);
  
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tooltipId = useId();

  // Current open state (controlled or uncontrolled)
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;

  // Trigger types array
  const triggerTypes = Array.isArray(trigger) ? trigger : [trigger];

  // Calculate tooltip position
  const getTooltipPosition = useCallback(() => {
    if (!triggerRef.current || !tooltipRef.current) return { top: 0, left: 0 };

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const [offsetX, offsetY] = offset;

    const positions = {
      top: {
        top: triggerRect.top - tooltipRect.height - offsetY,
        left: triggerRect.left + (triggerRect.width - tooltipRect.width) / 2,
      },
      'top-start': {
        top: triggerRect.top - tooltipRect.height - offsetY,
        left: triggerRect.left + offsetX,
      },
      'top-end': {
        top: triggerRect.top - tooltipRect.height - offsetY,
        left: triggerRect.right - tooltipRect.width - offsetX,
      },
      bottom: {
        top: triggerRect.bottom + offsetY,
        left: triggerRect.left + (triggerRect.width - tooltipRect.width) / 2,
      },
      'bottom-start': {
        top: triggerRect.bottom + offsetY,
        left: triggerRect.left + offsetX,
      },
      'bottom-end': {
        top: triggerRect.bottom + offsetY,
        left: triggerRect.right - tooltipRect.width - offsetX,
      },
      left: {
        top: triggerRect.top + (triggerRect.height - tooltipRect.height) / 2,
        left: triggerRect.left - tooltipRect.width - offsetX,
      },
      'left-start': {
        top: triggerRect.top + offsetY,
        left: triggerRect.left - tooltipRect.width - offsetX,
      },
      'left-end': {
        top: triggerRect.bottom - tooltipRect.height - offsetY,
        left: triggerRect.left - tooltipRect.width - offsetX,
      },
      right: {
        top: triggerRect.top + (triggerRect.height - tooltipRect.height) / 2,
        left: triggerRect.right + offsetX,
      },
      'right-start': {
        top: triggerRect.top + offsetY,
        left: triggerRect.right + offsetX,
      },
      'right-end': {
        top: triggerRect.bottom - tooltipRect.height - offsetY,
        left: triggerRect.right + offsetX,
      },
    };

    return positions[placement];
  }, [placement, offset]);

  // Open tooltip
  const openTooltip = useCallback(() => {
    if (disabled) return;
    
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }

    timeoutRef.current = setTimeout(() => {
      if (controlledOpen === undefined) {
        setInternalOpen(true);
      }
      onOpenChange?.(true);
      setIsVisible(true);
    }, delay);
  }, [disabled, delay, controlledOpen, onOpenChange]);

  // Close tooltip
  const closeTooltip = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    hideTimeoutRef.current = setTimeout(() => {
      if (controlledOpen === undefined) {
        setInternalOpen(false);
      }
      onOpenChange?.(false);
      setIsVisible(false);
    }, hideDelay);
  }, [hideDelay, controlledOpen, onOpenChange]);

  // Handle trigger events
  const handleMouseEnter = useCallback(() => {
    if (triggerTypes.includes('hover')) {
      openTooltip();
    }
  }, [triggerTypes, openTooltip]);

  const handleMouseLeave = useCallback(() => {
    if (triggerTypes.includes('hover') && !interactive) {
      closeTooltip();
    }
  }, [triggerTypes, interactive, closeTooltip]);

  const handleFocus = useCallback(() => {
    if (triggerTypes.includes('focus')) {
      openTooltip();
    }
  }, [triggerTypes, openTooltip]);

  const handleBlur = useCallback(() => {
    if (triggerTypes.includes('focus')) {
      closeTooltip();
    }
  }, [triggerTypes, closeTooltip]);

  const handleClick = useCallback(() => {
    if (triggerTypes.includes('click')) {
      isOpen ? closeTooltip() : openTooltip();
    }
  }, [triggerTypes, isOpen, openTooltip, closeTooltip]);

  // Handle interactive tooltip
  const handleTooltipMouseEnter = useCallback(() => {
    if (interactive && hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }
  }, [interactive]);

  const handleTooltipMouseLeave = useCallback(() => {
    if (interactive && triggerTypes.includes('hover')) {
      closeTooltip();
    }
  }, [interactive, triggerTypes, closeTooltip]);

  // Handle escape key
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && isOpen) {
      closeTooltip();
    }
  }, [isOpen, closeTooltip]);

  // Close on click outside
  useEffect(() => {
    if (!isOpen || !triggerTypes.includes('click')) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (
        triggerRef.current && !triggerRef.current.contains(target) &&
        tooltipRef.current && !tooltipRef.current.contains(target)
      ) {
        closeTooltip();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, triggerTypes, closeTooltip]);

  // Cleanup timeouts
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
    };
  }, []);

  // Update visibility based on open state
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => setIsVisible(false), 200);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Combine styles
  const tooltipStyles = [
    baseTooltipStyles,
    variantStyles[variant],
    sizeStyles[size].tooltip,
    sizeStyles[size].text,
    isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none',
    tooltipClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Render tooltip content
  const renderContent = () => {
    if (content) return content;
    
    if (title || description || icon) {
      return (
        <div className="flex items-start space-x-2">
          {icon && (
            <div className={`${sizeStyles[size].icon} flex-shrink-0 mt-0.5 opacity-80`}>
              {icon}
            </div>
          )}
          <div className="flex-1 min-w-0">
            {title && (
              <div className="font-medium mb-1">{title}</div>
            )}
            {description && (
              <div className="opacity-90 leading-relaxed">{description}</div>
            )}
          </div>
        </div>
      );
    }

    return null;
  };

  const tooltipPosition = getTooltipPosition();

  return (
    <div
      ref={ref}
      className={`relative inline-block ${className}`}
      {...props}
    >
      {/* Trigger Element */}
      <div
        ref={triggerRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        aria-describedby={isOpen ? tooltipId : undefined}
        className={disabled ? 'opacity-50 cursor-not-allowed' : ''}
      >
        {children}
      </div>

      {/* Tooltip */}
      {isVisible && (
        <div
          ref={tooltipRef}
          id={tooltipId}
          role="tooltip"
          className={tooltipStyles}
          style={{
            ...tooltipPosition,
            maxWidth: maxWidth,
            zIndex: 1000,
          }}
          onMouseEnter={handleTooltipMouseEnter}
          onMouseLeave={handleTooltipMouseLeave}
        >
          {renderContent()}

          {/* Arrow */}
          {showArrow && (
            <div
              className={`
                absolute ${arrowStyles[placement]} ${sizeStyles[size].arrow}
                ${variantStyles[variant]} border transform rotate-45
              `}
              style={{ borderWidth: '1px' }}
            />
          )}
        </div>
      )}
    </div>
  );
});

Tooltip.displayName = 'Tooltip';

/**
 * HelpTooltip Component
 * 
 * Specialized tooltip with predefined help content for VisionGuard features
 */
interface HelpTooltipProps extends Omit<TooltipProps, 'content' | 'title' | 'description' | 'icon' | 'variant'> {
  preset: keyof typeof helpPresets;
  children: React.ReactNode;
}

const HelpTooltip = forwardRef<HTMLDivElement, HelpTooltipProps>(({
  preset,
  children,
  ...props
}, ref) => {
  const helpContent = helpPresets[preset];

  return (
    <Tooltip
      ref={ref}
      title={helpContent.title}
      description={helpContent.content}
      icon={helpContent.icon}
      variant={helpContent.variant}
      size="md"
      placement="top"
      {...props}
    >
      {children}
    </Tooltip>
  );
});

HelpTooltip.displayName = 'HelpTooltip';

/**
 * InfoTooltip Component
 * 
 * Quick info tooltip with icon for contextual information
 */
interface InfoTooltipProps extends Omit<TooltipProps, 'children'> {
  iconSize?: 'sm' | 'md' | 'lg';
  iconClassName?: string;
}

const InfoTooltip = forwardRef<HTMLDivElement, InfoTooltipProps>(({
  iconSize = 'sm',
  iconClassName = '',
  variant = 'info',
  trigger = 'hover',
  ...props
}, ref) => {
  const iconSizes = {
    sm: 'w-3.5 h-3.5',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  return (
    <Tooltip
      ref={ref}
      variant={variant}
      trigger={trigger}
      {...props}
    >
      <Info 
        className={`
          ${iconSizes[iconSize]} text-gray-400 hover:text-gray-600 
          dark:text-gray-500 dark:hover:text-gray-400 
          cursor-help transition-colors
          ${iconClassName}
        `}
      />
    </Tooltip>
  );
});

InfoTooltip.displayName = 'InfoTooltip';

/**
 * ValidationTooltip Component
 * 
 * Tooltip for form validation messages
 */
interface ValidationTooltipProps extends Omit<TooltipProps, 'variant' | 'icon'> {
  type: 'error' | 'warning' | 'success';
  message: string;
}

const ValidationTooltip = forwardRef<HTMLDivElement, ValidationTooltipProps>(({
  type,
  message,
  trigger = ['focus', 'hover'],
  placement = 'top',
  size = 'sm',
  ...props
}, ref) => {
  const typeConfig = {
    error: {
      variant: 'error' as const,
      icon: <AlertCircle className="w-4 h-4" />,
    },
    warning: {
      variant: 'warning' as const,
      icon: <AlertTriangle className="w-4 h-4" />,
    },
    success: {
      variant: 'success' as const,
      icon: <CheckCircle className="w-4 h-4" />,
    },
  };

  const config = typeConfig[type];

  return (
    <Tooltip
      ref={ref}
      variant={config.variant}
      icon={config.icon}
      content={message}
      trigger={trigger}
      placement={placement}
      size={size}
      {...props}
    />
  );
});

ValidationTooltip.displayName = 'ValidationTooltip';

// Attach sub-components to main Tooltip component
const TooltipWithComponents = Tooltip as typeof Tooltip & {
  Help: typeof HelpTooltip;
  Info: typeof InfoTooltip;
  Validation: typeof ValidationTooltip;
};

TooltipWithComponents.Help = HelpTooltip;
TooltipWithComponents.Info = InfoTooltip;
TooltipWithComponents.Validation = ValidationTooltip;

export default TooltipWithComponents;
export type { 
  TooltipProps,
  HelpTooltipProps,
  InfoTooltipProps,
  ValidationTooltipProps,
  TooltipVariant,
  TooltipSize,
  TooltipPlacement,
  TooltipTrigger
};
export { 
  HelpTooltip, 
  InfoTooltip, 
  ValidationTooltip,
  helpPresets 
};