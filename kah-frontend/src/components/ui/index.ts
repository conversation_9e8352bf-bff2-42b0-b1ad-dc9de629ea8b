/**
 * Kaydan Analytic Hub (KAH) - UI Component Library
 * 
 * A comprehensive UI component library designed specifically for data analytics
 * and exploration platforms. Built with React, TypeScript, and Tailwind CSS.
 * 
 * Features:
 * - Analytics-themed components with modern design
 * - Multi-theme support (light, dark, auto, high-contrast)
 * - Full accessibility compliance (WCAG 2.1 AA)
 * - Mobile-first responsive design
 * - TypeScript strict mode with comprehensive type definitions
 * - Internationalization ready
 * - Performance optimized with tree-shaking support
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 * @license MIT
 */

// Import components for KAH UI object
import Button from './Button';
import Input from './Input';
import Select from './Select';
import Slider from './Slider';
import Toggle from './Toggle';
import Card from './Card';
import Tabs from './Tabs';
import Modal from './Modal';
import Dropdown from './Dropdown';
import Badge from './Badge';
import Spinner from './Spinner';
import Progress from './Progress';
import Alert from './Alert';
import Tooltip from './Tooltip';

// =============================================================================
// CORE INTERACTION COMPONENTS
// =============================================================================

/**
 * Button Component
 * 
 * Versatile button with analytics-themed variants, multiple sizes,
 * loading states, and polymorphic rendering support.
 * 
 * Features:
 * - Analytics variants: primary, secondary, success, warning, error
 * - Size variants: xs, sm, md, lg, xl
 * - Polymorphic: can render as button, anchor, or router link
 * - Loading state with spinner
 * - Left/right icon support
 * - Full accessibility with keyboard navigation
 * 
 * @example
 * ```tsx
 * <Button variant="primary" size="md" loading>
 *   Analyze Data
 * </Button>
 * ```
 */
export { default as Button } from './Button';
export type { 
  ButtonProps, 
  ButtonVariant, 
  ButtonSize 
} from './Button';

/**
 * Input Component
 * 
 * Form input with validation states, auto-icons, and specialized variants
 * for data analysis and search functionality.
 * 
 * Features:
 * - Auto-icons based on input type (search, email, password, etc.)
 * - Validation states with visual feedback
 * - Password visibility toggle
 * - Clearable inputs with X button
 * - Left/right addons for enhanced functionality
 * - Loading states for async operations
 * 
 * Sub-components:
 * - Input.Group: Grouped inputs with consistent spacing
 * - Input.Search: Search input with debouncing
 * 
 * @example
 * ```tsx
 * <Input 
 *   type="search" 
 *   placeholder="Search datasets..."
 *   autoIcon
 *   clearable
 * />
 * ```
 */
export { default as Input } from './Input';
export type { 
  InputProps, 
  InputGroupProps, 
  SearchInputProps,
  InputVariant, 
  InputSize 
} from './Input';
export { InputGroup, SearchInput } from './Input';

/**
 * Select Component
 * 
 * Advanced dropdown selection with search, creation, grouping,
 * and specialized variants for cameras, models, and zones.
 * 
 * Features:
 * - Single and multi-select modes
 * - Searchable options with real-time filtering
 * - Creatable options (add new values on-the-fly)
 * - Option grouping and categorization
 * - Rich option display with icons and descriptions
 * - Keyboard navigation support
 * 
 * Sub-components:
 * - Select.Camera: Camera selection with status indicators
 * - Select.Model: Detection model selection with accuracy info
 * - Select.Zone: Zone selection with activity indicators
 * 
 * @example
 * ```tsx
 * <Select.Camera 
 *   cameras={cameraList}
 *   onValueChange={setCameraId}
 *   showStatus
 * />
 * ```
 */
export { default as Select } from './Select';
export type { 
  SelectProps, 
  CameraSelectProps, 
  ModelSelectProps, 
  ZoneSelectProps,
  SelectOption,
  SelectVariant, 
  SelectSize 
} from './Select';
export { CameraSelect, ModelSelect, ZoneSelect } from './Select';

/**
 * Slider Component
 * 
 * Range slider for detection thresholds, volume controls, opacity settings,
 * and video timeline scrubbing.
 * 
 * Features:
 * - Specialized types: confidence, volume, opacity, timeline, etc.
 * - Auto-ranges and steps based on slider type
 * - Custom value formatting (percentage, time, multiplier)
 * - Tick marks and custom markers
 * - Keyboard navigation with arrow keys
 * - Real-time and committed value updates
 * 
 * Sub-components:
 * - Slider.Confidence: Detection confidence with optimum ranges
 * - Slider.Volume: Volume control with mute functionality
 * - Slider.Timeline: Video timeline with buffered segments
 * 
 * @example
 * ```tsx
 * <Slider.Confidence 
 *   value={confidence}
 *   onChange={setConfidence}
 *   optimumRange={[75, 95]}
 * />
 * ```
 */
export { default as Slider } from './Slider';
export type { 
  SliderProps, 
  ConfidenceSliderProps, 
  VolumeSliderProps, 
  TimelineSliderProps,
  SliderVariant, 
  SliderSize 
} from './Slider';
export { ConfidenceSlider, VolumeSlider, TimelineSlider } from './Slider';

/**
 * Toggle Component
 * 
 * Toggle switches for detection features, camera controls, and system settings
 * with auto-icons and specialized variants.
 * 
 * Features:
 * - Functional types: camera, recording, audio, network, privacy, detection
 * - Auto-icons based on toggle type (camera on/off, volume, wifi, etc.)
 * - Custom on/off labels
 * - Loading states for async operations
 * - Validation feedback with success/error states
 * 
 * Sub-components:
 * - Toggle.Detection: Detection feature toggles
 * - Toggle.Camera: Camera control toggles
 * - Toggle.System: System setting toggles
 * - Toggle.Group: Grouped toggles with spacing
 * 
 * @example
 * ```tsx
 * <Toggle.Detection 
 *   label="Enable Object Detection"
 *   checked={detectionEnabled}
 *   onChange={setDetectionEnabled}
 * />
 * ```
 */
export { default as Toggle } from './Toggle';
export type { 
  ToggleProps, 
  ToggleGroupProps, 
  DetectionToggleProps,
  ToggleVariant, 
  ToggleSize 
} from './Toggle';
export { ToggleGroup, DetectionToggle, CameraToggle, SystemToggle } from './Toggle';

// =============================================================================
// CONTENT ORGANIZATION COMPONENTS
// =============================================================================

/**
 * Card Component
 * 
 * Versatile content container with specialized variants for metrics,
 * detection results, sessions, and zones.
 * 
 * Features:
 * - Specialized variants: metric, detection, zone, session
 * - Interactive states: hoverable, clickable, selected
 * - Loading states with skeleton animation
 * - Composable structure with header, content, footer, actions
 * 
 * Sub-components:
 * - Card.Header: Title, subtitle, badge, and action area
 * - Card.Content: Main content area
 * - Card.Footer: Metadata and timestamps
 * - Card.Actions: Button groups with alignment options
 * 
 * @example
 * ```tsx
 * <Card variant="metric" size="lg">
 *   <Card.Header 
 *     title="Active Cameras"
 *     badge={<Badge variant="success">Live</Badge>}
 *   />
 *   <Card.Content>
 *     <div className="text-4xl font-bold">24</div>
 *   </Card.Content>
 * </Card>
 * ```
 */
export { default as Card } from './Card';
export type { 
  CardProps, 
  CardHeaderProps, 
  CardContentProps, 
  CardFooterProps,
  CardVariant, 
  CardSize 
} from './Card';
export { CardHeader, CardContent, CardFooter, CardActions } from './Card';

/**
 * Tabs Component
 * 
 * Content organization with specialized tabs for analytics, detection types,
 * and camera management.
 * 
 * Features:
 * - Multiple variants: default, cards, pills, underlined, minimal
 * - Scrollable tabs with overflow handling
 * - Closable and addable tabs for dynamic content
 * - Drag and drop reordering
 * - Badge support for counts and status
 * 
 * Sub-components:
 * - Tabs.Analytics: Analytics content with data counts
 * - Tabs.Detection: Detection types with accuracy indicators
 * - Tabs.Camera: Camera views with status indicators
 * 
 * @example
 * ```tsx
 * <Tabs.Analytics 
 *   analyticsItems={analyticsData}
 *   showDataCounts
 *   variant="detection"
 * />
 * ```
 */
export { default as Tabs } from './Tabs';
export type { 
  TabsProps, 
  AnalyticsTabsProps, 
  DetectionTabsProps, 
  CameraTabsProps,
  TabItem,
  TabsVariant, 
  TabsSize 
} from './Tabs';
export { AnalyticsTabs, DetectionTabs, CameraTabs } from './Tabs';

/**
 * Modal Component
 * 
 * Dialog system for settings, confirmations, and specialized detection workflows.
 * 
 * Features:
 * - Specialized types: settings, camera, detection, zone, export, confirmation
 * - Auto-icons based on modal type
 * - Focus management and keyboard navigation
 * - Overlay click and escape key handling
 * - Loading states with progress indication
 * 
 * Sub-components:
 * - Modal.Settings: Settings dialogs with save/reset actions
 * - Modal.Confirmation: Confirmation dialogs with action buttons
 * - Modal.Detection: Detection configuration dialogs
 * - Modal.Export: Export dialogs with progress tracking
 * 
 * @example
 * ```tsx
 * <Modal.Settings 
 *   isOpen={showSettings}
 *   onClose={closeSettings}
 *   title="Camera Configuration"
 *   onSave={saveSettings}
 *   showReset
 * >
 *   <SettingsForm />
 * </Modal.Settings>
 * ```
 */
export { default as Modal } from './Modal';
export type { 
  ModalProps, 
  ModalHeaderProps, 
  ConfirmationModalProps, 
  SettingsModalProps,
  ModalVariant, 
  ModalSize 
} from './Modal';
export { ModalHeader, ModalBody, ModalFooter, ConfirmationModal, SettingsModal } from './Modal';

/**
 * Dropdown Component
 * 
 * Context menus and action dropdowns with VisionGuard operation presets.
 * 
 * Features:
 * - Context menu presets: camera, detection, zone, session, user
 * - Multiple trigger types: click, hover, focus, contextmenu
 * - Rich menu items with icons, badges, descriptions, shortcuts
 * - Nested submenus and separators
 * - Keyboard navigation with arrow keys
 * 
 * Sub-components:
 * - Dropdown.Context: Right-click context menus with presets
 * - Dropdown.Action: Action buttons with "more options" styling
 * - Dropdown.User: User account menu with avatar
 * 
 * @example
 * ```tsx
 * <Dropdown.Context 
 *   preset="camera"
 *   onItemSelect={handleCameraAction}
 * >
 *   <CameraCard camera={camera} />
 * </Dropdown.Context>
 * ```
 */
export { default as Dropdown } from './Dropdown';
export type { 
  DropdownProps, 
  ContextMenuProps, 
  ActionMenuProps, 
  UserMenuProps,
  DropdownItem,
  DropdownVariant, 
  DropdownSize 
} from './Dropdown';
export { ContextMenu, ActionMenu, UserMenu, contextMenuPresets } from './Dropdown';

// =============================================================================
// FEEDBACK COMPONENTS
// =============================================================================

/**
 * Badge Component
 * 
 * Status indicators, counters, and labels with detection-specific variants.
 * 
 * Features:
 * - Detection variants: person (green), vehicle (orange), object (purple), zone (cyan)
 * - Multiple shapes: rounded, pill, square
 * - Dot indicators for minimal status display
 * - Pulse animation for live states
 * - Count formatting with overflow handling
 * 
 * Sub-components:
 * - Badge.Group: Grouped badges with consistent spacing
 * - Badge.Count: Count badges with auto-formatting
 * - Badge.Status: Status badges with auto-icons
 * 
 * @example
 * ```tsx
 * <Badge variant="detection-person" pulse>
 *   12 People Detected
 * </Badge>
 * ```
 */
export { default as Badge } from './Badge';
export type { 
  BadgeProps, 
  BadgeGroupProps, 
  CountBadgeProps, 
  StatusBadgeProps,
  BadgeVariant, 
  BadgeSize 
} from './Badge';
export { BadgeGroup, CountBadge, StatusBadge } from './Badge';

/**
 * Spinner Component
 * 
 * Loading indicators with detection-specific variants and specialized animations.
 * 
 * Features:
 * - Detection variants: camera, processing, analysis with themed colors
 * - Multiple animation types: spin, pulse, dots, bars
 * - Speed control: slow, normal, fast
 * - Loading overlays and inline spinners
 * - Progress tracking for video processing
 * 
 * Sub-components:
 * - Spinner.Overlay: Full-screen loading overlays
 * - Spinner.Inline: Inline spinners for buttons/inputs
 * - Spinner.Processing: Video/detection processing with progress
 * 
 * @example
 * ```tsx
 * <Spinner.Processing 
 *   processingType="detection"
 *   progress={45}
 *   label="Analyzing video..."
 * />
 * ```
 */
export { default as Spinner } from './Spinner';
export type { 
  SpinnerProps, 
  LoadingOverlayProps, 
  InlineSpinnerProps, 
  ProcessingSpinnerProps,
  SpinnerVariant, 
  SpinnerSize 
} from './Spinner';
export { LoadingOverlay, InlineSpinner, ProcessingSpinner } from './Spinner';

/**
 * Progress Component
 * 
 * Progress tracking for detection processing, exports, and multi-step operations.
 * 
 * Features:
 * - Process types: detection, export, import, analysis, training
 * - Multiple progress types: linear, circular, stepped
 * - Real-time updates with speed and time estimation
 * - Multi-progress for complex operations
 * - Status indicators: active, paused, completed, error
 * 
 * Sub-components:
 * - Progress.Process: Process tracking with speed/time estimates
 * - Progress.Stepped: Multi-step workflows and wizards
 * - Progress.Multi: Multiple progress bars for complex operations
 * 
 * @example
 * ```tsx
 * <Progress.Process
 *   processDetails={{
 *     type: 'detection',
 *     name: 'Real-time Detection',
 *     speed: '25 FPS',
 *     estimatedTimeRemaining: 45
 *   }}
 *   variant="detection"
 * />
 * ```
 */
export { default as Progress } from './Progress';
export type { 
  ProgressProps, 
  ProcessProgressProps, 
  SteppedProgressProps, 
  MultiProgressProps,
  ProgressStep,
  ProcessDetails,
  ProgressVariant, 
  ProgressSize 
} from './Progress';
export { ProcessProgress, SteppedProgress, MultiProgress } from './Progress';

/**
 * Alert Component
 * 
 * Notification system for detection events, system alerts, and user feedback.
 * 
 * Features:
 * - Detection alerts with snapshots and metadata
 * - System alerts with severity levels and device info
 * - Auto-close and dismissible options
 * - Rich content with actions and timestamps
 * - Toast notifications for temporary messages
 * 
 * Sub-components:
 * - Alert.Detection: Detection event alerts with snapshots
 * - Alert.System: System health and maintenance alerts
 * - Alert.List: Alert feed with pagination
 * - Alert.Toast: Floating toast notifications
 * 
 * @example
 * ```tsx
 * <Alert.Detection
 *   detection={detectionEvent}
 *   showImage
 *   showZone
 *   onViewDetails={handleViewDetails}
 * />
 * ```
 */
export { default as Alert } from './Alert';
export type { 
  AlertProps, 
  DetectionAlertProps, 
  SystemAlertProps, 
  AlertListProps,
  ToastProps,
  AlertVariant, 
  AlertSize 
} from './Alert';
export { DetectionAlert, SystemAlert, AlertList, Toast } from './Alert';

/**
 * Tooltip Component
 * 
 * Contextual help and information with VisionGuard feature presets.
 * 
 * Features:
 * - Help presets for common VisionGuard features
 * - Multiple trigger types: hover, focus, click, manual
 * - Rich content with icons and descriptions
 * - Interactive tooltips for complex content
 * - Validation tooltips for form feedback
 * 
 * Sub-components:
 * - Tooltip.Help: Predefined help content for VisionGuard features
 * - Tooltip.Info: Quick info tooltips with info icon
 * - Tooltip.Validation: Form validation messages
 * 
 * @example
 * ```tsx
 * <Tooltip.Help preset="confidence">
 *   <Slider type="confidence" />
 * </Tooltip.Help>
 * ```
 */
export { default as Tooltip } from './Tooltip';
export type { 
  TooltipProps, 
  HelpTooltipProps, 
  InfoTooltipProps, 
  ValidationTooltipProps,
  TooltipVariant, 
  TooltipSize 
} from './Tooltip';
export { HelpTooltip, InfoTooltip, ValidationTooltip, helpPresets } from './Tooltip';

// =============================================================================
// TYPE DEFINITIONS AND UTILITIES
// =============================================================================

/**
 * Type Definitions
 * 
 * Shared type definitions used across multiple components.
 */
export type {
  // Size variants used across components
  ComponentSize,
  
  // Variant types for consistent theming
  ComponentVariant,
  
  // Analytics-specific types
  AnalyticsType,
  AnalyticsStatus,
  DataSourceStatus,
  DatasetStatus,
  
  // Process and operation types
  ProcessStatus,
  OperationType,
  
  // Common props interfaces
  BaseComponentProps,
  InteractiveProps,
  ValidationProps,
} from './types';

/**
 * Utility Functions
 * 
 * Helper functions for component styling and behavior.
 */
export {
  // CSS class utilities
  cn,
  mergeClasses,
  
  // Theme utilities
  getThemeColors,
  getAnalyticsColors,
  
  // Component utilities
  generateId,
  formatDuration,
  formatFileSize,
  formatPercentage,
} from './utils';

/**
 * Constants
 * 
 * Shared constants and configuration values.
 */
export {
  // Color palettes
  ANALYTICS_COLORS,
  THEME_COLORS,
  
  // Size definitions
  COMPONENT_SIZES,
  
  // Default values
  DEFAULT_ANIMATION_DURATION,
  DEFAULT_DEBOUNCE_DELAY,
  
  // Keyboard shortcuts
  KEYBOARD_SHORTCUTS,
} from './Constants';

// =============================================================================
// COMPONENT LIBRARY INFORMATION
// =============================================================================

/**
 * Library Metadata
 */
export const LIBRARY_INFO = {
  name: 'Kaydan Analytic Hub (KAH) UI Components',
  version: '1.0.0',
  description: 'Comprehensive UI component library for data analytics and exploration platforms',
  author: 'Kaydan Team',
  license: 'MIT',
  homepage: 'https://kaydan.com',
  repository: 'https://github.com/kaydan/ui-components',
  
  // Component categories
  categories: {
    interaction: ['Button', 'Input', 'Select', 'Slider', 'Toggle'],
    organization: ['Card', 'Tabs', 'Modal', 'Dropdown'],
    feedback: ['Badge', 'Spinner', 'Progress', 'Alert', 'Tooltip'],
  },
  
  // Theme information
  themes: {
    supported: ['light', 'dark', 'auto', 'high-contrast'],
    analytics: {
      person: '#10b981',
      vehicle: '#f59e0b', 
      object: '#8b5cf6',
      zone: '#06b6d4',
    },
  },
  
  // Accessibility compliance
  accessibility: {
    standard: 'WCAG 2.1 AA',
    features: [
      'Keyboard navigation',
      'Screen reader support',
      'Focus management',
      'High contrast mode',
      'Reduced motion support',
    ],
  },
  
  // Browser support
  browsers: {
    chrome: '>=90',
    firefox: '>=88',
    safari: '>=14',
    edge: '>=90',
  },
} as const;

/**
 * Default Export
 * 
 * All components exported as a single object for convenience.
 */
const KAHUI = {
  // Core interaction components
  Button,
  Input,
  Select,
  Slider,
  Toggle,
  
  // Content organization components
  Card,
  Tabs,
  Modal,
  Dropdown,
  
  // Feedback components
  Badge,
  Spinner,
  Progress,
  Alert,
  Tooltip,
  
  // Library information
  LIBRARY_INFO,
};

export default KAHUI;

/**
 * Usage Examples
 * 
 * @example Basic usage with individual imports
 * ```tsx
 * import { Button, Card, Badge } from '@kaydan/ui';
 * 
 * function Dashboard() {
 *   return (
 *     <Card variant="metric">
 *       <Card.Header 
 *         title="Active Datasets"
 *         badge={<Badge variant="success">Live</Badge>}
 *       />
 *       <Card.Content>
 *         <div className="text-4xl font-bold">24</div>
 *       </Card.Content>
 *       <Card.Actions>
 *         <Button variant="primary">View Details</Button>
 *       </Card.Actions>
 *     </Card>
 *   );
 * }
 * ```
 * 
 * @example Default import usage
 * ```tsx
 * import KAHUI from '@kaydan/ui';
 * 
 * function App() {
 *   return (
 *     <KAHUI.Modal.Settings 
 *       isOpen={showSettings}
 *       onClose={closeSettings}
 *     >
 *       <KAHUI.Tabs variant="pills">
 *         {settingsTabs}
 *       </KAHUI.Tabs>
 *     </KAHUI.Modal.Settings>
 *   );
 * }
 * ```
 * 
 * @example Analytics-specific workflow
 * ```tsx
 * import { 
 *   Select, 
 *   Slider, 
 *   Toggle, 
 *   Progress, 
 *   Alert 
 * } from '@kaydan/ui';
 * 
 * function AnalyticsConfig() {
 *   return (
 *     <div className="space-y-6">
 *       <Select.Model 
 *         models={detectionModels}
 *         onValueChange={setSelectedModel}
 *         showAccuracy
 *       />
 *       
 *       <Slider.Confidence 
 *         value={confidence}
 *         onChange={setConfidence}
 *         optimumRange={[75, 95]}
 *       />
 *       
 *       <Toggle.Detection 
 *         label="Enable Real-time Detection"
 *         checked={detectionEnabled}
 *         onChange={setDetectionEnabled}
 *       />
 *       
 *       {processing && (
 *         <Progress.Process
 *           processDetails={{
 *             type: 'detection',
 *             name: 'Model Training',
 *             progress: trainingProgress
 *           }}
 *           variant="detection"
 *         />
 *       )}
 *       
 *       <Alert.Detection
 *         detection={latestDetection}
 *         showImage
 *         onViewDetails={viewDetectionDetails}
 *       />
 *     </div>
 *   );
 * }
 * ```
 */