import React, { forwardRef, useState, useRef, useEffect, useId, useCallback } from 'react';
import { 
  ChevronLeft, 
  ChevronRight, 
  MoreHorizontal,
  X,
  Plus,
  BarChart3,
  Camera,
  Eye,
  Settings,
  History,
  Download,
  Users,
  Car,
  Box,
  MapPin,
  Zap,
  Shield,
  Bell,
  HelpCircle
} from 'lucide-react';

// Tab variant types
type TabsVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'detection'
  | 'underlined'
  | 'pills'
  | 'cards'
  | 'minimal';

type TabsSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type TabsOrientation = 'horizontal' | 'vertical';

// Tab item interface
interface TabItem {
  id: string;
  label: string;
  content?: React.ReactNode;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
  disabled?: boolean;
  closable?: boolean;
  hidden?: boolean;
  href?: string;
  onClick?: () => void;
}

// Analytics-specific tab types
interface AnalyticsTab extends TabItem {
  type?: 'overview' | 'trends' | 'zones' | 'reports';
  dataCount?: number;
}

interface DetectionTab extends TabItem {
  detectionType?: 'person' | 'vehicle' | 'object' | 'general';
  accuracy?: number;
  status?: 'active' | 'inactive' | 'error';
}

interface CameraTab extends TabItem {
  cameraId?: string;
  status?: 'online' | 'offline' | 'error';
  streamUrl?: string;
}

// Icon mapping for different tab types
const typeIcons = {
  overview: BarChart3,
  trends: BarChart3,
  zones: MapPin,
  reports: Download,
  camera: Camera,
  detection: Eye,
  settings: Settings,
  history: History,
  person: Users,
  vehicle: Car,
  object: Box,
  general: Zap,
  security: Shield,
  alerts: Bell,
  help: HelpCircle,
} as const;

// Base tabs props
interface BaseTabsProps {
  variant?: TabsVariant;
  size?: TabsSize;
  orientation?: TabsOrientation;
  items: TabItem[];
  activeTab?: string;
  defaultActiveTab?: string;
  scrollable?: boolean;
  closableByDefault?: boolean;
  addable?: boolean;
  draggable?: boolean;
  centered?: boolean;
  justified?: boolean;
  sticky?: boolean;
  className?: string;
  tabListClassName?: string;
  tabClassName?: string;
  panelClassName?: string;
  onChange?: (tabId: string) => void;
  onTabClose?: (tabId: string) => void;
  onTabAdd?: () => void;
  onTabMove?: (fromIndex: number, toIndex: number) => void;
  renderTabContent?: (tab: TabItem) => React.ReactNode;
}

type TabsProps = BaseTabsProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseTabsProps>;

// Variant styles
const variantStyles: Record<TabsVariant, { list: string; tab: string; activeTab: string; panel: string }> = {
  default: {
    list: 'border-b border-gray-200 dark:border-gray-700',
    tab: `
      px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-gray-800 hover:border-gray-300
      dark:hover:text-gray-200 dark:hover:border-gray-600
      border-b-2 border-transparent transition-all duration-200
    `,
    activeTab: `
      text-primary-600 border-primary-600
      dark:text-primary-400 dark:border-primary-400
    `,
    panel: 'py-4',
  },
  primary: {
    list: 'border-b border-primary-200 dark:border-primary-700',
    tab: `
      px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-primary-700 hover:border-primary-300
      dark:hover:text-primary-300 dark:hover:border-primary-600
      border-b-2 border-transparent transition-all duration-200
    `,
    activeTab: `
      text-primary-700 border-primary-600
      dark:text-primary-300 dark:border-primary-400
    `,
    panel: 'py-4',
  },
  secondary: {
    list: 'border-b border-gray-100 dark:border-gray-800',
    tab: `
      px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-500
      hover:text-gray-700 hover:bg-gray-50
      dark:hover:text-gray-300 dark:hover:bg-gray-800
      rounded-t-md transition-all duration-200
    `,
    activeTab: `
      text-gray-900 bg-gray-100 dark:text-gray-100 dark:bg-gray-800
    `,
    panel: 'py-4',
  },
  detection: {
    list: 'border-b border-cyan-200 dark:border-cyan-700',
    tab: `
      px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-cyan-700 hover:border-cyan-300
      dark:hover:text-cyan-300 dark:hover:border-cyan-600
      border-b-2 border-transparent transition-all duration-200
    `,
    activeTab: `
      text-cyan-700 border-cyan-600
      dark:text-cyan-300 dark:border-cyan-400
    `,
    panel: 'py-4',
  },
  underlined: {
    list: '',
    tab: `
      px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-gray-800 dark:hover:text-gray-200
      border-b-2 border-transparent transition-all duration-200
    `,
    activeTab: `
      text-gray-900 border-gray-900
      dark:text-gray-100 dark:border-gray-100
    `,
    panel: 'py-4',
  },
  pills: {
    list: 'space-x-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg',
    tab: `
      px-3 py-1.5 text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-gray-800 dark:hover:text-gray-200
      rounded-md transition-all duration-200
    `,
    activeTab: `
      text-gray-900 bg-white shadow-sm
      dark:text-gray-100 dark:bg-gray-700
    `,
    panel: 'py-4',
  },
  cards: {
    list: 'space-x-2',
    tab: `
      px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-gray-800 dark:hover:text-gray-200
      border border-gray-200 dark:border-gray-700
      rounded-t-lg transition-all duration-200
      -mb-px
    `,
    activeTab: `
      text-gray-900 bg-white border-gray-200 border-b-white
      dark:text-gray-100 dark:bg-gray-800 dark:border-gray-600 dark:border-b-gray-800
    `,
    panel: 'py-4 border border-gray-200 dark:border-gray-700 rounded-b-lg -mt-px bg-white dark:bg-gray-800 p-4',
  },
  minimal: {
    list: 'space-x-6',
    tab: `
      text-sm font-medium text-gray-600 dark:text-gray-400
      hover:text-gray-800 dark:hover:text-gray-200
      transition-colors duration-200
    `,
    activeTab: `
      text-gray-900 dark:text-gray-100
    `,
    panel: 'py-4',
  },
};

// Size styles
const sizeStyles: Record<TabsSize, { tab: string; icon: string; badge: string }> = {
  xs: {
    tab: 'px-2 py-1 text-xs',
    icon: 'w-3 h-3',
    badge: 'text-xs px-1.5 py-0.5',
  },
  sm: {
    tab: 'px-3 py-1.5 text-sm',
    icon: 'w-3.5 h-3.5',
    badge: 'text-xs px-2 py-0.5',
  },
  md: {
    tab: 'px-4 py-2 text-sm',
    icon: 'w-4 h-4',
    badge: 'text-xs px-2 py-1',
  },
  lg: {
    tab: 'px-5 py-2.5 text-base',
    icon: 'w-5 h-5',
    badge: 'text-sm px-2.5 py-1',
  },
  xl: {
    tab: 'px-6 py-3 text-lg',
    icon: 'w-6 h-6',
    badge: 'text-sm px-3 py-1.5',
  },
};

/**
 * Tabs Component
 * 
 * A versatile tabs component for the VisionGuard Detection Analytics platform.
 * Supports content organization, navigation, and specialized analytics/detection tabs.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Horizontal and vertical orientations
 * - Scrollable tabs for overflow handling
 * - Closable and addable tabs
 * - Drag and drop reordering
 * - Icons, badges, and custom content
 * - Keyboard navigation
 * - Sticky tabs for long content
 * - Analytics and detection specialized tabs
 * - Full accessibility support
 * 
 * @example
 * ```tsx
 * // Analytics tabs
 * <Tabs 
 *   variant="detection"
 *   items={[
 *     { 
 *       id: 'overview', 
 *       label: 'Overview', 
 *       icon: <BarChart3 />,
 *       content: <AnalyticsOverview /> 
 *     },
 *     { 
 *       id: 'trends', 
 *       label: 'Trends', 
 *       icon: <TrendingUp />,
 *       badge: <Badge variant="success">Live</Badge>,
 *       content: <TrendsAnalysis /> 
 *     }
 *   ]}
 *   activeTab={activeTab}
 *   onChange={setActiveTab}
 * />
 * 
 * // Camera tabs with status
 * <Tabs 
 *   variant="cards"
 *   items={cameras.map(camera => ({
 *     id: camera.id,
 *     label: camera.name,
 *     icon: <Camera />,
 *     badge: camera.status === 'online' ? 
 *       <Badge variant="success" dot /> : 
 *       <Badge variant="error" dot />,
 *     content: <CameraView camera={camera} />
 *   }))}
 *   closableByDefault
 *   addable
 *   onTabAdd={addNewCamera}
 *   onTabClose={removeCamera}
 * />
 * 
 * // Settings tabs
 * <Tabs 
 *   variant="pills"
 *   orientation="vertical"
 *   items={[
 *     { id: 'general', label: 'General', icon: <Settings /> },
 *     { id: 'cameras', label: 'Cameras', icon: <Camera /> },
 *     { id: 'detection', label: 'Detection', icon: <Eye /> },
 *     { id: 'alerts', label: 'Alerts', icon: <Bell /> }
 *   ]}
 * />
 * ```
 */
const Tabs = forwardRef<HTMLDivElement, TabsProps>(({
  variant = 'default',
  size = 'md',
  orientation = 'horizontal',
  items = [],
  activeTab,
  defaultActiveTab,
  scrollable = true,
  closableByDefault = false,
  addable = false,
  draggable = false,
  centered = false,
  justified = false,
  sticky = false,
  className = '',
  tabListClassName = '',
  tabClassName = '',
  panelClassName = '',
  onChange,
  onTabClose,
  onTabAdd,
  onTabMove,
  renderTabContent,
  ...props
}, ref) => {
  const [internalActiveTab, setInternalActiveTab] = useState(() => {
    return activeTab || defaultActiveTab || items.find(item => !item.disabled && !item.hidden)?.id || '';
  });
  
  const [scrollLeft, setScrollLeft] = useState(0);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [draggedTab, setDraggedTab] = useState<string | null>(null);
  
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const tabsId = useId();

  // Current active tab (controlled or uncontrolled)
  const currentActiveTab = activeTab !== undefined ? activeTab : internalActiveTab;

  // Filter visible items
  const visibleItems = items.filter(item => !item.hidden);

  // Get current active tab item
  const activeTabItem = visibleItems.find(item => item.id === currentActiveTab);

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    const tab = visibleItems.find(item => item.id === tabId);
    if (tab && !tab.disabled) {
      if (activeTab === undefined) {
        setInternalActiveTab(tabId);
      }
      onChange?.(tabId);
      tab.onClick?.();
    }
  };

  // Handle tab close
  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose?.(tabId);
  };

  // Handle tab add
  const handleTabAdd = () => {
    onTabAdd?.();
  };

  // Check scroll position
  const checkScrollPosition = useCallback(() => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setScrollLeft(scrollLeft);
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  }, []);

  // Scroll tabs
  const scrollTabs = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 200;
      const newScrollLeft = direction === 'left' 
        ? scrollLeft - scrollAmount 
        : scrollLeft + scrollAmount;
      
      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth',
      });
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent, tabId: string) => {
    const currentIndex = visibleItems.findIndex(item => item.id === tabId);
    let newIndex = currentIndex;

    switch (e.key) {
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        newIndex = currentIndex > 0 ? currentIndex - 1 : visibleItems.length - 1;
        break;
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        newIndex = currentIndex < visibleItems.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'Home':
        e.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newIndex = visibleItems.length - 1;
        break;
      case 'Delete':
        if (closableByDefault || visibleItems[currentIndex].closable) {
          e.preventDefault();
          onTabClose?.(tabId);
        }
        break;
    }

    // Find next non-disabled tab
    let attempts = 0;
    while (attempts < visibleItems.length && visibleItems[newIndex]?.disabled) {
      newIndex = (newIndex + 1) % visibleItems.length;
      attempts++;
    }

    if (newIndex !== currentIndex && visibleItems[newIndex] && !visibleItems[newIndex].disabled) {
      handleTabChange(visibleItems[newIndex].id);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, tabId: string) => {
    if (!draggable) return;
    setDraggedTab(tabId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!draggable) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetTabId: string) => {
    if (!draggable || !draggedTab) return;
    e.preventDefault();
    
    const fromIndex = visibleItems.findIndex(item => item.id === draggedTab);
    const toIndex = visibleItems.findIndex(item => item.id === targetTabId);
    
    if (fromIndex !== -1 && toIndex !== -1 && fromIndex !== toIndex) {
      onTabMove?.(fromIndex, toIndex);
    }
    
    setDraggedTab(null);
  };

  // Update scroll position on mount and resize
  useEffect(() => {
    checkScrollPosition();
    const resizeObserver = new ResizeObserver(checkScrollPosition);
    
    if (scrollContainerRef.current) {
      resizeObserver.observe(scrollContainerRef.current);
    }
    
    return () => resizeObserver.disconnect();
  }, [checkScrollPosition]);

  // Combine styles
  const tabListStyles = [
    'flex',
    orientation === 'vertical' ? 'flex-col' : 'flex-row',
    centered && orientation === 'horizontal' && 'justify-center',
    justified && orientation === 'horizontal' && 'justify-between',
    variantStyles[variant].list,
    tabListClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const scrollContainerStyles = [
    'flex',
    orientation === 'vertical' ? 'flex-col' : 'flex-row',
    scrollable && orientation === 'horizontal' && 'overflow-x-auto scrollbar-hide',
    scrollable && orientation === 'vertical' && 'overflow-y-auto scrollbar-hide',
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div
      ref={ref}
      className={`w-full ${className}`}
      {...props}
    >
      {/* Tab List */}
      <div
        className={`
          relative
          ${sticky ? 'sticky top-0 z-10 bg-white dark:bg-gray-900' : ''}
        `}
      >
        <div className={tabListStyles}>
          {/* Scroll Left Button */}
          {scrollable && orientation === 'horizontal' && canScrollLeft && (
            <button
              type="button"
              onClick={() => scrollTabs('left')}
              className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="Scroll tabs left"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
          )}

          {/* Tabs Container */}
          <div
            ref={scrollContainerRef}
            className={scrollContainerStyles}
            onScroll={checkScrollPosition}
            role="tablist"
            aria-orientation={orientation}
          >
            {visibleItems.map((tab) => {
              const isActive = tab.id === currentActiveTab;
              const isClosable = closableByDefault || tab.closable;
              
              const tabStyles = [
                'relative inline-flex items-center justify-center gap-2',
                'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                'dark:focus:ring-offset-gray-900',
                variantStyles[variant].tab,
                sizeStyles[size].tab,
                isActive && variantStyles[variant].activeTab,
                tab.disabled && 'opacity-50 cursor-not-allowed',
                draggedTab === tab.id && 'opacity-50',
                tabClassName,
              ]
                .filter(Boolean)
                .join(' ');

              const TabContent = () => (
                <>
                  {/* Icon */}
                  {tab.icon && (
                    <span className={`${sizeStyles[size].icon} flex-shrink-0`}>
                      {tab.icon}
                    </span>
                  )}

                  {/* Label */}
                  <span className="truncate">{tab.label}</span>

                  {/* Badge */}
                  {tab.badge && (
                    <span className="flex-shrink-0">
                      {tab.badge}
                    </span>
                  )}

                  {/* Close Button */}
                  {isClosable && (
                    <button
                      type="button"
                      onClick={(e) => handleTabClose(e, tab.id)}
                      className="flex-shrink-0 ml-1 p-0.5 rounded-sm text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      aria-label={`Close ${tab.label} tab`}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  )}
                </>
              );

              if (tab.href) {
                return (
                  <a
                    key={tab.id}
                    href={tab.href}
                    className={tabStyles}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`${tabsId}-panel-${tab.id}`}
                    id={`${tabsId}-tab-${tab.id}`}
                    tabIndex={isActive ? 0 : -1}
                  >
                    <TabContent />
                  </a>
                );
              }

              return (
                <button
                  key={tab.id}
                  type="button"
                  className={tabStyles}
                  onClick={() => handleTabChange(tab.id)}
                  onKeyDown={(e) => handleKeyDown(e, tab.id)}
                  disabled={tab.disabled}
                  draggable={draggable}
                  onDragStart={(e) => handleDragStart(e, tab.id)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, tab.id)}
                  role="tab"
                  aria-selected={isActive}
                  aria-controls={`${tabsId}-panel-${tab.id}`}
                  id={`${tabsId}-tab-${tab.id}`}
                  tabIndex={isActive ? 0 : -1}
                >
                  <TabContent />
                </button>
              );
            })}
          </div>

          {/* Scroll Right Button */}
          {scrollable && orientation === 'horizontal' && canScrollRight && (
            <button
              type="button"
              onClick={() => scrollTabs('right')}
              className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="Scroll tabs right"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          )}

          {/* Add Tab Button */}
          {addable && (
            <button
              type="button"
              onClick={handleTabAdd}
              className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              aria-label="Add new tab"
            >
              <Plus className="w-4 h-4" />
            </button>
          )}

          {/* Overflow Menu */}
          {scrollable && visibleItems.length > 5 && (
            <button
              type="button"
              className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="More tabs"
            >
              <MoreHorizontal className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Tab Panels */}
      <div className={`${variantStyles[variant].panel} ${panelClassName}`}>
        {activeTabItem && (
          <div
            role="tabpanel"
            aria-labelledby={`${tabsId}-tab-${activeTabItem.id}`}
            id={`${tabsId}-panel-${activeTabItem.id}`}
            tabIndex={0}
          >
            {renderTabContent ? renderTabContent(activeTabItem) : activeTabItem.content}
          </div>
        )}
      </div>
    </div>
  );
});

Tabs.displayName = 'Tabs';

/**
 * AnalyticsTabs Component
 * 
 * Specialized tabs for analytics content organization
 */
interface AnalyticsTabsProps extends Omit<TabsProps, 'items'> {
  analyticsItems: AnalyticsTab[];
  showDataCounts?: boolean;
}

const AnalyticsTabs = forwardRef<HTMLDivElement, AnalyticsTabsProps>(({
  analyticsItems,
  showDataCounts = true,
  variant = 'detection',
  ...props
}, ref) => {
  const items = analyticsItems.map(item => ({
    ...item,
    icon: item.icon || (item.type && typeIcons[item.type] ? 
      React.createElement(typeIcons[item.type], { className: 'w-4 h-4' }) : 
      undefined
    ),
    badge: showDataCounts && item.dataCount !== undefined ? (
      <span className="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full">
        {item.dataCount}
      </span>
    ) : item.badge,
  }));

  return (
    <Tabs
      ref={ref}
      variant={variant}
      items={items}
      {...props}
    />
  );
});

AnalyticsTabs.displayName = 'AnalyticsTabs';

/**
 * DetectionTabs Component
 * 
 * Specialized tabs for detection type organization
 */
interface DetectionTabsProps extends Omit<TabsProps, 'items'> {
  detectionItems: DetectionTab[];
  showAccuracy?: boolean;
  showStatus?: boolean;
}

const DetectionTabs = forwardRef<HTMLDivElement, DetectionTabsProps>(({
  detectionItems,
  showAccuracy = true,
  showStatus = true,
  variant = 'detection',
  ...props
}, ref) => {
  const items = detectionItems.map(item => ({
    ...item,
    icon: item.icon || (item.detectionType && typeIcons[item.detectionType] ? 
      React.createElement(typeIcons[item.detectionType], { className: 'w-4 h-4' }) : 
      undefined
    ),
    badge: (
      <div className="flex items-center space-x-2">
        {showStatus && item.status && (
          <div className={`w-2 h-2 rounded-full ${
            item.status === 'active' ? 'bg-green-500' :
            item.status === 'inactive' ? 'bg-gray-400' :
            'bg-red-500'
          }`} />
        )}
        {showAccuracy && item.accuracy !== undefined && (
          <span className="px-2 py-0.5 text-xs bg-cyan-100 dark:bg-cyan-900 text-cyan-700 dark:text-cyan-300 rounded-full">
            {item.accuracy}%
          </span>
        )}
        {item.badge}
      </div>
    ),
  }));

  return (
    <Tabs
      ref={ref}
      variant={variant}
      items={items}
      {...props}
    />
  );
});

DetectionTabs.displayName = 'DetectionTabs';

/**
 * CameraTabs Component
 * 
 * Specialized tabs for camera views and management
 */
interface CameraTabsProps extends Omit<TabsProps, 'items'> {
  cameraItems: CameraTab[];
  showStatus?: boolean;
}

const CameraTabs = forwardRef<HTMLDivElement, CameraTabsProps>(({
  cameraItems,
  showStatus = true,
  variant = 'cards',
  closableByDefault = true,
  ...props
}, ref) => {
  const items = cameraItems.map(item => ({
    ...item,
    icon: item.icon || <Camera className="w-4 h-4" />,
    badge: showStatus && item.status ? (
      <div className={`w-2 h-2 rounded-full ${
        item.status === 'online' ? 'bg-green-500' :
        item.status === 'offline' ? 'bg-gray-400' :
        'bg-red-500'
      }`} />
    ) : item.badge,
  }));

  return (
    <Tabs
      ref={ref}
      variant={variant}
      items={items}
      closableByDefault={closableByDefault}
      {...props}
    />
  );
});

CameraTabs.displayName = 'CameraTabs';

// Attach sub-components to main Tabs component
const TabsWithComponents = Tabs as typeof Tabs & {
    Analytics: typeof AnalyticsTabs;
    Detection: typeof DetectionTabs;
    Camera: typeof CameraTabs;
  };
  
  TabsWithComponents.Analytics = AnalyticsTabs;
  TabsWithComponents.Detection = DetectionTabs;
  TabsWithComponents.Camera = CameraTabs;
  
  export default TabsWithComponents;
  export { AnalyticsTabs, DetectionTabs, CameraTabs };
  export type { 
    TabsProps,
    AnalyticsTabsProps,
    DetectionTabsProps,
    CameraTabsProps,
    TabItem,
    AnalyticsTab,
    DetectionTab,
    CameraTab,
    TabsVariant,
    TabsSize,
    TabsOrientation
  };