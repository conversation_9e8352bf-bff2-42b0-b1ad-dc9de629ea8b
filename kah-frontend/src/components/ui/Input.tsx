import React, { forwardRef, useState, useId } from 'react';
import { 
  Eye, 
  EyeOff, 
  Search, 
  X, 
  AlertCircle, 
  CheckCircle,
  Info,
  Calendar,
  Clock,
  Hash,
  AtSign,
  Lock,
  User,
  Camera,
  Zap
} from 'lucide-react';

// Input variant types
type InputVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'ghost';

type InputSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type InputType = 
  | 'text'
  | 'email'
  | 'password'
  | 'search'
  | 'url'
  | 'tel'
  | 'number'
  | 'date'
  | 'time'
  | 'datetime-local'
  | 'color'
  | 'file'
  | 'range'
  | 'hidden';

// Icon mapping for input types
const typeIcons = {
  search: Search,
  email: AtSign,
  password: Lock,
  tel: Hash,
  url: Zap,
  date: Calendar,
  time: Clock,
  number: Hash,
  file: Camera,
  default: User,
} as const;

// Base input props
interface BaseInputProps {
  variant?: InputVariant;
  size?: InputSize;
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  info?: string;
  required?: boolean;
  disabled?: boolean;
  loading?: boolean;
  clearable?: boolean;
  showPasswordToggle?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  leftAddon?: React.ReactNode;
  rightAddon?: React.ReactNode;
  autoIcon?: boolean;
  fullWidth?: boolean;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  onClear?: () => void;
}

type InputProps = BaseInputProps & 
  Omit<React.InputHTMLAttributes<HTMLInputElement>, keyof BaseInputProps>;

// Variant styles using Tailwind classes
const variantStyles: Record<InputVariant, string> = {
  default: `
    border-gray-300 bg-white text-gray-900 placeholder-gray-500
    focus:border-primary-500 focus:ring-primary-500
    dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-primary-400 dark:focus:ring-primary-400
  `,
  primary: `
    border-primary-300 bg-white text-gray-900 placeholder-gray-500
    focus:border-primary-500 focus:ring-primary-500
    dark:border-primary-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-primary-400 dark:focus:ring-primary-400
  `,
  secondary: `
    border-gray-200 bg-gray-50 text-gray-900 placeholder-gray-400
    focus:border-gray-400 focus:ring-gray-400 focus:bg-white
    dark:border-gray-700 dark:bg-gray-900 dark:text-gray-100 dark:placeholder-gray-500
    dark:focus:border-gray-500 dark:focus:ring-gray-500 dark:focus:bg-gray-800
  `,
  success: `
    border-green-300 bg-white text-gray-900 placeholder-gray-500
    focus:border-green-500 focus:ring-green-500
    dark:border-green-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-green-400 dark:focus:ring-green-400
  `,
  warning: `
    border-yellow-300 bg-white text-gray-900 placeholder-gray-500
    focus:border-yellow-500 focus:ring-yellow-500
    dark:border-yellow-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-yellow-400 dark:focus:ring-yellow-400
  `,
  error: `
    border-red-300 bg-white text-gray-900 placeholder-gray-500
    focus:border-red-500 focus:ring-red-500
    dark:border-red-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-red-400 dark:focus:ring-red-400
  `,
  detection: `
    border-cyan-300 bg-white text-gray-900 placeholder-gray-500
    focus:border-cyan-500 focus:ring-cyan-500
    dark:border-cyan-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-cyan-400 dark:focus:ring-cyan-400
  `,
  ghost: `
    border-transparent bg-transparent text-gray-900 placeholder-gray-500
    focus:border-gray-300 focus:ring-gray-300 focus:bg-white
    hover:bg-gray-50
    dark:text-gray-100 dark:placeholder-gray-400
    dark:focus:border-gray-600 dark:focus:ring-gray-600 dark:focus:bg-gray-800
    dark:hover:bg-gray-800
  `,
};

// Size styles
const sizeStyles: Record<InputSize, string> = {
  xs: 'h-6 px-2 text-xs',
  sm: 'h-8 px-3 text-sm',
  md: 'h-10 px-3 text-sm',
  lg: 'h-12 px-4 text-base',
  xl: 'h-14 px-5 text-lg',
};

// Icon size styles
const iconSizeStyles: Record<InputSize, string> = {
  xs: 'w-3 h-3',
  sm: 'w-3.5 h-3.5',
  md: 'w-4 h-4',
  lg: 'w-5 h-5',
  xl: 'w-6 h-6',
};

// Label size styles
const labelSizeStyles: Record<InputSize, string> = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-sm',
  lg: 'text-base',
  xl: 'text-lg',
};

// Base input styles
const baseStyles = `
  block w-full rounded-md border
  shadow-sm transition-all duration-200 ease-in-out
  focus:outline-none focus:ring-1
  disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50
  dark:disabled:bg-gray-900
`;

// Addon styles
const addonStyles = `
  px-3 bg-gray-50 border border-r-0 text-gray-500 text-sm
  dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400
  first:rounded-l-md last:rounded-r-md last:border-r last:border-l-0
`;

// Loading styles
const loadingStyles = `
  relative
  after:absolute after:inset-0 after:bg-gradient-to-r 
  after:from-transparent after:via-white/20 after:to-transparent
  after:translate-x-[-100%] after:animate-pulse
  dark:after:via-gray-700/20
`;

// High contrast mode styles
const highContrastStyles = `
  contrast-more:border-2 contrast-more:border-current
`;

/**
 * Input Component
 * 
 * A versatile input component for the VisionGuard Detection Analytics platform.
 * Supports multiple variants, validation states, icons, addons, and advanced features.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Validation states with visual feedback
 * - Auto-icons based on input type
 * - Left/right icons and addons
 * - Password visibility toggle
 * - Clearable inputs
 * - Loading states
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Basic input
 * <Input 
 *   label="Camera Name" 
 *   placeholder="Enter camera name..."
 *   required
 * />
 * 
 * // Search input with auto-icon
 * <Input 
 *   type="search"
 *   placeholder="Search sessions..."
 *   autoIcon
 *   clearable
 * />
 * 
 * // Password input with toggle
 * <Input 
 *   type="password"
 *   label="Password"
 *   showPasswordToggle
 *   required
 * />
 * 
 * // Input with validation
 * <Input 
 *   label="Confidence Threshold"
 *   type="number"
 *   min={0}
 *   max={100}
 *   variant="detection"
 *   success="Valid threshold selected"
 * />
 * 
 * // Input with addons
 * <Input 
 *   label="Detection Zone"
 *   leftAddon={<ZoneIcon />}
 *   rightAddon="px"
 *   placeholder="Enter zone size"
 * />
 * ```
 */
const Input = forwardRef<HTMLInputElement, InputProps>(({
  variant = 'default',
  size = 'md',
  type = 'text',
  label,
  description,
  error,
  success,
  warning,
  info,
  required = false,
  disabled = false,
  loading = false,
  clearable = false,
  showPasswordToggle = false,
  leftIcon,
  rightIcon,
  leftAddon,
  rightAddon,
  autoIcon = false,
  fullWidth = true,
  className = '',
  inputClassName = '',
  labelClassName = '',
  errorClassName = '',
  value,
  onClear,
  onChange,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false);
  const [internalValue, setInternalValue] = useState(value || '');
  const inputId = useId();

  // Determine actual input type (for password toggle)
  const actualType = type === 'password' && showPassword ? 'text' : type;

  // Determine variant based on validation state
  const getVariant = () => {
    if (error) return 'error';
    if (success) return 'success';
    if (warning) return 'warning';
    return variant;
  };

  const currentVariant = getVariant();

  // Auto-select icon based on input type
  const getAutoIcon = () => {
    if (!autoIcon) return null;
    const IconComponent = typeIcons[type as keyof typeof typeIcons] || typeIcons.default;
    return <IconComponent className={iconSizeStyles[size]} />;
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    onChange?.(e);
  };

  // Handle clear
  const handleClear = () => {
    setInternalValue('');
    onClear?.();
    // Create synthetic event for controlled components
    if (onChange) {
      const syntheticEvent = {
        target: { value: '' },
        currentTarget: { value: '' },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }
  };

  // Determine if clear button should be shown
  const showClearButton = clearable && !disabled && !loading && (value || internalValue);

  // Determine if password toggle should be shown
  const showPasswordButton = showPasswordToggle && type === 'password' && !disabled;

  // Get status icon
  const getStatusIcon = () => {
    if (error) return <AlertCircle className={`${iconSizeStyles[size]} text-red-500`} />;
    if (success) return <CheckCircle className={`${iconSizeStyles[size]} text-green-500`} />;
    if (warning) return <AlertCircle className={`${iconSizeStyles[size]} text-yellow-500`} />;
    if (info) return <Info className={`${iconSizeStyles[size]} text-blue-500`} />;
    return null;
  };

  // Combine input styles
  const inputStyles = [
    baseStyles,
    variantStyles[currentVariant],
    sizeStyles[size],
    highContrastStyles,
    loading && loadingStyles,
    leftAddon && 'rounded-l-none border-l-0',
    rightAddon && 'rounded-r-none border-r-0',
    (leftIcon || getAutoIcon()) && 'pl-10',
    (rightIcon || showClearButton || showPasswordButton || getStatusIcon()) && 'pr-10',
    !fullWidth && 'w-auto',
    inputClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Status message
  const statusMessage = error || success || warning || info;
  const statusColor = error ? 'text-red-600 dark:text-red-400' :
                     success ? 'text-green-600 dark:text-green-400' :
                     warning ? 'text-yellow-600 dark:text-yellow-400' :
                     'text-blue-600 dark:text-blue-400';

  return (
    <div className={`${fullWidth ? 'w-full' : 'w-auto'} ${className}`}>
      {/* Label */}
      {label && (
        <label
          htmlFor={inputId}
          className={`
            block font-medium text-gray-700 dark:text-gray-300 mb-1
            ${labelSizeStyles[size]}
            ${labelClassName}
          `}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Description */}
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          {description}
        </p>
      )}

      {/* Input Container */}
      <div className="relative flex">
        {/* Left Addon */}
        {leftAddon && (
          <div className={`${addonStyles} border-r-0`}>
            {leftAddon}
          </div>
        )}

        {/* Input Field */}
        <div className="relative flex-1">
          <input
            ref={ref}
            id={inputId}
            type={actualType}
            value={value !== undefined ? value : internalValue}
            onChange={handleChange}
            disabled={disabled || loading}
            className={inputStyles}
            aria-invalid={!!error}
            aria-describedby={
              statusMessage ? `${inputId}-message` : undefined
            }
            {...props}
          />

          {/* Left Icon */}
          {(leftIcon || getAutoIcon()) && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <div className="text-gray-400 dark:text-gray-500">
                {leftIcon || getAutoIcon()}
              </div>
            </div>
          )}

          {/* Right Icons */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {/* Status Icon */}
            {getStatusIcon()}

            {/* Custom Right Icon */}
            {rightIcon && !showClearButton && !showPasswordButton && (
              <div className="text-gray-400 dark:text-gray-500">
                {rightIcon}
              </div>
            )}

            {/* Clear Button */}
            {showClearButton && (
              <button
                type="button"
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                aria-label="Clear input"
              >
                <X className={iconSizeStyles[size]} />
              </button>
            )}

            {/* Password Toggle */}
            {showPasswordButton && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? (
                  <EyeOff className={iconSizeStyles[size]} />
                ) : (
                  <Eye className={iconSizeStyles[size]} />
                )}
              </button>
            )}

            {/* Loading Spinner */}
            {loading && (
              <div className="animate-spin text-gray-400 dark:text-gray-500">
                <div className={`${iconSizeStyles[size]} border-2 border-current border-t-transparent rounded-full`} />
              </div>
            )}
          </div>
        </div>

        {/* Right Addon */}
        {rightAddon && (
          <div className={`${addonStyles} border-l-0`}>
            {rightAddon}
          </div>
        )}
      </div>

      {/* Status Message */}
      {statusMessage && (
        <p
          id={`${inputId}-message`}
          className={`mt-1 text-sm ${statusColor} ${errorClassName}`}
        >
          {statusMessage}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

/**
 * InputGroup Component
 * 
 * Groups multiple related inputs with consistent styling
 */
interface InputGroupProps {
  children: React.ReactNode;
  label?: string;
  description?: string;
  required?: boolean;
  className?: string;
  spacing?: 'tight' | 'normal' | 'loose';
}

const InputGroup = forwardRef<HTMLDivElement, InputGroupProps>(({
  children,
  label,
  description,
  required = false,
  className = '',
  spacing = 'normal',
  ...props
}, ref) => {
  const spacingStyles = {
    tight: 'space-y-2',
    normal: 'space-y-4',
    loose: 'space-y-6',
  };

  return (
    <div
      ref={ref}
      className={`${spacingStyles[spacing]} ${className}`}
      {...props}
    >
      {label && (
        <div className="mb-2">
          <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </h3>
          {description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
});

InputGroup.displayName = 'InputGroup';

/**
 * SearchInput Component
 * 
 * Specialized input for search functionality
 */
interface SearchInputProps extends Omit<InputProps, 'type' | 'autoIcon'> {
  onSearch?: (value: string) => void;
  searchOnType?: boolean;
  debounceMs?: number;
}

const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(({
  onSearch,
  searchOnType = true,
  debounceMs = 300,
  clearable = true,
  placeholder = 'Search...',
  ...props
}, ref) => {
  const [searchValue, setSearchValue] = useState('');
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);

    if (searchOnType && onSearch) {
      // Clear existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set new timer
      const timer = setTimeout(() => {
        onSearch(value);
      }, debounceMs);
      setDebounceTimer(timer);
    }

    props.onChange?.(e);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch(searchValue);
    }
    props.onKeyPress?.(e);
  };

  return (
    <Input
      ref={ref}
      type="search"
      autoIcon
      clearable={clearable}
      placeholder={placeholder}
      value={searchValue}
      onChange={handleChange}
      onKeyPress={handleKeyPress}
      {...props}
    />
  );
});

SearchInput.displayName = 'SearchInput';

// Attach sub-components to main Input component
const InputWithComponents = Input as typeof Input & {
  Group: typeof InputGroup;
  Search: typeof SearchInput;
};

InputWithComponents.Group = InputGroup;
InputWithComponents.Search = SearchInput;

export default InputWithComponents;
export type { 
  InputProps,
  InputGroupProps,
  SearchInputProps,
  InputVariant,
  InputSize,
  InputType
};
export { InputGroup, SearchInput };