import React, { forwardRef } from 'react';
import { ChevronRight, MoreVertical } from 'lucide-react';

// Card variant types
type CardVariant = 
  | 'default'
  | 'elevated'
  | 'bordered'
  | 'ghost'
  | 'metric'
  | 'detection'
  | 'zone'
  | 'session';

type CardSize = 'sm' | 'md' | 'lg' | 'xl';

// Base card props
interface BaseCardProps {
  variant?: CardVariant;
  size?: CardSize;
  interactive?: boolean;
  hoverable?: boolean;
  selected?: boolean;
  loading?: boolean;
  className?: string;
  children: React.ReactNode;
}

// Card header props
interface CardHeaderProps {
  title?: string;
  subtitle?: string;
  badge?: React.ReactNode;
  action?: React.ReactNode;
  icon?: React.ReactNode;
  className?: string;
}

// Card content props
interface CardContentProps {
  className?: string;
  children: React.ReactNode;
}

// Card footer props
interface CardFooterProps {
  className?: string;
  children: React.ReactNode;
}

// Card action props
interface CardActionsProps {
  align?: 'left' | 'center' | 'right' | 'between';
  className?: string;
  children: React.ReactNode;
}

// Main card props
type CardProps = BaseCardProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseCardProps>;

// Variant styles using Tailwind classes
const variantStyles: Record<CardVariant, string> = {
  default: `
    bg-white border border-gray-200 shadow-sm
    dark:bg-gray-800 dark:border-gray-700
  `,
  elevated: `
    bg-white border border-gray-200 shadow-lg
    dark:bg-gray-800 dark:border-gray-700 dark:shadow-2xl
  `,
  bordered: `
    bg-white border-2 border-gray-300
    dark:bg-gray-800 dark:border-gray-600
  `,
  ghost: `
    bg-transparent border border-transparent
    hover:bg-gray-50 hover:border-gray-200
    dark:hover:bg-gray-800 dark:hover:border-gray-700
  `,
  metric: `
    bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-md
    hover:shadow-lg hover:from-gray-50 hover:to-gray-100
    dark:from-gray-800 dark:to-gray-900 dark:border-gray-700
    dark:hover:from-gray-700 dark:hover:to-gray-800
  `,
  detection: `
    bg-white border-l-4 border-l-primary-500 border-r border-t border-b border-gray-200 shadow-sm
    hover:border-l-primary-600 hover:shadow-md
    dark:bg-gray-800 dark:border-r-gray-700 dark:border-t-gray-700 dark:border-b-gray-700
    dark:border-l-primary-600
  `,
  zone: `
    bg-white border-l-4 border-l-cyan-500 border-r border-t border-b border-gray-200 shadow-sm
    hover:border-l-cyan-600 hover:shadow-md
    dark:bg-gray-800 dark:border-r-gray-700 dark:border-t-gray-700 dark:border-b-gray-700
    dark:border-l-cyan-600
  `,
  session: `
    bg-white border border-gray-200 shadow-sm overflow-hidden
    hover:shadow-md hover:border-gray-300
    dark:bg-gray-800 dark:border-gray-700 dark:hover:border-gray-600
  `,
};

// Size styles
const sizeStyles: Record<CardSize, string> = {
  sm: 'p-3',
  md: 'p-4',
  lg: 'p-6',
  xl: 'p-8',
};

// Interactive styles
const interactiveStyles = `
  cursor-pointer transition-all duration-200 ease-in-out
  hover:scale-[1.02] active:scale-[0.98]
  focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
  dark:focus:ring-offset-gray-900
`;

// Selected styles
const selectedStyles = `
  ring-2 ring-primary-500 border-primary-500
  dark:border-primary-600 dark:ring-primary-600
`;

// Loading styles
const loadingStyles = `
  relative overflow-hidden
  before:absolute before:inset-0 before:bg-gradient-to-r 
  before:from-transparent before:via-white/20 before:to-transparent
  before:translate-x-[-100%] before:animate-pulse
  dark:before:via-gray-700/20
`;

// Base card styles
const baseStyles = `
  rounded-lg
  transition-all duration-200 ease-in-out
`;

// High contrast mode styles
const highContrastStyles = `
  contrast-more:border-2 contrast-more:border-current
`;

/**
 * Card Header Component
 */
const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(({
  title,
  subtitle,
  badge,
  action,
  icon,
  className = '',
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={`
        flex items-start justify-between gap-3 mb-4 last:mb-0
        ${className}
      `}
      {...props}
    >
      <div className="flex items-start gap-3 min-w-0 flex-1">
        {icon && (
          <div className="flex-shrink-0 mt-0.5">
            <div className="w-5 h-5 text-gray-500 dark:text-gray-400">
              {icon}
            </div>
          </div>
        )}
        <div className="min-w-0 flex-1">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {subtitle}
            </p>
          )}
        </div>
        {badge && (
          <div className="flex-shrink-0">
            {badge}
          </div>
        )}
      </div>
      {action && (
        <div className="flex-shrink-0">
          {action}
        </div>
      )}
    </div>
  );
});

CardHeader.displayName = 'CardHeader';

/**
 * Card Content Component
 */
const CardContent = forwardRef<HTMLDivElement, CardContentProps>(({
  className = '',
  children,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={`text-gray-700 dark:text-gray-300 ${className}`}
      {...props}
    >
      {children}
    </div>
  );
});

CardContent.displayName = 'CardContent';

/**
 * Card Footer Component
 */
const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(({
  className = '',
  children,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={`
        mt-4 pt-4 border-t border-gray-200 dark:border-gray-700
        text-sm text-gray-600 dark:text-gray-400
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
});

CardFooter.displayName = 'CardFooter';

/**
 * Card Actions Component
 */
const CardActions = forwardRef<HTMLDivElement, CardActionsProps>(({
  align = 'right',
  className = '',
  children,
  ...props
}, ref) => {
  const alignStyles = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div
      ref={ref}
      className={`
        flex items-center gap-2 mt-4
        ${alignStyles[align]}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
});

CardActions.displayName = 'CardActions';

/**
 * Main Card Component
 * 
 * A versatile card component designed for the VisionGuard Detection Analytics platform.
 * Supports multiple variants, sizes, and interactive states.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from sm to xl
 * - Interactive and hoverable states
 * - Selected state for multi-selection
 * - Loading state with shimmer effect
 * - Composable header, content, footer, and actions
 * - Theme-aware styling
 * - Accessibility compliant
 * 
 * @example
 * ```tsx
 * // Basic metric card
 * <Card variant="metric" size="md">
 *   <CardHeader 
 *     title="Active Cameras" 
 *     badge={<Badge variant="success">Live</Badge>}
 *     icon={<CameraIcon />}
 *   />
 *   <CardContent>
 *     <div className="text-3xl font-bold">24</div>
 *     <div className="text-sm text-green-600">+2 since yesterday</div>
 *   </CardContent>
 * </Card>
 * 
 * // Interactive session card
 * <Card variant="session" interactive onClick={handleSessionClick}>
 *   <CardHeader 
 *     title="Session #1234"
 *     subtitle="2024-01-15 14:30:22"
 *     action={<MoreVertical className="w-4 h-4" />}
 *   />
 *   <CardContent>
 *     <img src="/session-thumbnail.jpg" alt="Session thumbnail" />
 *   </CardContent>
 *   <CardFooter>
 *     Duration: 2h 34m • 127 detections
 *   </CardFooter>
 * </Card>
 * 
 * // Detection zone card
 * <Card variant="zone" selected>
 *   <CardHeader title="Entrance Zone" />
 *   <CardContent>
 *     Zone configuration and stats
 *   </CardContent>
 *   <CardActions>
 *     <Button size="sm" variant="secondary">Edit</Button>
 *     <Button size="sm" variant="primary">Activate</Button>
 *   </CardActions>
 * </Card>
 * ```
 */
const Card = forwardRef<HTMLDivElement, CardProps>(({
  variant = 'default',
  size = 'md',
  interactive = false,
  hoverable = false,
  selected = false,
  loading = false,
  className = '',
  children,
  ...props
}, ref) => {
  // Combine all styles
  const combinedClassName = [
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    highContrastStyles,
    (interactive || hoverable) && interactiveStyles,
    selected && selectedStyles,
    loading && loadingStyles,
    className,
  ]
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Determine element type based on interactivity
  const Component = interactive ? 'button' : 'div';
  
  // Props for interactive cards
  const interactiveProps = interactive ? {
    type: 'button' as const,
    'aria-pressed': selected,
  } : {};

  return (
    <Component
      ref={ref as any}
      className={combinedClassName}
      aria-busy={loading}
      {...interactiveProps}
      {...props}
    >
      {loading && (
        <div className="absolute inset-0 bg-white/50 dark:bg-gray-800/50 rounded-lg">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      )}
      {children}
    </Component>
  );
});

Card.displayName = 'Card';

// Attach sub-components to main Card component
const CardWithComponents = Card as typeof Card & {
  Header: typeof CardHeader;
  Content: typeof CardContent;
  Footer: typeof CardFooter;
  Actions: typeof CardActions;
};

CardWithComponents.Header = CardHeader;
CardWithComponents.Content = CardContent;
CardWithComponents.Footer = CardFooter;
CardWithComponents.Actions = CardActions;

export default CardWithComponents;
export type { 
  CardProps, 
  CardHeaderProps, 
  CardContentProps, 
  CardFooterProps, 
  CardActionsProps,
  CardVariant,
  CardSize
};
export { CardHeader, CardContent, CardFooter, CardActions };