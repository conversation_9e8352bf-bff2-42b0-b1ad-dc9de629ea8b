import React, { forwardRef, useState, useRef, useCallback, useId } from 'react';
import { 
  Volume2, 
  VolumeX, 
  Eye, 
  Settings, 
  Zap, 
  Timer, 
  TrendingUp,
  Target,
  Gauge,
  Sliders as SliderIcon,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';

// Slider variant types
type SliderVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'ghost';

type SliderSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type SliderType = 
  | 'default'
  | 'confidence'
  | 'volume'
  | 'opacity'
  | 'threshold'
  | 'sensitivity'
  | 'quality'
  | 'speed'
  | 'zoom'
  | 'brightness'
  | 'contrast'
  | 'timeline';

// Icon mapping for different slider types
const typeIcons = {
  confidence: Target,
  volume: Volume2,
  opacity: Eye,
  threshold: Gauge,
  sensitivity: Zap,
  quality: Settings,
  speed: Timer,
  zoom: TrendingUp,
  brightness: Settings,
  contrast: Settings,
  timeline: Timer,
  default: SliderIcon,
} as const;

// Step presets for different types
const typeSteps = {
  confidence: 1,
  volume: 5,
  opacity: 5,
  threshold: 1,
  sensitivity: 1,
  quality: 10,
  speed: 0.1,
  zoom: 0.1,
  brightness: 5,
  contrast: 5,
  timeline: 1,
  default: 1,
} as const;

// Range presets for different types
const typeRanges = {
  confidence: { min: 0, max: 100 },
  volume: { min: 0, max: 100 },
  opacity: { min: 0, max: 100 },
  threshold: { min: 0, max: 100 },
  sensitivity: { min: 0, max: 100 },
  quality: { min: 0, max: 100 },
  speed: { min: 0.1, max: 2.0 },
  zoom: { min: 1.0, max: 10.0 },
  brightness: { min: 0, max: 100 },
  contrast: { min: 0, max: 100 },
  timeline: { min: 0, max: 100 },
  default: { min: 0, max: 100 },
} as const;

// Base slider props
interface BaseSliderProps {
  variant?: SliderVariant;
  size?: SliderSize;
  type?: SliderType;
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  info?: string;
  min?: number;
  max?: number;
  step?: number;
  value?: number;
  defaultValue?: number;
  disabled?: boolean;
  loading?: boolean;
  showValue?: boolean;
  showTicks?: boolean;
  showMinMax?: boolean;
  showPercentage?: boolean;
  formatValue?: (value: number) => string;
  icon?: React.ReactNode;
  autoIcon?: boolean;
  vertical?: boolean;
  reversed?: boolean;
  tickMarks?: number[];
  marks?: Array<{ value: number; label: string }>;
  className?: string;
  sliderClassName?: string;
  labelClassName?: string;
  trackClassName?: string;
  thumbClassName?: string;
  onChange?: (value: number) => void;
  onChangeCommitted?: (value: number) => void;
}

type SliderProps = BaseSliderProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseSliderProps>;

// Variant styles for track and thumb
const variantStyles: Record<SliderVariant, { track: string; thumb: string; fill: string }> = {
  default: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-gray-300 dark:border-gray-600 shadow-md',
    fill: 'bg-gray-400 dark:bg-gray-500',
  },
  primary: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-primary-500 dark:border-primary-600 shadow-md',
    fill: 'bg-primary-500 dark:bg-primary-600',
  },
  secondary: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-gray-400 dark:border-gray-500 shadow-md',
    fill: 'bg-gray-500 dark:bg-gray-400',
  },
  success: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-green-500 dark:border-green-600 shadow-md',
    fill: 'bg-green-500 dark:bg-green-600',
  },
  warning: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-yellow-500 dark:border-yellow-600 shadow-md',
    fill: 'bg-yellow-500 dark:bg-yellow-600',
  },
  error: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-red-500 dark:border-red-600 shadow-md',
    fill: 'bg-red-500 dark:bg-red-600',
  },
  detection: {
    track: 'bg-gray-200 dark:bg-gray-700',
    thumb: 'bg-white border-cyan-500 dark:border-cyan-600 shadow-md',
    fill: 'bg-cyan-500 dark:bg-cyan-600',
  },
  ghost: {
    track: 'bg-transparent border border-gray-300 dark:border-gray-600',
    thumb: 'bg-gray-100 border-gray-400 dark:bg-gray-700 dark:border-gray-500 shadow-sm',
    fill: 'bg-gray-300 dark:bg-gray-600',
  },
};

// Size styles
const sizeStyles = {
  track: {
    xs: 'h-1',
    sm: 'h-1.5',
    md: 'h-2',
    lg: 'h-2.5',
    xl: 'h-3',
  },
  thumb: {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7',
  },
  icon: {
    xs: 'w-3 h-3',
    sm: 'w-3.5 h-3.5',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6',
  },
  label: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
  },
  value: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
  },
} as const;

// Base track styles
const baseTrackStyles = `
  relative rounded-full cursor-pointer
  transition-all duration-200 ease-in-out
`;

// Base thumb styles
const baseThumbStyles = `
  absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2
  rounded-full border-2 cursor-pointer
  transition-all duration-200 ease-in-out
  hover:scale-110 focus:scale-110 active:scale-95
  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500
  dark:focus:ring-offset-gray-800
`;

// Base fill styles
const baseFillStyles = `
  absolute top-0 left-0 h-full rounded-full
  transition-all duration-200 ease-in-out
  pointer-events-none
`;

// High contrast mode styles
const highContrastStyles = `
  contrast-more:border-2 contrast-more:border-current
`;

/**
 * Slider Component
 * 
 * A versatile range slider component for the VisionGuard Detection Analytics platform.
 * Supports confidence thresholds, volume controls, opacity settings, and various detection parameters.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Specialized types for different use cases
 * - Auto-icons and value formatting
 * - Tick marks and custom marks
 * - Vertical and reversed orientations
 * - Real-time and committed value updates
 * - Loading states and validation
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Confidence threshold slider
 * <Slider 
 *   type="confidence"
 *   label="Detection Confidence"
 *   value={confidence}
 *   onChange={setConfidence}
 *   showValue
 *   showPercentage
 *   variant="detection"
 * />
 * 
 * // Volume control
 * <Slider 
 *   type="volume"
 *   label="Alert Volume"
 *   value={volume}
 *   onChange={setVolume}
 *   autoIcon
 *   showValue
 * />
 * 
 * // Opacity control with marks
 * <Slider 
 *   type="opacity"
 *   label="Overlay Opacity"
 *   value={opacity}
 *   onChange={setOpacity}
 *   marks={[
 *     { value: 0, label: 'Hidden' },
 *     { value: 50, label: 'Semi' },
 *     { value: 100, label: 'Opaque' }
 *   ]}
 * />
 * 
 * // Timeline scrubber
 * <Slider 
 *   type="timeline"
 *   label="Video Timeline"
 *   min={0}
 *   max={duration}
 *   value={currentTime}
 *   onChange={setCurrentTime}
 *   formatValue={(v) => formatTime(v)}
 *   showTicks
 * />
 * ```
 */
const Slider = forwardRef<HTMLDivElement, SliderProps>(({
  variant = 'default',
  size = 'md',
  type = 'default',
  label,
  description,
  error,
  success,
  warning,
  info,
  min,
  max,
  step,
  value,
  defaultValue,
  disabled = false,
  loading = false,
  showValue = false,
  showTicks = false,
  showMinMax = false,
  showPercentage = false,
  formatValue,
  icon,
  autoIcon = false,
  vertical = false,
  reversed = false,
  tickMarks = [],
  marks = [],
  className = '',
  sliderClassName = '',
  labelClassName = '',
  trackClassName = '',
  thumbClassName = '',
  onChange,
  onChangeCommitted,
  ...props
}, ref) => {
  // Use type presets if not explicitly provided
  const minValue = min ?? typeRanges[type].min;
  const maxValue = max ?? typeRanges[type].max;
  const stepValue = step ?? typeSteps[type];
  
  const [internalValue, setInternalValue] = useState(value ?? defaultValue ?? minValue);
  const [isDragging, setIsDragging] = useState(false);
  const trackRef = useRef<HTMLDivElement>(null);
  const sliderId = useId();

  // Current value (controlled or uncontrolled)
  const currentValue = value !== undefined ? value : internalValue;

  // Determine variant based on validation state
  const getVariant = () => {
    if (error) return 'error';
    if (success) return 'success';
    if (warning) return 'warning';
    return variant;
  };

  const currentVariant = getVariant();

  // Get auto-icon based on type
  const getAutoIcon = () => {
    if (!autoIcon || icon) return null;
    const IconComponent = typeIcons[type];
    return <IconComponent className={sizeStyles.icon[size]} />;
  };

  // Format value for display
  const getFormattedValue = useCallback((val: number) => {
    if (formatValue) return formatValue(val);
    if (showPercentage) return `${Math.round(val)}%`;
    if (type === 'speed') return `${val.toFixed(1)}x`;
    if (type === 'zoom') return `${val.toFixed(1)}x`;
    return Math.round(val).toString();
  }, [formatValue, showPercentage, type]);

  // Calculate percentage for positioning
  const getPercentage = useCallback((val: number) => {
    const percentage = ((val - minValue) / (maxValue - minValue)) * 100;
    return reversed ? 100 - percentage : percentage;
  }, [minValue, maxValue, reversed]);

  // Calculate value from percentage
  const getValueFromPercentage = useCallback((percentage: number) => {
    const adjustedPercentage = reversed ? 100 - percentage : percentage;
    const rawValue = minValue + (adjustedPercentage / 100) * (maxValue - minValue);
    const steppedValue = Math.round(rawValue / stepValue) * stepValue;
    return Math.max(minValue, Math.min(maxValue, steppedValue));
  }, [minValue, maxValue, stepValue, reversed]);

  // Handle value change
  const handleValueChange = useCallback((newValue: number) => {
    const clampedValue = Math.max(minValue, Math.min(maxValue, newValue));
    
    if (value === undefined) {
      setInternalValue(clampedValue);
    }
    onChange?.(clampedValue);
  }, [minValue, maxValue, value, onChange]);

  // Handle mouse/touch events
  const handlePointerDown = useCallback((e: React.PointerEvent) => {
    if (disabled || loading) return;
    
    e.preventDefault();
    setIsDragging(true);
    
    const rect = trackRef.current?.getBoundingClientRect();
    if (!rect) return;

    const getValueFromEvent = (event: PointerEvent) => {
      const percentage = vertical
        ? ((rect.bottom - event.clientY) / rect.height) * 100
        : ((event.clientX - rect.left) / rect.width) * 100;
      return getValueFromPercentage(Math.max(0, Math.min(100, percentage)));
    };

    const handlePointerMove = (event: PointerEvent) => {
      handleValueChange(getValueFromEvent(event));
    };

    const handlePointerUp = (event: PointerEvent) => {
      setIsDragging(false);
      const finalValue = getValueFromEvent(event);
      onChangeCommitted?.(finalValue);
      
      document.removeEventListener('pointermove', handlePointerMove);
      document.removeEventListener('pointerup', handlePointerUp);
    };

    // Initial value update
    handleValueChange(getValueFromEvent(e.nativeEvent));

    document.addEventListener('pointermove', handlePointerMove);
    document.addEventListener('pointerup', handlePointerUp);
  }, [disabled, loading, vertical, getValueFromPercentage, handleValueChange, onChangeCommitted]);

  // Handle keyboard events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled || loading) return;

    let newValue = currentValue;
    const largeStep = stepValue * 10;

    switch (e.key) {
      case 'ArrowRight':
      case 'ArrowUp':
        e.preventDefault();
        newValue = currentValue + (e.shiftKey ? largeStep : stepValue);
        break;
      case 'ArrowLeft':
      case 'ArrowDown':
        e.preventDefault();
        newValue = currentValue - (e.shiftKey ? largeStep : stepValue);
        break;
      case 'Home':
        e.preventDefault();
        newValue = minValue;
        break;
      case 'End':
        e.preventDefault();
        newValue = maxValue;
        break;
      case 'PageUp':
        e.preventDefault();
        newValue = currentValue + largeStep;
        break;
      case 'PageDown':
        e.preventDefault();
        newValue = currentValue - largeStep;
        break;
    }

    if (newValue !== currentValue) {
      handleValueChange(newValue);
      onChangeCommitted?.(newValue);
    }
  }, [disabled, loading, currentValue, stepValue, minValue, maxValue, handleValueChange, onChangeCommitted]);

  // Generate tick marks
  const generateTicks = useCallback(() => {
    if (!showTicks && tickMarks.length === 0) return [];
    
    if (tickMarks.length > 0) {
      return tickMarks.map(tick => getPercentage(tick));
    }

    // Auto-generate ticks
    const tickCount = 5;
    const ticks = [];
    for (let i = 0; i <= tickCount; i++) {
      const tickValue = minValue + (i / tickCount) * (maxValue - minValue);
      ticks.push(getPercentage(tickValue));
    }
    return ticks;
  }, [showTicks, tickMarks, minValue, maxValue, getPercentage]);

  // Get status icon
  const getStatusIcon = () => {
    if (error) return <AlertCircle className={`${sizeStyles.icon[size]} text-red-500`} />;
    if (success) return <CheckCircle className={`${sizeStyles.icon[size]} text-green-500`} />;
    if (warning) return <AlertCircle className={`${sizeStyles.icon[size]} text-yellow-500`} />;
    if (info) return <Info className={`${sizeStyles.icon[size]} text-blue-500`} />;
    return null;
  };

  // Combine styles
  const trackStyles = [
    baseTrackStyles,
    sizeStyles.track[size],
    variantStyles[currentVariant].track,
    highContrastStyles,
    vertical ? 'w-2 h-full' : 'w-full h-2',
    trackClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const thumbStyles = [
    baseThumbStyles,
    sizeStyles.thumb[size],
    variantStyles[currentVariant].thumb,
    isDragging && 'scale-110 shadow-lg',
    thumbClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const fillStyles = [
    baseFillStyles,
    variantStyles[currentVariant].fill,
  ]
    .filter(Boolean)
    .join(' ');

  // Calculate positions
  const thumbPosition = getPercentage(currentValue);
  const fillWidth = reversed ? 100 - thumbPosition : thumbPosition;

  // Status message
  const statusMessage = error || success || warning || info;
  const statusColor = error ? 'text-red-600 dark:text-red-400' :
                     success ? 'text-green-600 dark:text-green-400' :
                     warning ? 'text-yellow-600 dark:text-yellow-400' :
                     'text-blue-600 dark:text-blue-400';

  return (
    <div
      ref={ref}
      className={`w-full ${className}`}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          {/* Icon */}
          {(icon || getAutoIcon()) && (
            <div className="text-gray-500 dark:text-gray-400">
              {icon || getAutoIcon()}
            </div>
          )}
          
          {/* Label */}
          {label && (
            <label
              htmlFor={sliderId}
              className={`
                font-medium text-gray-700 dark:text-gray-300
                ${sizeStyles.label[size]}
                ${labelClassName}
              `}
            >
              {label}
            </label>
          )}
          
          {/* Status Icon */}
          {getStatusIcon()}
        </div>

        {/* Value Display */}
        {showValue && (
          <div className={`
            font-mono font-medium text-gray-900 dark:text-gray-100
            ${sizeStyles.value[size]}
          `}>
            {getFormattedValue(currentValue)}
          </div>
        )}
      </div>

      {/* Description */}
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          {description}
        </p>
      )}

      {/* Slider Container */}
      <div className={`relative ${vertical ? 'h-32 w-8' : 'h-8 w-full'} ${sliderClassName}`}>
        {/* Track */}
        <div
          ref={trackRef}
          className={trackStyles}
          onPointerDown={handlePointerDown}
          role="slider"
          tabIndex={disabled ? -1 : 0}
          aria-valuemin={minValue}
          aria-valuemax={maxValue}
          aria-valuenow={currentValue}
          aria-valuetext={getFormattedValue(currentValue)}
          aria-orientation={vertical ? 'vertical' : 'horizontal'}
          aria-disabled={disabled}
          aria-describedby={statusMessage ? `${sliderId}-message` : undefined}
          onKeyDown={handleKeyDown}
        >
          {/* Fill */}
          <div
            className={fillStyles}
            style={{
              [vertical ? 'height' : 'width']: `${fillWidth}%`,
              [vertical ? 'bottom' : 'left']: 0,
            }}
          />

          {/* Tick Marks */}
          {(showTicks || tickMarks.length > 0) && (
            <div className="absolute inset-0 pointer-events-none">
              {generateTicks().map((tickPosition, index) => (
                <div
                  key={index}
                  className="absolute w-0.5 h-2 bg-gray-400 dark:bg-gray-500 -translate-x-1/2"
                  style={{
                    [vertical ? 'bottom' : 'left']: `${tickPosition}%`,
                    [vertical ? 'left' : 'top']: '50%',
                    transform: vertical ? 'translateY(50%)' : 'translateX(-50%)',
                  }}
                />
              ))}
            </div>
          )}

          {/* Custom Marks */}
          {marks.map((mark) => (
            <div
              key={mark.value}
              className="absolute pointer-events-none"
              style={{
                [vertical ? 'bottom' : 'left']: `${getPercentage(mark.value)}%`,
                [vertical ? 'left' : 'top']: '100%',
                transform: vertical ? 'translateY(50%)' : 'translateX(-50%)',
              }}
            >
              <div className="w-0.5 h-3 bg-gray-600 dark:bg-gray-400 mb-1" />
              <div className="text-xs text-gray-600 dark:text-gray-400 whitespace-nowrap">
                {mark.label}
              </div>
            </div>
          ))}

          {/* Thumb */}
          <div
            className={thumbStyles}
            style={{
              [vertical ? 'bottom' : 'left']: `${thumbPosition}%`,
            }}
          >
            {/* Loading Spinner */}
            {loading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className={`${sizeStyles.icon[size]} animate-spin border-2 border-gray-400 border-t-transparent rounded-full`} />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Min/Max Labels */}
      {showMinMax && (
        <div className="flex justify-between mt-1">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {getFormattedValue(minValue)}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {getFormattedValue(maxValue)}
          </span>
        </div>
      )}

      {/* Status Message */}
      {statusMessage && (
        <p
          id={`${sliderId}-message`}
          className={`mt-2 text-sm ${statusColor}`}
        >
          {statusMessage}
        </p>
      )}
    </div>
  );
});

Slider.displayName = 'Slider';

/**
 * ConfidenceSlider Component
 * 
 * Specialized slider for detection confidence thresholds
 */
interface ConfidenceSliderProps extends Omit<SliderProps, 'type' | 'min' | 'max'> {
  optimumRange?: [number, number];
}

const ConfidenceSlider = forwardRef<HTMLDivElement, ConfidenceSliderProps>(({
  optimumRange = [70, 90],
  variant = 'detection',
  showValue = true,
  showPercentage = true,
  autoIcon = true,
  label = 'Detection Confidence',
  description = 'Minimum confidence level for object detection',
  ...props
}, ref) => {
  const marks = [
    { value: 0, label: 'Low' },
    { value: optimumRange[0], label: 'Good' },
    { value: optimumRange[1], label: 'High' },
    { value: 100, label: 'Max' },
  ];

  return (
    <Slider
      ref={ref}
      type="confidence"
      variant={variant}
      label={label}
      description={description}
      showValue={showValue}
      showPercentage={showPercentage}
      autoIcon={autoIcon}
      marks={marks}
      {...props}
    />
  );
});

ConfidenceSlider.displayName = 'ConfidenceSlider';

/**
 * VolumeSlider Component
 * 
 * Specialized slider for volume controls
 */
interface VolumeSliderProps extends Omit<SliderProps, 'type' | 'min' | 'max'> {
  muted?: boolean;
  onMuteToggle?: (muted: boolean) => void;
}

const VolumeSlider = forwardRef<HTMLDivElement, VolumeSliderProps>(({
  muted = false,
  onMuteToggle,
  variant = 'primary',
  showValue = true,
  autoIcon = true,
  label = 'Volume',
  value,
  onChange,
  ...props
}, ref) => {
  const effectiveValue = muted ? 0 : value;
  
  const handleChange = (newValue: number) => {
    if (muted && newValue > 0) {
      onMuteToggle?.(false);
    }
    onChange?.(newValue);
  };

  const VolumeIcon = effectiveValue === 0 ? VolumeX : Volume2;

  return (
    <div className="flex items-center space-x-3">
      {onMuteToggle && (
        <button
          type="button"
          onClick={() => onMuteToggle(!muted)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          aria-label={muted ? 'Unmute' : 'Mute'}
        >
          <VolumeIcon className="w-5 h-5" />
        </button>
      )}
      
      <div className="flex-1">
        <Slider
          ref={ref}
          type="volume"
          variant={variant}
          label={label}
          showValue={showValue}
          autoIcon={!onMuteToggle && autoIcon}
          value={effectiveValue}
          onChange={handleChange}
          {...props}
        />
      </div>
    </div>
  );
});

VolumeSlider.displayName = 'VolumeSlider';

/**
 * TimelineSlider Component
 * 
 * Specialized slider for video timeline scrubbing
 */
interface TimelineSliderProps extends Omit<SliderProps, 'type' | 'formatValue'> {
  duration: number;
  currentTime: number;
  buffered?: Array<{ start: number; end: number }>;
  formatTime?: (seconds: number) => string;
}

const TimelineSlider = forwardRef<HTMLDivElement, TimelineSliderProps>(({
  duration,
  currentTime,
  buffered = [],
  formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  },
  variant = 'primary',
  min = 0,
  step = 1,
  ...props
}, ref) => {
  return (
    <div className="relative">
      {/* Buffered Segments */}
      {buffered.map((segment, index) => (
        <div
          key={index}
          className="absolute top-0 h-full bg-gray-300 dark:bg-gray-600 rounded-full pointer-events-none"
          style={{
            left: `${(segment.start / duration) * 100}%`,
            width: `${((segment.end - segment.start) / duration) * 100}%`,
          }}
        />
      ))}
      
      <Slider
        ref={ref}
        type="timeline"
        variant={variant}
        min={min}
        max={duration}
        step={step}
        value={currentTime}
        formatValue={formatTime}
        showValue
        showMinMax
        {...props}
      />
    </div>
  );
});

TimelineSlider.displayName = 'TimelineSlider';

// Continuing from the previous Slider.tsx file...

// Attach sub-components to main Slider component
const SliderWithComponents = Slider as typeof Slider & {
    Confidence: typeof ConfidenceSlider;
    Volume: typeof VolumeSlider;
    Timeline: typeof TimelineSlider;
  };
  
  SliderWithComponents.Confidence = ConfidenceSlider;
  SliderWithComponents.Volume = VolumeSlider;
  SliderWithComponents.Timeline = TimelineSlider;
  
  export default SliderWithComponents;
  export type { 
    SliderProps,
    ConfidenceSliderProps,
    VolumeSliderProps,
    TimelineSliderProps,
    SliderVariant,
    SliderSize,
    SliderType
  };
  export { ConfidenceSlider, VolumeSlider, TimelineSlider };