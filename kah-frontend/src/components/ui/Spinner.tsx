import React, { forwardRef } from 'react';
import { Loader2, RotateCw, RefreshCw, Zap, Camera, Eye, Activity, Cpu } from 'lucide-react';

// Spinner variant types
type SpinnerVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'processing'
  | 'loading'
  | 'pulse'
  | 'dots'
  | 'bars'
  | 'camera'
  | 'analysis';

type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

type SpinnerSpeed = 'slow' | 'normal' | 'fast';

// Spinner type for different animations
type SpinnerType = 'spin' | 'pulse' | 'bounce' | 'ping' | 'custom';

// Icon mapping for different variants
const variantIcons = {
  default: Loader2,
  primary: Loader2,
  secondary: RotateCw,
  success: RefreshCw,
  warning: RefreshCw,
  error: RefreshCw,
  detection: Eye,
  processing: Cpu,
  loading: Loader2,
  pulse: Activity,
  camera: Camera,
  analysis: Zap,
} as const;

// Base spinner props
interface BaseSpinnerProps {
  variant?: SpinnerVariant;
  size?: SpinnerSize;
  speed?: SpinnerSpeed;
  type?: SpinnerType;
  label?: string;
  showLabel?: boolean;
  overlay?: boolean;
  centered?: boolean;
  className?: string;
  iconClassName?: string;
  labelClassName?: string;
}

type SpinnerProps = BaseSpinnerProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseSpinnerProps>;

// Variant color styles
const variantStyles: Record<SpinnerVariant, string> = {
  default: 'text-gray-500 dark:text-gray-400',
  primary: 'text-primary-500 dark:text-primary-400',
  secondary: 'text-gray-400 dark:text-gray-500',
  success: 'text-green-500 dark:text-green-400',
  warning: 'text-yellow-500 dark:text-yellow-400',
  error: 'text-red-500 dark:text-red-400',
  detection: 'text-cyan-500 dark:text-cyan-400',
  processing: 'text-blue-500 dark:text-blue-400',
  loading: 'text-primary-500 dark:text-primary-400',
  pulse: 'text-green-500 dark:text-green-400',
  dots: 'text-gray-500 dark:text-gray-400',
  bars: 'text-primary-500 dark:text-primary-400',
  camera: 'text-purple-500 dark:text-purple-400',
  analysis: 'text-orange-500 dark:text-orange-400',
};

// Size styles
const sizeStyles: Record<SpinnerSize, string> = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
  '2xl': 'w-16 h-16',
};

// Speed styles
const speedStyles: Record<SpinnerSpeed, string> = {
  slow: 'animate-spin [animation-duration:2s]',
  normal: 'animate-spin [animation-duration:1s]',
  fast: 'animate-spin [animation-duration:0.5s]',
};

// Label size styles
const labelSizeStyles: Record<SpinnerSize, string> = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-sm',
  lg: 'text-base',
  xl: 'text-lg',
  '2xl': 'text-xl',
};

// Base spinner styles
const baseStyles = `
  inline-flex items-center justify-center
`;

// Overlay styles
const overlayStyles = `
  fixed inset-0 z-50
  bg-white/80 dark:bg-gray-900/80
  backdrop-blur-sm
  flex items-center justify-center
`;

// Centered styles
const centeredStyles = `
  flex items-center justify-center
  min-h-[200px] w-full
`;

/**
 * Dots Spinner Component
 */
const DotsSpinner = ({ size, variant }: { size: SpinnerSize; variant: SpinnerVariant }) => {
  const dotSize = {
    xs: 'w-1 h-1',
    sm: 'w-1.5 h-1.5',
    md: 'w-2 h-2',
    lg: 'w-2.5 h-2.5',
    xl: 'w-3 h-3',
    '2xl': 'w-4 h-4',
  }[size];

  return (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className={`
            ${dotSize} rounded-full ${variantStyles[variant]}
            animate-pulse
            [animation-delay:${index * 0.2}s]
            [animation-duration:1.5s]
          `}
          style={{ animationDelay: `${index * 0.2}s` }}
        />
      ))}
    </div>
  );
};

/**
 * Bars Spinner Component
 */
const BarsSpinner = ({ size, variant }: { size: SpinnerSize; variant: SpinnerVariant }) => {
  const barHeight = {
    xs: 'h-3',
    sm: 'h-4',
    md: 'h-6',
    lg: 'h-8',
    xl: 'h-12',
    '2xl': 'h-16',
  }[size];

  const barWidth = {
    xs: 'w-0.5',
    sm: 'w-0.5',
    md: 'w-1',
    lg: 'w-1',
    xl: 'w-1.5',
    '2xl': 'w-2',
  }[size];

  return (
    <div className="flex items-end space-x-1">
      {[0, 1, 2, 3].map((index) => (
        <div
          key={index}
          className={`
            ${barWidth} ${barHeight} ${variantStyles[variant]}
            animate-pulse rounded-full
            [animation-delay:${index * 0.15}s]
            [animation-duration:1.2s]
          `}
          style={{ 
            animationDelay: `${index * 0.15}s`,
            transform: 'scaleY(0.4)',
            transformOrigin: 'bottom',
          }}
        />
      ))}
    </div>
  );
};

/**
 * Pulse Spinner Component
 */
const PulseSpinner = ({ size, variant }: { size: SpinnerSize; variant: SpinnerVariant }) => {
  return (
    <div className="relative">
      <div className={`${sizeStyles[size]} ${variantStyles[variant]} rounded-full`}>
        <div className="absolute inset-0 rounded-full animate-ping opacity-20 bg-current" />
        <div className="absolute inset-0 rounded-full animate-pulse opacity-40 bg-current" />
        <div className="relative w-full h-full rounded-full bg-current" />
      </div>
    </div>
  );
};

/**
 * Main Spinner Component
 * 
 * A versatile loading spinner component for the VisionGuard Detection Analytics platform.
 * Supports multiple animation types, variants, and states for different loading scenarios.
 * 
 * Features:
 * - Multiple animation types (spin, pulse, dots, bars)
 * - Detection-specific variants with themed colors
 * - Size variants from xs to 2xl
 * - Speed control (slow, normal, fast)
 * - Optional labels with accessibility
 * - Overlay mode for full-screen loading
 * - Centered mode for content areas
 * - Theme-aware styling
 * - Screen reader support
 * 
 * @example
 * ```tsx
 * // Basic loading spinner
 * <Spinner variant="primary" size="md" />
 * 
 * // Detection processing
 * <Spinner variant="detection" label="Analyzing video..." showLabel />
 * 
 * // Camera loading with custom animation
 * <Spinner variant="camera" type="pulse" size="lg" />
 * 
 * // Full screen overlay
 * <Spinner variant="processing" overlay label="Processing video stream..." showLabel />
 * 
 * // Dots animation for subtle loading
 * <Spinner variant="dots" type="custom" size="sm" />
 * 
 * // Fast processing indicator
 * <Spinner variant="analysis" speed="fast" label="Real-time analysis" />
 * ```
 */
const Spinner = forwardRef<HTMLDivElement, SpinnerProps>(({
  variant = 'default',
  size = 'md',
  speed = 'normal',
  type = 'spin',
  label,
  showLabel = false,
  overlay = false,
  centered = false,
  className = '',
  iconClassName = '',
  labelClassName = '',
  ...props
}, ref) => {
  // Get the appropriate icon for the variant
  const IconComponent = variantIcons[variant as keyof typeof variantIcons] || Loader2;

  // Determine animation classes based on type
  const getAnimationClasses = () => {
    switch (type) {
      case 'spin':
        return speedStyles[speed];
      case 'pulse':
        return 'animate-pulse';
      case 'bounce':
        return 'animate-bounce';
      case 'ping':
        return 'animate-ping';
      default:
        return '';
    }
  };

  // Render different spinner types
  const renderSpinner = () => {
    if (variant === 'dots' || type === 'custom') {
      if (variant === 'dots') {
        return <DotsSpinner size={size} variant={variant} />;
      }
      if (variant === 'bars') {
        return <BarsSpinner size={size} variant={variant} />;
      }
      if (variant === 'pulse' || type === 'pulse') {
        return <PulseSpinner size={size} variant={variant} />;
      }
    }

    // Default icon spinner
    return (
      <IconComponent
        className={`
          ${sizeStyles[size]}
          ${variantStyles[variant]}
          ${getAnimationClasses()}
          ${iconClassName}
        `}
        aria-hidden="true"
      />
    );
  };

  // Container styles
  const containerStyles = [
    baseStyles,
    overlay && overlayStyles,
    centered && !overlay && centeredStyles,
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Label element
  const labelElement = (label || showLabel) && (
    <span
      className={`
        ml-2 ${labelSizeStyles[size]} ${variantStyles[variant]} font-medium
        ${labelClassName}
      `}
    >
      {label || 'Loading...'}
    </span>
  );

  return (
    <div
      ref={ref}
      className={containerStyles}
      role="status"
      aria-live="polite"
      aria-label={label || 'Loading'}
      {...props}
    >
      <div className="flex items-center">
        {renderSpinner()}
        {labelElement}
      </div>
      <span className="sr-only">
        {label || 'Loading, please wait...'}
      </span>
    </div>
  );
});

Spinner.displayName = 'Spinner';

/**
 * LoadingOverlay Component
 * 
 * A specialized overlay spinner for full-screen loading states
 */
interface LoadingOverlayProps extends Omit<SpinnerProps, 'overlay' | 'centered'> {
  visible: boolean;
  backdrop?: boolean;
  children?: React.ReactNode;
}

const LoadingOverlay = forwardRef<HTMLDivElement, LoadingOverlayProps>(({
  visible,
  backdrop = true,
  children,
  variant = 'primary',
  size = 'xl',
  label = 'Loading...',
  showLabel = true,
  className = '',
  ...props
}, ref) => {
  if (!visible) return null;

  return (
    <div
      ref={ref}
      className={`
        fixed inset-0 z-50 flex items-center justify-center
        ${backdrop ? 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm' : ''}
        ${className}
      `}
      {...props}
    >
      <div className="flex flex-col items-center space-y-4">
        <Spinner
          variant={variant}
          size={size}
          label={label}
          showLabel={showLabel}
        />
        {children}
      </div>
    </div>
  );
});

LoadingOverlay.displayName = 'LoadingOverlay';

/**
 * InlineSpinner Component
 * 
 * A specialized spinner for inline loading states (buttons, inputs, etc.)
 */
interface InlineSpinnerProps extends Omit<SpinnerProps, 'overlay' | 'centered' | 'showLabel'> {
  replacing?: React.ReactNode;
}

const InlineSpinner = forwardRef<HTMLDivElement, InlineSpinnerProps>(({
  replacing,
  size = 'sm',
  variant = 'primary',
  speed = 'fast',
  className = '',
  ...props
}, ref) => {
  return (
    <Spinner
      ref={ref}
      variant={variant}
      size={size}
      speed={speed}
      className={`inline-flex ${className}`}
      aria-label="Loading"
      {...props}
    />
  );
});

InlineSpinner.displayName = 'InlineSpinner';

/**
 * ProcessingSpinner Component
 * 
 * Specialized spinner for video/image processing with contextual styling
 */
interface ProcessingSpinnerProps extends Omit<SpinnerProps, 'variant'> {
  processingType?: 'video' | 'image' | 'detection' | 'analysis' | 'export';
  progress?: number;
}

const ProcessingSpinner = forwardRef<HTMLDivElement, ProcessingSpinnerProps>(({
  processingType = 'detection',
  progress,
  size = 'lg',
  showLabel = true,
  label,
  className = '',
  ...props
}, ref) => {
  const processingVariants = {
    video: 'camera' as const,
    image: 'processing' as const,
    detection: 'detection' as const,
    analysis: 'analysis' as const,
    export: 'primary' as const,
  };

  const processingLabels = {
    video: 'Processing video...',
    image: 'Processing image...',
    detection: 'Detecting objects...',
    analysis: 'Analyzing data...',
    export: 'Exporting data...',
  };

  const variant = processingVariants[processingType];
  const defaultLabel = label || processingLabels[processingType];

  return (
    <div className={`flex flex-col items-center space-y-3 ${className}`}>
      <Spinner
        ref={ref}
        variant={variant}
        size={size}
        label={defaultLabel}
        showLabel={showLabel}
        type="pulse"
        {...props}
      />
      {progress !== undefined && (
        <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-primary-500 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          />
        </div>
      )}
      {progress !== undefined && (
        <span className="text-xs text-gray-600 dark:text-gray-400">
          {Math.round(progress)}% complete
        </span>
      )}
    </div>
  );
});

ProcessingSpinner.displayName = 'ProcessingSpinner';

// Attach sub-components to main Spinner component
const SpinnerWithComponents = Spinner as typeof Spinner & {
  Overlay: typeof LoadingOverlay;
  Inline: typeof InlineSpinner;
  Processing: typeof ProcessingSpinner;
};

SpinnerWithComponents.Overlay = LoadingOverlay;
SpinnerWithComponents.Inline = InlineSpinner;
SpinnerWithComponents.Processing = ProcessingSpinner;

export default SpinnerWithComponents;
export type { 
  SpinnerProps,
  LoadingOverlayProps,
  InlineSpinnerProps,
  ProcessingSpinnerProps,
  SpinnerVariant,
  SpinnerSize,
  SpinnerSpeed,
  SpinnerType
};
export { LoadingOverlay, InlineSpinner, ProcessingSpinner };