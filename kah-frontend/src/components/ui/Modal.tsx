import React, { forwardRef, useEffect, useRef, useCallback } from 'react';
import { 
  X, 
  <PERSON>tings, 
  Camera, 
  Eye, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  HelpCircle,
  Download,
  Upload,
  Trash2,
  Save,
  RotateCcw,
  Zap,
  Users,
  Car,
  Box,
  MapPin
} from 'lucide-react';

// Modal variant types
type ModalVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'ghost';

type ModalSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';

type ModalType = 
  | 'default'
  | 'settings'
  | 'camera'
  | 'detection'
  | 'zone'
  | 'export'
  | 'import'
  | 'confirmation'
  | 'alert'
  | 'help'
  | 'wizard';

// Icon mapping for different modal types
const typeIcons = {
  settings: Settings,
  camera: Camera,
  detection: Eye,
  zone: MapPin,
  export: Download,
  import: Upload,
  confirmation: HelpCircle,
  alert: AlertTriangle,
  help: Info,
  wizard: Zap,
  default: Settings,
} as const;

// Base modal props
interface BaseModalProps {
  variant?: ModalVariant;
  size?: ModalSize;
  type?: ModalType;
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  preventBodyScroll?: boolean;
  centered?: boolean;
  fullScreen?: boolean;
  icon?: React.ReactNode;
  autoIcon?: boolean;
  loading?: boolean;
  className?: string;
  overlayClassName?: string;
  contentClassName?: string;
  headerClassName?: string;
  bodyClassName?: string;
  footerClassName?: string;
  onOverlayClick?: () => void;
  onEscapePress?: () => void;
}

// Modal header props
interface ModalHeaderProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  showCloseButton?: boolean;
  onClose?: () => void;
  className?: string;
  children?: React.ReactNode;
}

// Modal body props
interface ModalBodyProps {
  className?: string;
  children: React.ReactNode;
}

// Modal footer props
interface ModalFooterProps {
  className?: string;
  children: React.ReactNode;
  align?: 'left' | 'center' | 'right' | 'between';
}

type ModalProps = BaseModalProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseModalProps>;

// Variant styles
const variantStyles: Record<ModalVariant, string> = {
  default: `
    border-gray-200 dark:border-gray-700
  `,
  primary: `
    border-primary-200 dark:border-primary-700
  `,
  secondary: `
    border-gray-300 dark:border-gray-600
  `,
  success: `
    border-green-200 dark:border-green-700
  `,
  warning: `
    border-yellow-200 dark:border-yellow-700
  `,
  error: `
    border-red-200 dark:border-red-700
  `,
  detection: `
    border-cyan-200 dark:border-cyan-700
  `,
  ghost: `
    border-transparent
  `,
};

// Size styles
const sizeStyles: Record<ModalSize, string> = {
  xs: 'max-w-xs',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  full: 'max-w-full mx-4',
};

// Base modal styles
const baseOverlayStyles = `
  fixed inset-0 z-50 flex items-center justify-center
  bg-black/50 dark:bg-black/70
  backdrop-blur-sm
  transition-opacity duration-300 ease-out
`;

const baseContentStyles = `
  relative w-full bg-white dark:bg-gray-800
  rounded-lg shadow-xl border
  transition-all duration-300 ease-out
  transform
  max-h-[90vh] overflow-hidden
  flex flex-col
`;

// Animation classes
const enterStyles = `
  opacity-100 scale-100 translate-y-0
`;

const exitStyles = `
  opacity-0 scale-95 translate-y-4
`;

/**
 * Modal Header Component
 */
const ModalHeader = forwardRef<HTMLDivElement, ModalHeaderProps>(({
  title,
  description,
  icon,
  showCloseButton = true,
  onClose,
  className = '',
  children,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={`
        flex items-start justify-between p-6 border-b border-gray-200 dark:border-gray-700
        ${className}
      `}
      {...props}
    >
      <div className="flex items-start space-x-3 flex-1 min-w-0">
        {/* Icon */}
        {icon && (
          <div className="flex-shrink-0 mt-1">
            <div className="w-5 h-5 text-gray-500 dark:text-gray-400">
              {icon}
            </div>
          </div>
        )}
        
        {/* Content */}
        <div className="flex-1 min-w-0">
          {title && (
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 leading-6">
              {title}
            </h2>
          )}
          {description && (
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {description}
            </p>
          )}
          {children}
        </div>
      </div>

      {/* Close Button */}
      {showCloseButton && onClose && (
        <button
          type="button"
          onClick={onClose}
          className="
            flex-shrink-0 ml-4 p-1 rounded-md text-gray-400 
            hover:text-gray-600 hover:bg-gray-100
            dark:hover:text-gray-200 dark:hover:bg-gray-700
            focus:outline-none focus:ring-2 focus:ring-primary-500
            transition-colors duration-200
          "
          aria-label="Close modal"
        >
          <X className="w-5 h-5" />
        </button>
      )}
    </div>
  );
});

ModalHeader.displayName = 'ModalHeader';

/**
 * Modal Body Component
 */
const ModalBody = forwardRef<HTMLDivElement, ModalBodyProps>(({
  className = '',
  children,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={`
        flex-1 p-6 overflow-y-auto
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
});

ModalBody.displayName = 'ModalBody';

/**
 * Modal Footer Component
 */
const ModalFooter = forwardRef<HTMLDivElement, ModalFooterProps>(({
  align = 'right',
  className = '',
  children,
  ...props
}, ref) => {
  const alignStyles = {
    left: 'justify-start',
    center: 'justify-center', 
    right: 'justify-end',
    between: 'justify-between',
  };

  return (
    <div
      ref={ref}
      className={`
        flex items-center gap-3 p-6 border-t border-gray-200 dark:border-gray-700
        ${alignStyles[align]}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
});

ModalFooter.displayName = 'ModalFooter';

/**
 * Main Modal Component
 * 
 * A versatile modal dialog component for the VisionGuard Detection Analytics platform.
 * Supports settings dialogs, configuration wizards, confirmations, and specialized detection modals.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to full screen
 * - Specialized types for different use cases
 * - Auto-icons based on modal type
 * - Overlay click and escape key handling
 * - Body scroll prevention
 * - Loading states and animations
 * - Composable header, body, and footer
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Settings modal
 * <Modal 
 *   type="settings"
 *   isOpen={showSettings}
 *   onClose={() => setShowSettings(false)}
 *   title="Camera Settings"
 *   size="lg"
 * >
 *   <Modal.Body>
 *     <SettingsForm />
 *   </Modal.Body>
 *   <Modal.Footer>
 *     <Button variant="secondary" onClick={onClose}>Cancel</Button>
 *     <Button variant="primary" onClick={onSave}>Save</Button>
 *   </Modal.Footer>
 * </Modal>
 * 
 * // Detection configuration
 * <Modal 
 *   type="detection"
 *   variant="detection"
 *   isOpen={showDetectionConfig}
 *   onClose={closeConfig}
 *   title="Detection Model Configuration"
 *   description="Configure AI models and detection parameters"
 *   autoIcon
 * >
 *   <DetectionConfigForm />
 * </Modal>
 * 
 * // Confirmation dialog
 * <Modal 
 *   type="confirmation"
 *   variant="warning"
 *   isOpen={showConfirm}
 *   onClose={cancelAction}
 *   title="Delete Camera"
 *   description="Are you sure you want to delete this camera? This action cannot be undone."
 *   size="sm"
 * >
 *   <Modal.Footer align="between">
 *     <Button variant="secondary" onClick={cancelAction}>Cancel</Button>
 *     <Button variant="danger" onClick={confirmDelete}>Delete</Button>
 *   </Modal.Footer>
 * </Modal>
 * ```
 */
const Modal = forwardRef<HTMLDivElement, ModalProps>(({
  variant = 'default',
  size = 'md',
  type = 'default',
  isOpen,
  onClose,
  title,
  description,
  children,
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  preventBodyScroll = true,
  centered = true,
  fullScreen = false,
  icon,
  autoIcon = false,
  loading = false,
  className = '',
  overlayClassName = '',
  contentClassName = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  onOverlayClick,
  onEscapePress,
  ...props
}, ref) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<Element | null>(null);

  // Get auto-icon based on type
  const getAutoIcon = () => {
    if (!autoIcon || icon) return null;
    const IconComponent = typeIcons[type];
    return <IconComponent className="w-5 h-5" />;
  };

  // Handle overlay click
  const handleOverlayClick = useCallback((e: React.MouseEvent) => {
    if (e.target === overlayRef.current && closeOnOverlayClick) {
      onOverlayClick?.();
      onClose();
    }
  }, [closeOnOverlayClick, onClose, onOverlayClick]);

  // Handle escape key
  const handleEscapeKey = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && closeOnEscape && isOpen) {
      onEscapePress?.();
      onClose();
    }
  }, [closeOnEscape, isOpen, onClose, onEscapePress]);

  // Focus management
  useEffect(() => {
    if (isOpen) {
      // Store previously focused element
      previousActiveElement.current = document.activeElement;
      
      // Focus the modal content
      setTimeout(() => {
        if (contentRef.current) {
          const focusableElement = contentRef.current.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement;
          
          if (focusableElement) {
            focusableElement.focus();
          } else {
            contentRef.current.focus();
          }
        }
      }, 100);
    } else {
      // Restore focus to previously focused element
      if (previousActiveElement.current instanceof HTMLElement) {
        previousActiveElement.current.focus();
      }
    }
  }, [isOpen]);

  // Escape key listener
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => document.removeEventListener('keydown', handleEscapeKey);
    }
  }, [isOpen, handleEscapeKey]);

  // Body scroll prevention
  useEffect(() => {
    if (isOpen && preventBodyScroll) {
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      
      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [isOpen, preventBodyScroll]);

  // Focus trap
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Tab') {
      const focusableElements = contentRef.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
        
        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    }
  }, []);

  if (!isOpen) return null;

  // Combine styles
  const overlayStyles = [
    baseOverlayStyles,
    centered ? 'items-center' : 'items-start pt-16',
    isOpen ? 'opacity-100' : 'opacity-0',
    overlayClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const contentStyles = [
    baseContentStyles,
    variantStyles[variant],
    fullScreen ? 'max-w-full max-h-full m-0 rounded-none' : sizeStyles[size],
    isOpen ? enterStyles : exitStyles,
    contentClassName,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div
      ref={overlayRef}
      className={overlayStyles}
      onClick={handleOverlayClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby={title ? 'modal-title' : undefined}
      aria-describedby={description ? 'modal-description' : undefined}
      {...props}
    >
      <div
        ref={contentRef}
        className={contentStyles}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 z-10 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg flex items-center justify-center">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 animate-spin border-2 border-primary-500 border-t-transparent rounded-full" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Loading...
              </span>
            </div>
          </div>
        )}

        {/* Header */}
        {(title || description || showCloseButton) && (
          <ModalHeader
            title={title}
            description={description}
            icon={icon || getAutoIcon()}
            showCloseButton={showCloseButton}
            onClose={onClose}
            className={headerClassName}
          />
        )}

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>
      </div>
    </div>
  );
});

Modal.displayName = 'Modal';

/**
 * ConfirmationModal Component
 * 
 * Specialized modal for confirmation dialogs
 */
interface ConfirmationModalProps extends Omit<ModalProps, 'type' | 'children'> {
  confirmText?: string;
  cancelText?: string;
  confirmVariant?: 'primary' | 'danger' | 'warning';
  onConfirm: () => void;
  onCancel?: () => void;
  children?: React.ReactNode;
}

const ConfirmationModal = forwardRef<HTMLDivElement, ConfirmationModalProps>(({
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  onConfirm,
  onCancel,
  onClose,
  size = 'sm',
  variant = 'warning',
  children,
  ...props
}, ref) => {
  const handleCancel = () => {
    onCancel?.();
    onClose();
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal
      ref={ref}
      type="confirmation"
      size={size}
      variant={variant}
      autoIcon
      onClose={onClose}
      {...props}
    >
      {children && <ModalBody>{children}</ModalBody>}
      <ModalFooter align="between">
        <button
          type="button"
          onClick={handleCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
        >
          {cancelText}
        </button>
        <button
          type="button"
          onClick={handleConfirm}
          className={`
            px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2
            ${confirmVariant === 'danger' ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' :
              confirmVariant === 'warning' ? 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500' :
              'bg-primary-600 hover:bg-primary-700 focus:ring-primary-500'}
          `}
        >
          {confirmText}
        </button>
      </ModalFooter>
    </Modal>
  );
});

ConfirmationModal.displayName = 'ConfirmationModal';

/**
 * SettingsModal Component
 * 
 * Specialized modal for settings and configuration
 */
interface SettingsModalProps extends Omit<ModalProps, 'type'> {
  saveText?: string;
  cancelText?: string;
  resetText?: string;
  onSave?: () => void;
  onCancel?: () => void;
  onReset?: () => void;
  showReset?: boolean;
  saveDisabled?: boolean;
}

const SettingsModal = forwardRef<HTMLDivElement, SettingsModalProps>(({
  saveText = 'Save Changes',
  cancelText = 'Cancel',
  resetText = 'Reset',
  onSave,
  onCancel,
  onReset,
  onClose,
  showReset = false,
  saveDisabled = false,
  size = 'lg',
  variant = 'primary',
  children,
  ...props
}, ref) => {
  const handleCancel = () => {
    onCancel?.();
    onClose();
  };

  const handleSave = () => {
    onSave?.();
    // Don't auto-close on save, let parent handle it
  };

  const handleReset = () => {
    onReset?.();
  };

  return (
    <Modal
      ref={ref}
      type="settings"
      size={size}
      variant={variant}
      autoIcon
      onClose={onClose}
      {...props}
    >
      <ModalBody>{children}</ModalBody>
      <ModalFooter align={showReset ? 'between' : 'right'}>
        {showReset && (
          <button
            type="button"
            onClick={handleReset}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            {resetText}
          </button>
        )}
        
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            {cancelText}
          </button>
          <button
            type="button"
            onClick={handleSave}
            disabled={saveDisabled}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="w-4 h-4 mr-2" />
            {saveText}
          </button>
        </div>
      </ModalFooter>
    </Modal>
  );
});

SettingsModal.displayName = 'SettingsModal';

/**
 * DetectionModal Component
 * 
 * Specialized modal for detection configuration
 */
interface DetectionModalProps extends Omit<ModalProps, 'type' | 'variant'> {
  detectionType?: 'person' | 'vehicle' | 'object' | 'general';
}

const DetectionModal = forwardRef<HTMLDivElement, DetectionModalProps>(({
  detectionType = 'general',
  variant = 'detection',
  autoIcon = true,
  ...props
}, ref) => {
  return (
    <Modal
      ref={ref}
      type="detection"
      variant={variant}
      autoIcon={autoIcon}
      {...props}
    />
  );
});

DetectionModal.displayName = 'DetectionModal';

/**
 * ExportModal Component
 * 
 * Specialized modal for data export operations
 */
interface ExportModalProps extends Omit<ModalProps, 'type'> {
  exportProgress?: number;
  exportStatus?: 'idle' | 'preparing' | 'exporting' | 'complete' | 'error';
}

const ExportModal = forwardRef<HTMLDivElement, ExportModalProps>(({
  exportProgress,
  exportStatus = 'idle',
  loading = exportStatus === 'preparing' || exportStatus === 'exporting',
  variant = 'primary',
  autoIcon = true,
  ...props
}, ref) => {
  return (
    <Modal
      ref={ref}
      type="export"
      variant={variant}
      autoIcon={autoIcon}
      loading={loading}
      {...props}
    >
      {props.children}
      
      {/* Progress Bar */}
      {exportProgress !== undefined && exportStatus === 'exporting' && (
        <div className="px-6 pb-6">
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(100, Math.max(0, exportProgress))}%` }}
            />
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 text-center">
            {Math.round(exportProgress)}% complete
          </p>
        </div>
      )}
    </Modal>
  );
});

ExportModal.displayName = 'ExportModal';

// Attach sub-components to main Modal component
const ModalWithComponents = Modal as typeof Modal & {
  Header: typeof ModalHeader;
  Body: typeof ModalBody;
  Footer: typeof ModalFooter;
  Confirmation: typeof ConfirmationModal;
  Settings: typeof SettingsModal;
  Detection: typeof DetectionModal;
  Export: typeof ExportModal;
};

ModalWithComponents.Header = ModalHeader;
ModalWithComponents.Body = ModalBody;
ModalWithComponents.Footer = ModalFooter;
ModalWithComponents.Confirmation = ConfirmationModal;
ModalWithComponents.Settings = SettingsModal;
ModalWithComponents.Detection = DetectionModal;
ModalWithComponents.Export = ExportModal;

export default ModalWithComponents;
export type { 
  ModalProps,
  ModalHeaderProps,
  ModalBodyProps,
  ModalFooterProps,
  ConfirmationModalProps,
  SettingsModalProps,
  DetectionModalProps,
  ExportModalProps,
  ModalVariant,
  ModalSize,
  ModalType
};
export { 
  ModalHeader, 
  ModalBody, 
  ModalFooter, 
  ConfirmationModal, 
  SettingsModal, 
  DetectionModal, 
  ExportModal 
};