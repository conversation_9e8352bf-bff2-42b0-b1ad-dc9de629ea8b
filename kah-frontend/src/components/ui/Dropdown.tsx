import React, { forwardRef, useState, useRef, useEffect, useCallback } from 'react';
import { 
  MoreVertical, 
  MoreHorizontal,
  ChevronDown,
  Check,
  X,
  Settings,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  Eye,
  EyeOff,
  Play,
  Pause,
  Square,
  RotateCcw,
  RefreshCw,
  Share,
  Link,
  Camera,
  Users,
  Car,
  Box,
  MapPin,
  Zap,
  Shield,
  Bell,
  Info,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

// Dropdown variant types
type DropdownVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'minimal';

type DropdownSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type DropdownPlacement = 
  | 'bottom-start'
  | 'bottom-end'
  | 'top-start'
  | 'top-end'
  | 'left-start'
  | 'left-end'
  | 'right-start'
  | 'right-end';

type DropdownTrigger = 'click' | 'hover' | 'focus' | 'contextmenu';

// Dropdown item interface
interface DropdownItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  badge?: React.ReactNode;
  description?: string;
  disabled?: boolean;
  hidden?: boolean;
  destructive?: boolean;
  separator?: boolean;
  submenu?: DropdownItem[];
  shortcut?: string;
  href?: string;
  target?: string;
  onClick?: (item: DropdownItem) => void;
}

// Context menu presets
const contextMenuPresets = {
  camera: [
    { id: 'view', label: 'View Stream', icon: <Eye className="w-4 h-4" /> },
    { id: 'settings', label: 'Camera Settings', icon: <Settings className="w-4 h-4" /> },
    { id: 'separator1', separator: true },
    { id: 'start-recording', label: 'Start Recording', icon: <Play className="w-4 h-4" /> },
    { id: 'stop-recording', label: 'Stop Recording', icon: <Square className="w-4 h-4" /> },
    { id: 'separator2', separator: true },
    { id: 'copy-url', label: 'Copy Stream URL', icon: <Link className="w-4 h-4" /> },
    { id: 'export-data', label: 'Export Data', icon: <Download className="w-4 h-4" /> },
    { id: 'separator3', separator: true },
    { id: 'delete', label: 'Delete Camera', icon: <Trash2 className="w-4 h-4" />, destructive: true },
  ],
  detection: [
    { id: 'configure', label: 'Configure Model', icon: <Settings className="w-4 h-4" /> },
    { id: 'test', label: 'Test Detection', icon: <Zap className="w-4 h-4" /> },
    { id: 'separator1', separator: true },
    { id: 'enable', label: 'Enable Detection', icon: <Play className="w-4 h-4" /> },
    { id: 'disable', label: 'Disable Detection', icon: <Pause className="w-4 h-4" /> },
    { id: 'separator2', separator: true },
    { id: 'export-config', label: 'Export Configuration', icon: <Download className="w-4 h-4" /> },
    { id: 'reset', label: 'Reset to Defaults', icon: <RotateCcw className="w-4 h-4" /> },
  ],
  zone: [
    { id: 'edit', label: 'Edit Zone', icon: <Edit className="w-4 h-4" /> },
    { id: 'duplicate', label: 'Duplicate Zone', icon: <Copy className="w-4 h-4" /> },
    { id: 'separator1', separator: true },
    { id: 'activate', label: 'Activate Zone', icon: <Play className="w-4 h-4" /> },
    { id: 'deactivate', label: 'Deactivate Zone', icon: <Pause className="w-4 h-4" /> },
    { id: 'separator2', separator: true },
    { id: 'view-analytics', label: 'View Analytics', icon: <Eye className="w-4 h-4" /> },
    { id: 'export-data', label: 'Export Zone Data', icon: <Download className="w-4 h-4" /> },
    { id: 'separator3', separator: true },
    { id: 'delete', label: 'Delete Zone', icon: <Trash2 className="w-4 h-4" />, destructive: true },
  ],
  session: [
    { id: 'play', label: 'Play Session', icon: <Play className="w-4 h-4" /> },
    { id: 'download', label: 'Download Video', icon: <Download className="w-4 h-4" /> },
    { id: 'separator1', separator: true },
    { id: 'share', label: 'Share Session', icon: <Share className="w-4 h-4" /> },
    { id: 'copy-link', label: 'Copy Link', icon: <Link className="w-4 h-4" /> },
    { id: 'separator2', separator: true },
    { id: 'view-detections', label: 'View Detections', icon: <Eye className="w-4 h-4" /> },
    { id: 'export-data', label: 'Export Analytics', icon: <Download className="w-4 h-4" /> },
    { id: 'separator3', separator: true },
    { id: 'delete', label: 'Delete Session', icon: <Trash2 className="w-4 h-4" />, destructive: true },
  ],
  user: [
    { id: 'profile', label: 'User Profile', icon: <Users className="w-4 h-4" /> },
    { id: 'settings', label: 'Account Settings', icon: <Settings className="w-4 h-4" /> },
    { id: 'separator1', separator: true },
    { id: 'notifications', label: 'Notifications', icon: <Bell className="w-4 h-4" /> },
    { id: 'security', label: 'Security', icon: <Shield className="w-4 h-4" /> },
    { id: 'separator2', separator: true },
    { id: 'help', label: 'Help & Support', icon: <Info className="w-4 h-4" /> },
    { id: 'logout', label: 'Sign Out', icon: <X className="w-4 h-4" />, destructive: true },
  ],
} as const;

// Base dropdown props
interface BaseDropdownProps {
  variant?: DropdownVariant;
  size?: DropdownSize;
  placement?: DropdownPlacement;
  trigger?: DropdownTrigger | DropdownTrigger[];
  items: DropdownItem[];
  children: React.ReactNode;
  disabled?: boolean;
  closeOnSelect?: boolean;
  closeOnClickOutside?: boolean;
  closeOnEscape?: boolean;
  offset?: [number, number];
  maxHeight?: number;
  minWidth?: number;
  className?: string;
  menuClassName?: string;
  itemClassName?: string;
  onOpen?: () => void;
  onClose?: () => void;
  onItemSelect?: (item: DropdownItem) => void;
}

type DropdownProps = BaseDropdownProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseDropdownProps>;

// Variant styles
const variantStyles: Record<DropdownVariant, string> = {
  default: `
    bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
    shadow-lg ring-1 ring-black ring-opacity-5
  `,
  primary: `
    bg-white dark:bg-gray-800 border border-primary-200 dark:border-primary-700
    shadow-lg ring-1 ring-primary-500 ring-opacity-20
  `,
  secondary: `
    bg-gray-50 dark:bg-gray-900 border border-gray-300 dark:border-gray-600
    shadow-md ring-1 ring-black ring-opacity-5
  `,
  success: `
    bg-white dark:bg-gray-800 border border-green-200 dark:border-green-700
    shadow-lg ring-1 ring-green-500 ring-opacity-20
  `,
  warning: `
    bg-white dark:bg-gray-800 border border-yellow-200 dark:border-yellow-700
    shadow-lg ring-1 ring-yellow-500 ring-opacity-20
  `,
  error: `
    bg-white dark:bg-gray-800 border border-red-200 dark:border-red-700
    shadow-lg ring-1 ring-red-500 ring-opacity-20
  `,
  detection: `
    bg-white dark:bg-gray-800 border border-cyan-200 dark:border-cyan-700
    shadow-lg ring-1 ring-cyan-500 ring-opacity-20
  `,
  minimal: `
    bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-800
    shadow-sm
  `,
};

// Size styles
const sizeStyles: Record<DropdownSize, { menu: string; item: string; icon: string }> = {
  xs: {
    menu: 'py-1 min-w-32',
    item: 'px-2 py-1 text-xs',
    icon: 'w-3 h-3',
  },
  sm: {
    menu: 'py-1 min-w-36',
    item: 'px-3 py-1.5 text-sm',
    icon: 'w-3.5 h-3.5',
  },
  md: {
    menu: 'py-1 min-w-40',
    item: 'px-3 py-2 text-sm',
    icon: 'w-4 h-4',
  },
  lg: {
    menu: 'py-2 min-w-48',
    item: 'px-4 py-2.5 text-base',
    icon: 'w-5 h-5',
  },
  xl: {
    menu: 'py-2 min-w-56',
    item: 'px-5 py-3 text-lg',
    icon: 'w-6 h-6',
  },
};

// Base menu styles
const baseMenuStyles = `
  absolute z-50 rounded-md
  focus:outline-none
  transform transition-all duration-200 ease-out
  origin-top-left
`;

// Base item styles
const baseItemStyles = `
  flex items-center justify-between w-full text-left
  hover:bg-gray-50 dark:hover:bg-gray-700
  focus:bg-gray-50 dark:focus:bg-gray-700
  focus:outline-none
  transition-colors duration-150
  cursor-pointer
`;

// Separator styles
const separatorStyles = `
  border-t border-gray-200 dark:border-gray-600 my-1
`;

// Destructive item styles
const destructiveStyles = `
  text-red-600 dark:text-red-400
  hover:bg-red-50 dark:hover:bg-red-900/20
  focus:bg-red-50 dark:focus:bg-red-900/20
`;

// Disabled item styles
const disabledStyles = `
  opacity-50 cursor-not-allowed
  hover:bg-transparent dark:hover:bg-transparent
  focus:bg-transparent dark:focus:bg-transparent
`;

/**
 * Dropdown Component
 * 
 * A versatile dropdown menu component for the VisionGuard Detection Analytics platform.
 * Supports context menus, action menus, and specialized detection/camera operations.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Flexible placement options
 * - Multiple trigger types (click, hover, focus, context menu)
 * - Nested submenus and separators
 * - Keyboard navigation and shortcuts
 * - Context menu presets for common operations
 * - Icon and badge support
 * - Destructive action styling
 * - Full accessibility support
 * 
 * @example
 * ```tsx
 * // Camera context menu
 * <Dropdown 
 *   trigger="contextmenu"
 *   items={contextMenuPresets.camera}
 *   onItemSelect={handleCameraAction}
 * >
 *   <CameraCard camera={camera} />
 * </Dropdown>
 * 
 * // User menu
 * <Dropdown 
 *   trigger="click"
 *   placement="bottom-end"
 *   items={contextMenuPresets.user}
 *   variant="primary"
 * >
 *   <Button variant="ghost">
 *     <User className="w-4 h-4" />
 *   </Button>
 * </Dropdown>
 * 
 * // Custom action menu
 * <Dropdown 
 *   items={[
 *     { id: 'edit', label: 'Edit', icon: <Edit /> },
 *     { id: 'duplicate', label: 'Duplicate', icon: <Copy /> },
 *     { id: 'separator', separator: true },
 *     { id: 'delete', label: 'Delete', icon: <Trash2 />, destructive: true }
 *   ]}
 *   onItemSelect={handleAction}
 * >
 *   <Button variant="ghost" size="sm">
 *     <MoreVertical className="w-4 h-4" />
 *   </Button>
 * </Dropdown>
 * ```
 */
const Dropdown = forwardRef<HTMLDivElement, DropdownProps>(({
  variant = 'default',
  size = 'md',
  placement = 'bottom-start',
  trigger = 'click',
  items = [],
  children,
  disabled = false,
  closeOnSelect = true,
  closeOnClickOutside = true,
  closeOnEscape = true,
  offset = [0, 4],
  maxHeight = 320,
  minWidth,
  className = '',
  menuClassName = '',
  itemClassName = '',
  onOpen,
  onClose,
  onItemSelect,
  ...props
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  
  const triggerRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // Filter visible items
  const visibleItems = items.filter(item => !item.hidden);

  // Trigger types array
  const triggerTypes = Array.isArray(trigger) ? trigger : [trigger];

  // Calculate menu position
  const getMenuPosition = useCallback(() => {
    if (!triggerRef.current) return { top: 0, left: 0 };

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const [offsetX, offsetY] = offset;

    const positions = {
      'bottom-start': {
        top: triggerRect.bottom + offsetY,
        left: triggerRect.left + offsetX,
      },
      'bottom-end': {
        top: triggerRect.bottom + offsetY,
        left: triggerRect.right - offsetX,
        transform: 'translateX(-100%)',
      },
      'top-start': {
        top: triggerRect.top - offsetY,
        left: triggerRect.left + offsetX,
        transform: 'translateY(-100%)',
      },
      'top-end': {
        top: triggerRect.top - offsetY,
        left: triggerRect.right - offsetX,
        transform: 'translateX(-100%) translateY(-100%)',
      },
      'left-start': {
        top: triggerRect.top + offsetY,
        left: triggerRect.left - offsetX,
        transform: 'translateX(-100%)',
      },
      'left-end': {
        top: triggerRect.bottom - offsetY,
        left: triggerRect.left - offsetX,
        transform: 'translateX(-100%) translateY(-100%)',
      },
      'right-start': {
        top: triggerRect.top + offsetY,
        left: triggerRect.right + offsetX,
      },
      'right-end': {
        top: triggerRect.bottom - offsetY,
        left: triggerRect.right + offsetX,
        transform: 'translateY(-100%)',
      },
    };

    return positions[placement];
  }, [placement, offset]);

  // Open dropdown
  const openDropdown = useCallback(() => {
    if (disabled) return;
    setIsOpen(true);
    setHighlightedIndex(-1);
    onOpen?.();
  }, [disabled, onOpen]);

  // Close dropdown
  const closeDropdown = useCallback(() => {
    setIsOpen(false);
    setActiveSubmenu(null);
    setHighlightedIndex(-1);
    onClose?.();
  }, [onClose]);

  // Handle item selection
  const handleItemSelect = useCallback((item: DropdownItem) => {
    if (item.disabled || item.separator) return;

    if (item.href) {
      if (item.target === '_blank') {
        window.open(item.href, '_blank', 'noopener,noreferrer');
      } else {
        window.location.href = item.href;
      }
    }

    item.onClick?.(item);
    onItemSelect?.(item);

    if (closeOnSelect && !item.submenu) {
      closeDropdown();
    }
  }, [closeOnSelect, closeDropdown, onItemSelect]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => {
          const nextIndex = prev < visibleItems.length - 1 ? prev + 1 : 0;
          // Skip separators and disabled items
          const nextItem = visibleItems[nextIndex];
          if (nextItem?.separator || nextItem?.disabled) {
            return nextIndex < visibleItems.length - 1 ? nextIndex + 1 : 0;
          }
          return nextIndex;
        });
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => {
          const nextIndex = prev > 0 ? prev - 1 : visibleItems.length - 1;
          // Skip separators and disabled items
          const nextItem = visibleItems[nextIndex];
          if (nextItem?.separator || nextItem?.disabled) {
            return nextIndex > 0 ? nextIndex - 1 : visibleItems.length - 1;
          }
          return nextIndex;
        });
        break;
      case 'ArrowRight':
        if (highlightedIndex >= 0) {
          const item = visibleItems[highlightedIndex];
          if (item?.submenu) {
            setActiveSubmenu(item.id);
          }
        }
        break;
      case 'ArrowLeft':
        setActiveSubmenu(null);
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (highlightedIndex >= 0) {
          handleItemSelect(visibleItems[highlightedIndex]);
        }
        break;
      case 'Escape':
        if (closeOnEscape) {
          e.preventDefault();
          closeDropdown();
        }
        break;
    }
  }, [isOpen, visibleItems, highlightedIndex, handleItemSelect, closeOnEscape, closeDropdown]);

  // Handle trigger events
  const handleTriggerClick = useCallback((e: React.MouseEvent) => {
    if (triggerTypes.includes('click')) {
      e.preventDefault();
      e.stopPropagation();
      isOpen ? closeDropdown() : openDropdown();
    }
  }, [triggerTypes, isOpen, openDropdown, closeDropdown]);

  const handleTriggerContextMenu = useCallback((e: React.MouseEvent) => {
    if (triggerTypes.includes('contextmenu')) {
      e.preventDefault();
      e.stopPropagation();
      openDropdown();
    }
  }, [triggerTypes, openDropdown]);

  const handleTriggerMouseEnter = useCallback(() => {
    if (triggerTypes.includes('hover')) {
      openDropdown();
    }
  }, [triggerTypes, openDropdown]);

  const handleTriggerMouseLeave = useCallback(() => {
    if (triggerTypes.includes('hover')) {
      setTimeout(() => {
        if (!menuRef.current?.matches(':hover')) {
          closeDropdown();
        }
      }, 100);
    }
  }, [triggerTypes, closeDropdown]);

  const handleTriggerFocus = useCallback(() => {
    if (triggerTypes.includes('focus')) {
      openDropdown();
    }
  }, [triggerTypes, openDropdown]);

  const handleTriggerBlur = useCallback(() => {
    if (triggerTypes.includes('focus')) {
      setTimeout(() => {
        if (!menuRef.current?.contains(document.activeElement)) {
          closeDropdown();
        }
      }, 100);
    }
  }, [triggerTypes, closeDropdown]);

  // Close on click outside
  useEffect(() => {
    if (!isOpen || !closeOnClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (
        triggerRef.current && !triggerRef.current.contains(target) &&
        menuRef.current && !menuRef.current.contains(target)
      ) {
        closeDropdown();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, closeOnClickOutside, closeDropdown]);

  // Focus highlighted item
  useEffect(() => {
    if (highlightedIndex >= 0 && itemRefs.current[highlightedIndex]) {
      itemRefs.current[highlightedIndex]?.focus();
    }
  }, [highlightedIndex]);

  // Combine styles
  const menuStyles = [
    baseMenuStyles,
    variantStyles[variant],
    sizeStyles[size].menu,
    isOpen ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none',
    menuClassName,
  ]
    .filter(Boolean)
    .join(' ');

  const menuPosition = getMenuPosition();

  return (
    <div
      ref={ref}
      className={`relative inline-block ${className}`}
      {...props}
    >
      {/* Trigger */}
      <div
        ref={triggerRef}
        onClick={handleTriggerClick}
        onContextMenu={handleTriggerContextMenu}
        onMouseEnter={handleTriggerMouseEnter}
        onMouseLeave={handleTriggerMouseLeave}
        onFocus={handleTriggerFocus}
        onBlur={handleTriggerBlur}
        onKeyDown={handleKeyDown}
        className={disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      >
        {children}
      </div>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          ref={menuRef}
          className={menuStyles}
          style={{
            ...menuPosition,
            maxHeight: maxHeight,
            minWidth: minWidth || sizeStyles[size].menu.match(/min-w-(\w+)/)?.[1],
            zIndex: 1000,
          }}
          role="menu"
          aria-orientation="vertical"
          onKeyDown={handleKeyDown}
        >
          <div className="overflow-y-auto">
            {visibleItems.map((item, index) => {
              if (item.separator) {
                return <div key={item.id} className={separatorStyles} role="separator" />;
              }

              const isHighlighted = index === highlightedIndex;
              const hasSubmenu = item.submenu && item.submenu.length > 0;

              const itemStyles = [
                baseItemStyles,
                sizeStyles[size].item,
                item.destructive && destructiveStyles,
                item.disabled && disabledStyles,
                isHighlighted && 'bg-gray-50 dark:bg-gray-700',
                itemClassName,
              ]
                .filter(Boolean)
                .join(' ');

              const ItemContent = () => (
                <>
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    {/* Icon */}
                    {item.icon && (
                      <span className={`${sizeStyles[size].icon} flex-shrink-0`}>
                        {item.icon}
                      </span>
                    )}

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{item.label}</div>
                      {item.description && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                          {item.description}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Right Side */}
                  <div className="flex items-center space-x-2 flex-shrink-0">
                    {/* Badge */}
                    {item.badge && (
                      <span className="flex-shrink-0">
                        {item.badge}
                      </span>
                    )}

                    {/* Shortcut */}
                    {item.shortcut && (
                      <span className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                        {item.shortcut}
                      </span>
                    )}

                    {/* Submenu Indicator */}
                    {hasSubmenu && (
                      <ChevronDown className={`${sizeStyles[size].icon} transform rotate-[-90deg]`} />
                    )}
                  </div>
                </>
              );

              if (item.href) {
                return (
                  <a
                    key={item.id}
                    href={item.href}
                    target={item.target}
                    className={itemStyles}
                    role="menuitem"
                    onClick={() => handleItemSelect(item)}
                  >
                    <ItemContent />
                  </a>
                );
              }

              return (
                <button
                  key={item.id}
                  ref={el => itemRefs.current[index] = el}
                  type="button"
                  className={itemStyles}
                  disabled={item.disabled}
                  onClick={() => handleItemSelect(item)}
                  role="menuitem"
                  aria-haspopup={hasSubmenu}
                  aria-expanded={hasSubmenu && activeSubmenu === item.id}
                >
                  <ItemContent />
                </button>
              );
            })}
          </div>

          {/* Submenu */}
          {activeSubmenu && (
            <div className="absolute left-full top-0 ml-1">
              {/* Render submenu recursively */}
            </div>
          )}
        </div>
      )}
    </div>
  );
});

Dropdown.displayName = 'Dropdown';

/**
 * ContextMenu Component
 * 
 * Specialized dropdown for right-click context menus
 */
interface ContextMenuProps extends Omit<DropdownProps, 'trigger'> {
  preset?: keyof typeof contextMenuPresets;
}

const ContextMenu = forwardRef<HTMLDivElement, ContextMenuProps>(({
  preset,
  items: itemsProp,
  ...props
}, ref) => {
  const items = preset ? contextMenuPresets[preset] : itemsProp || [];

  return (
    <Dropdown
      ref={ref}
      trigger="contextmenu"
      items={items}
      closeOnSelect={true}
      {...props}
    />
  );
});

ContextMenu.displayName = 'ContextMenu';

/**
 * ActionMenu Component
 * 
 * Specialized dropdown for action buttons (More options)
 */
interface ActionMenuProps extends Omit<DropdownProps, 'children'> {
  icon?: React.ReactNode;
  buttonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
}

const ActionMenu = forwardRef<HTMLDivElement, ActionMenuProps>(({
  icon = <MoreVertical className="w-4 h-4" />,
  size = 'sm',
  placement = 'bottom-end',
  buttonProps = {},
  ...props
}, ref) => {
  return (
    <Dropdown
      ref={ref}
      size={size}
      placement={placement}
      trigger="click"
      {...props}
    >
      <button
        type="button"
        className="
          p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100
          dark:hover:text-gray-200 dark:hover:bg-gray-700
          focus:outline-none focus:ring-2 focus:ring-primary-500
          transition-colors duration-200
        "
        aria-label="More options"
        {...buttonProps}
      >
        {icon}
      </button>
    </Dropdown>
  );
});

ActionMenu.displayName = 'ActionMenu';

/**
 * UserMenu Component
 * 
 * Specialized dropdown for user account menu
 */
interface UserMenuProps extends Omit<DropdownProps, 'items' | 'children'> {
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  customItems?: DropdownItem[];
}

const UserMenu = forwardRef<HTMLDivElement, UserMenuProps>(({
  user,
  customItems,
  placement = 'bottom-end',
  variant = 'primary',
  ...props
}, ref) => {
  const items = customItems || contextMenuPresets.user;

  return (
    <Dropdown
      ref={ref}
      placement={placement}
      variant={variant}
      trigger="click"
      items={items}
      {...props}
    >
      <button
        type="button"
        className="
          flex items-center space-x-2 p-2 rounded-md text-left
          hover:bg-gray-100 dark:hover:bg-gray-700
          focus:outline-none focus:ring-2 focus:ring-primary-500
          transition-colors duration-200
        "
        aria-label="User menu"
      >
        {user?.avatar ? (
          <img
            src={user.avatar}
            alt={user.name}
            className="w-8 h-8 rounded-full"
          />
        ) : (
          <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-medium">
              {user?.name?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
        )}
        <div className="hidden sm:block">
          {user?.name && (
            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {user.name}
            </div>
          )}
          {user?.email && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {user.email}
            </div>
          )}
        </div>
        <ChevronDown className="w-4 h-4 text-gray-400" />
      </button>
    </Dropdown>
  );
});

UserMenu.displayName = 'UserMenu';

// Attach sub-components to main Dropdown component
const DropdownWithComponents = Dropdown as typeof Dropdown & {
  Context: typeof ContextMenu;
  Action: typeof ActionMenu;
  User: typeof UserMenu;
};

DropdownWithComponents.Context = ContextMenu;
DropdownWithComponents.Action = ActionMenu;
DropdownWithComponents.User = UserMenu;

export default DropdownWithComponents;
export type { 
  DropdownProps,
  ContextMenuProps,
  ActionMenuProps,
  UserMenuProps,
  DropdownItem,
  DropdownVariant,
  DropdownSize,
  DropdownPlacement,
  DropdownTrigger
};
export { 
  ContextMenu, 
  ActionMenu, 
  UserMenu,
  contextMenuPresets 
};