/**
 * Kaydan Analytic Hub (KAH) UI Types
 * 
 * Shared type definitions used across multiple UI components
 * for the Kaydan Analytic Hub data analytics platform.
 */

import { DETECTION_OBJECT_TYPES, DETECTION_STATUSES } from './Constants';

// =============================================================================
// SIZE VARIANTS
// =============================================================================

export type ComponentSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// =============================================================================
// VARIANT TYPES
// =============================================================================

export type ComponentVariant = 
  | 'default'
  | 'primary' 
  | 'secondary'
  | 'success'
  | 'warning'
  | 'destructive'
  | 'outline'
  | 'ghost'
  | 'link';

// =============================================================================
// ANALYTICS-SPECIFIC TYPES
// =============================================================================

export type AnalyticsType = typeof DETECTION_OBJECT_TYPES[number];

export type AnalyticsStatus = typeof DETECTION_STATUSES[number];

export type DataSourceStatus = 
  | 'online'
  | 'offline'
  | 'connecting'
  | 'error'
  | 'maintenance';

export type DatasetStatus = 
  | 'active'
  | 'inactive'
  | 'processing'
  | 'error';

// =============================================================================
// PROCESS AND OPERATION TYPES
// =============================================================================

export type ProcessStatus = 
  | 'idle'
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export type OperationType = 
  | 'analysis'
  | 'export'
  | 'import'
  | 'processing'
  | 'training'
  | 'configuration'
  | 'backup'
  | 'restore';

// =============================================================================
// THEME TYPES
// =============================================================================

export type ThemeMode = 'light' | 'dark' | 'auto';

export type ColorScheme = 
  | 'blue'
  | 'green' 
  | 'orange'
  | 'red'
  | 'purple'
  | 'gray'
  | 'cyan';

// =============================================================================
// COMMON PROPS INTERFACES
// =============================================================================

export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  'data-testid'?: string;
}

export interface InteractiveProps {
  disabled?: boolean;
  loading?: boolean;
  onClick?: (event: React.MouseEvent) => void;
  onFocus?: (event: React.FocusEvent) => void;
  onBlur?: (event: React.FocusEvent) => void;
}

export interface ValidationProps {
  required?: boolean;
  error?: string;
  valid?: boolean;
  invalid?: boolean;
}

// =============================================================================
// FORM AND DATA TYPES
// =============================================================================

export interface FormFieldProps extends BaseComponentProps, ValidationProps {
  label?: string;
  placeholder?: string;
  helperText?: string;
  name?: string;
  value?: string | number | boolean;
  onChange?: (value: string | number | boolean) => void;
}

export interface SelectOption {
  value: string | number;
  label: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  disabled?: boolean;
  badge?: string;
}

export interface SelectGroup {
  label: string;
  options: SelectOption[];
}

// =============================================================================
// ANALYTICS DATA TYPES
// =============================================================================

export interface AnalyticsData {
  id: string;
  objectType: AnalyticsType;
  confidence: number;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  timestamp: Date;
  dataSourceId?: string;
  datasetId?: string;
  metadata?: Record<string, unknown>;
}

export interface AnalyticsDataset {
  id: string;
  name: string;
  points: Array<{ x: number; y: number }>;
  enabled: boolean;
  objectTypes: AnalyticsType[];
  confidence: number;
  color?: string;
  status: DatasetStatus;
}

export interface AnalyticsMetrics {
  totalAnalytics: number;
  accuracy: number;
  processingSpeed: number; // FPS
  falsePositives: number;
  falseNegatives: number;
  confidence: {
    average: number;
    min: number;
    max: number;
  };
}

// =============================================================================
// DATA SOURCE TYPES
// =============================================================================

export interface DataSource {
  id: string;
  name: string;
  url: string;
  status: DataSourceStatus;
  location?: string;
  resolution?: string;
  fps?: number;
  datasets?: AnalyticsDataset[];
  lastSeen?: Date;
  metadata?: Record<string, unknown>;
}

// =============================================================================
// MODEL AND CLASSIFICATION TYPES
// =============================================================================

export interface ModelClass {
  id: number;
  model: {
    id: number;
    name: string;
    version: string;
  };
  class_id: number;
  class_name: string;
}

export interface ObjectClass {
  id: string;
  name: string;
  displayName: string;
  color: string;
  enabled: boolean;
  confidence: number;
}

export interface AnalyticsModel {
  id: string;
  name: string;
  version: string;
  type: 'object_detection' | 'classification' | 'segmentation';
  status: 'active' | 'inactive' | 'loading' | 'error';
  accuracy: number;
  speed: number; // FPS
  objectClasses: ObjectClass[];
  metadata?: Record<string, unknown>;
}

// =============================================================================
// NOTIFICATION TYPES
// =============================================================================

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  timestamp: Date;
  dismissible?: boolean;
  autoClose?: boolean;
  duration?: number; // milliseconds
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

// =============================================================================
// CHART AND VISUALIZATION TYPES
// =============================================================================

export interface ChartDataPoint {
  timestamp: Date;
  value: number;
  label?: string;
  metadata?: Record<string, unknown>;
}

export interface ChartSeries {
  name: string;
  data: ChartDataPoint[];
  color?: string;
  type?: 'line' | 'bar' | 'area';
}

export interface AnalyticsChartData {
  period: 'hour' | 'day' | 'week' | 'month';
  metrics: {
    analytics: ChartSeries[];
    accuracy: ChartSeries[];
    performance: ChartSeries[];
  };
  summary: {
    totalAnalytics: number;
    averageAccuracy: number;
    averageFPS: number;
    activeTime: number; // minutes
  };
}

// =============================================================================
// MEDIA AND FILE TYPES
// =============================================================================

export interface MediaFile {
  id: string;
  name: string;
  type: 'image' | 'video';
  url: string;
  size: number; // bytes
  duration?: number; // seconds for video
  dimensions?: {
    width: number;
    height: number;
  };
  thumbnail?: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'xml' | 'pdf';
  dateRange: {
    start: Date;
    end: Date;
  };
  includeImages: boolean;
  includeMetadata: boolean;
  compression?: 'none' | 'low' | 'medium' | 'high';
}

// =============================================================================
// SESSION AND HISTORICAL DATA TYPES
// =============================================================================

export interface AnalyticsSession {
  id: string;
  name: string;
  startTime: Date;
  endTime?: Date;
  status: ProcessStatus;
  dataSourceIds: string[];
  analyticsCount: number;
  duration: number; // minutes
  metrics: AnalyticsMetrics;
  recordings?: MediaFile[];
  notes?: string;
}

export interface HistoricalData {
  sessions: AnalyticsSession[];
  totalAnalytics: number;
  timeRange: {
    start: Date;
    end: Date;
  };
  filters: {
    dataSourceIds?: string[];
    objectTypes?: AnalyticsType[];
    confidence?: {
      min: number;
      max: number;
    };
  };
}

// =============================================================================
// SYSTEM CONFIGURATION TYPES
// =============================================================================

export interface SystemConfiguration {
  analytics: {
    defaultModel: string;
    confidence: number;
    maxConcurrentStreams: number;
    processingInterval: number;
  };
  dataSources: {
    autoDiscovery: boolean;
    connectionTimeout: number;
    retryAttempts: number;
  };
  storage: {
    retention: number; // days
    compression: boolean;
    location: string;
  };
  alerts: {
    enabled: boolean;
    channels: string[];
    cooldownPeriod: number; // minutes
  };
}

// =============================================================================
// API AND RESPONSE TYPES
// =============================================================================

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
  metadata?: {
    timestamp: Date;
    requestId: string;
    pagination?: PaginationInfo;
  };
}

export interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface PaginatedResponse<T> {
  results: T[];
  count: number;
  next?: string;
  previous?: string;
}

// =============================================================================
// SYSTEM EVENT TYPES
// =============================================================================

export interface SystemEvent {
  id: string;
  type: 'analytics' | 'dataSource' | 'system' | 'user';
  subtype?: string;
  timestamp: Date;
  source: string;
  data: Record<string, unknown>;
  severity: 'low' | 'medium' | 'high' | 'critical';
  acknowledged?: boolean;
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  category?: string;
}

export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  disabled?: boolean;
  separator?: boolean;
  shortcut?: string;
  children?: ContextMenuItem[];
}

// =============================================================================
// UTILITY TYPE HELPERS
// =============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type ValueOf<T> = T[keyof T];

export type NonEmptyArray<T> = [T, ...T[]];

export type StringOrNumber = string | number;

export type Callback<T = void> = (...args: unknown[]) => T;

export type AsyncCallback<T = void> = (...args: unknown[]) => Promise<T>;