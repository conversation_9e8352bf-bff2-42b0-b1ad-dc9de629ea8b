import React, { forwardRef, useId } from 'react';
import { 
  Check, 
  X, 
  Eye, 
  EyeOff, 
  Camera, 
  CameraOff, 
  Volume2, 
  VolumeX, 
  Wifi, 
  WifiOff,
  Play,
  Pause,
  Shield,
  ShieldOff,
  Zap,
  ZapOff,
  Moon,
  Sun,
  Bell,
  BellOff
} from 'lucide-react';

// Toggle variant types
type ToggleVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'ghost';

type ToggleSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type ToggleType = 
  | 'default'
  | 'checkbox'
  | 'visibility'
  | 'camera'
  | 'audio'
  | 'network'
  | 'recording'
  | 'privacy'
  | 'detection'
  | 'theme'
  | 'notifications';

// Icon mapping for different toggle types
const typeIcons = {
  visibility: { on: Eye, off: EyeOff },
  camera: { on: Camera, off: CameraOff },
  audio: { on: Volume2, off: VolumeX },
  network: { on: Wifi, off: WifiOff },
  recording: { on: Play, off: Pause },
  privacy: { on: Shield, off: ShieldOff },
  detection: { on: Zap, off: ZapOff },
  theme: { on: Moon, off: Sun },
  notifications: { on: Bell, off: BellOff },
  checkbox: { on: Check, off: X },
} as const;

// Base toggle props
interface BaseToggleProps {
  variant?: ToggleVariant;
  size?: ToggleSize;
  type?: ToggleType;
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  info?: string;
  disabled?: boolean;
  loading?: boolean;
  checked?: boolean;
  defaultChecked?: boolean;
  showIcons?: boolean;
  showLabels?: boolean;
  onLabel?: string;
  offLabel?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
  toggleClassName?: string;
  labelClassName?: string;
  trackClassName?: string;
  thumbClassName?: string;
  onChange?: (checked: boolean) => void;
}

type ToggleProps = BaseToggleProps & 
  Omit<React.InputHTMLAttributes<HTMLInputElement>, keyof BaseToggleProps | 'type' | 'size'>;

// Variant styles for track (background)
const trackVariantStyles: Record<ToggleVariant, string> = {
  default: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-gray-400 dark:peer-checked:bg-gray-500
    peer-focus:ring-gray-300 dark:peer-focus:ring-gray-600
  `,
  primary: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-primary-500 dark:peer-checked:bg-primary-600
    peer-focus:ring-primary-300 dark:peer-focus:ring-primary-600
  `,
  secondary: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-gray-500 dark:peer-checked:bg-gray-400
    peer-focus:ring-gray-300 dark:peer-focus:ring-gray-500
  `,
  success: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-green-500 dark:peer-checked:bg-green-600
    peer-focus:ring-green-300 dark:peer-focus:ring-green-600
  `,
  warning: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-yellow-500 dark:peer-checked:bg-yellow-600
    peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-600
  `,
  error: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-red-500 dark:peer-checked:bg-red-600
    peer-focus:ring-red-300 dark:peer-focus:ring-red-600
  `,
  detection: `
    bg-gray-200 dark:bg-gray-700
    peer-checked:bg-cyan-500 dark:peer-checked:bg-cyan-600
    peer-focus:ring-cyan-300 dark:peer-focus:ring-cyan-600
  `,
  ghost: `
    bg-transparent border border-gray-300 dark:border-gray-600
    peer-checked:bg-gray-100 peer-checked:border-gray-400
    dark:peer-checked:bg-gray-800 dark:peer-checked:border-gray-500
    peer-focus:ring-gray-300 dark:peer-focus:ring-gray-600
  `,
};

// Size styles for track and thumb
const sizeStyles = {
  track: {
    xs: 'w-7 h-4',
    sm: 'w-9 h-5',
    md: 'w-11 h-6',
    lg: 'w-14 h-7',
    xl: 'w-16 h-8',
  },
  thumb: {
    xs: 'w-3 h-3 after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-2 after:w-2 after:transition-all peer-checked:after:translate-x-3',
    sm: 'w-4 h-4 after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:after:translate-x-4',
    md: 'w-5 h-5 after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:after:translate-x-5',
    lg: 'w-6 h-6 after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:after:translate-x-7',
    xl: 'w-7 h-7 after:absolute after:top-0.5 after:left-[2px] after:bg-white after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:after:translate-x-8',
  },
  icon: {
    xs: 'w-2 h-2',
    sm: 'w-2.5 h-2.5',
    md: 'w-3 h-3',
    lg: 'w-3.5 h-3.5',
    xl: 'w-4 h-4',
  },
  label: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
  },
} as const;

// Base toggle styles
const baseTrackStyles = `
  peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-offset-2
  dark:peer-focus:ring-offset-gray-800
  rounded-full transition-all duration-200 ease-in-out
  relative inline-flex items-center cursor-pointer
  peer-disabled:opacity-50 peer-disabled:cursor-not-allowed
`;

const baseThumbStyles = `
  bg-white border-gray-300 rounded-full transition-all duration-200 ease-in-out
  peer-checked:bg-white peer-disabled:bg-gray-100
  dark:border-gray-600 dark:bg-gray-300 dark:peer-checked:bg-white
`;

// Loading styles
const loadingStyles = `
  relative overflow-hidden
  before:absolute before:inset-0 before:bg-gradient-to-r 
  before:from-transparent before:via-white/20 before:to-transparent
  before:translate-x-[-100%] before:animate-pulse
  dark:before:via-gray-700/20
`;

// High contrast mode styles
const highContrastStyles = `
  contrast-more:border-2 contrast-more:border-current
`;

/**
 * Toggle Component
 * 
 * A versatile toggle switch component for the VisionGuard Detection Analytics platform.
 * Supports multiple variants, sizes, types, and states for different functionality.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Specialized types for different functionality (camera, audio, etc.)
 * - Auto-icons based on toggle type
 * - Custom on/off labels and icons
 * - Loading states with animation
 * - Validation states with visual feedback
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Basic detection toggle
 * <Toggle 
 *   label="Enable Detection"
 *   variant="detection"
 *   checked={detectionEnabled}
 *   onChange={setDetectionEnabled}
 * />
 * 
 * // Camera toggle with auto-icons
 * <Toggle 
 *   type="camera"
 *   label="Camera Stream"
 *   showIcons
 *   checked={cameraActive}
 *   onChange={toggleCamera}
 * />
 * 
 * // Privacy mode with labels
 * <Toggle 
 *   type="privacy"
 *   label="Privacy Mode"
 *   showLabels
 *   onLabel="Protected"
 *   offLabel="Standard"
 *   variant="warning"
 * />
 * 
 * // Recording toggle with validation
 * <Toggle 
 *   type="recording"
 *   label="Auto Recording"
 *   description="Automatically record when motion is detected"
 *   success="Recording configured successfully"
 *   checked={autoRecord}
 *   onChange={setAutoRecord}
 * />
 * ```
 */
const Toggle = forwardRef<HTMLInputElement, ToggleProps>(({
  variant = 'default',
  size = 'md',
  type = 'default',
  label,
  description,
  error,
  success,
  warning,
  info,
  disabled = false,
  loading = false,
  checked,
  defaultChecked,
  showIcons = false,
  showLabels = false,
  onLabel = 'On',
  offLabel = 'Off',
  leftIcon,
  rightIcon,
  className = '',
  toggleClassName = '',
  labelClassName = '',
  trackClassName = '',
  thumbClassName = '',
  onChange,
  ...props
}, ref) => {
  const toggleId = useId();

  // Determine variant based on validation state
  const getVariant = () => {
    if (error) return 'error';
    if (success) return 'success';
    if (warning) return 'warning';
    return variant;
  };

  const currentVariant = getVariant();

  // Get auto-icons based on type
  const getAutoIcons = () => {
    if (!showIcons || !typeIcons[type as keyof typeof typeIcons]) return null;
    const icons = typeIcons[type as keyof typeof typeIcons];
    return icons;
  };

  const autoIcons = getAutoIcons();

  // Handle change event
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!disabled && !loading) {
      onChange?.(e.target.checked);
    }
  };

  // Combine track styles
  const trackStyles = [
    baseTrackStyles,
    trackVariantStyles[currentVariant],
    sizeStyles.track[size],
    highContrastStyles,
    loading && loadingStyles,
    trackClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Combine thumb styles
  const thumbStyles = [
    baseThumbStyles,
    sizeStyles.thumb[size],
    thumbClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Status message
  const statusMessage = error || success || warning || info;
  const statusColor = error ? 'text-red-600 dark:text-red-400' :
                     success ? 'text-green-600 dark:text-green-400' :
                     warning ? 'text-yellow-600 dark:text-yellow-400' :
                     'text-blue-600 dark:text-blue-400';

  // Current state for display
  const isChecked = checked ?? defaultChecked ?? false;

  return (
    <div className={`flex items-start space-x-3 ${className}`}>
      {/* Left Icon */}
      {leftIcon && (
        <div className={`${sizeStyles.icon[size]} mt-1 text-gray-500 dark:text-gray-400 flex-shrink-0`}>
          {leftIcon}
        </div>
      )}

      {/* Toggle Switch */}
      <div className="flex items-center space-x-3">
        <label
          htmlFor={toggleId}
          className={`relative inline-flex items-center cursor-pointer ${toggleClassName}`}
        >
          {/* Hidden Checkbox */}
          <input
            ref={ref}
            id={toggleId}
            type="checkbox"
            checked={checked}
            defaultChecked={defaultChecked}
            disabled={disabled || loading}
            onChange={handleChange}
            className="sr-only peer"
            aria-describedby={statusMessage ? `${toggleId}-message` : undefined}
            {...props}
          />

          {/* Track */}
          <div className={trackStyles}>
            {/* Track Icons */}
            {autoIcons && (
              <>
                {/* Off Icon */}
                <div className={`absolute left-1 top-1/2 transform -translate-y-1/2 ${sizeStyles.icon[size]} text-gray-500 peer-checked:opacity-0 transition-opacity duration-200`}>
                  <autoIcons.off />
                </div>
                
                {/* On Icon */}
                <div className={`absolute right-1 top-1/2 transform -translate-y-1/2 ${sizeStyles.icon[size]} text-white opacity-0 peer-checked:opacity-100 transition-opacity duration-200`}>
                  <autoIcons.on />
                </div>
              </>
            )}

            {/* Thumb */}
            <div className={thumbStyles}>
              {/* Loading Spinner */}
              {loading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`${sizeStyles.icon[size]} animate-spin border-2 border-gray-400 border-t-transparent rounded-full`} />
                </div>
              )}
            </div>
          </div>

          {/* Labels */}
          {showLabels && (
            <div className={`ml-3 ${sizeStyles.label[size]} font-medium`}>
              <span className={`${isChecked ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'} transition-colors duration-200`}>
                {isChecked ? onLabel : offLabel}
              </span>
            </div>
          )}
        </label>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {/* Label */}
        {label && (
          <label
            htmlFor={toggleId}
            className={`
              block font-medium text-gray-700 dark:text-gray-300 cursor-pointer
              ${sizeStyles.label[size]}
              ${labelClassName}
            `}
          >
            {label}
          </label>
        )}

        {/* Description */}
        {description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {description}
          </p>
        )}

        {/* Status Message */}
        {statusMessage && (
          <p
            id={`${toggleId}-message`}
            className={`mt-1 text-sm ${statusColor}`}
          >
            {statusMessage}
          </p>
        )}
      </div>

      {/* Right Icon */}
      {rightIcon && (
        <div className={`${sizeStyles.icon[size]} mt-1 text-gray-500 dark:text-gray-400 flex-shrink-0`}>
          {rightIcon}
        </div>
      )}
    </div>
  );
});

Toggle.displayName = 'Toggle';

/**
 * ToggleGroup Component
 * 
 * Groups multiple related toggles with consistent styling
 */
interface ToggleGroupProps {
  children: React.ReactNode;
  label?: string;
  description?: string;
  className?: string;
  spacing?: 'tight' | 'normal' | 'loose';
  orientation?: 'vertical' | 'horizontal';
}

const ToggleGroup = forwardRef<HTMLDivElement, ToggleGroupProps>(({
  children,
  label,
  description,
  className = '',
  spacing = 'normal',
  orientation = 'vertical',
  ...props
}, ref) => {
  const spacingStyles = {
    tight: orientation === 'vertical' ? 'space-y-2' : 'space-x-4',
    normal: orientation === 'vertical' ? 'space-y-4' : 'space-x-6',
    loose: orientation === 'vertical' ? 'space-y-6' : 'space-x-8',
  };

  const orientationStyles = {
    vertical: 'flex flex-col',
    horizontal: 'flex flex-wrap items-center',
  };

  return (
    <div ref={ref} className={className} {...props}>
      {label && (
        <div className="mb-4">
          <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
            {label}
          </h3>
          {description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {description}
            </p>
          )}
        </div>
      )}
      <div className={`${orientationStyles[orientation]} ${spacingStyles[spacing]}`}>
        {children}
      </div>
    </div>
  );
});

ToggleGroup.displayName = 'ToggleGroup';

/**
 * DetectionToggle Component
 * 
 * Specialized toggle for detection features
 */
interface DetectionToggleProps extends Omit<ToggleProps, 'variant' | 'type'> {
  detectionType?: 'person' | 'vehicle' | 'object' | 'general';
}

const DetectionToggle = forwardRef<HTMLInputElement, DetectionToggleProps>(({
  detectionType = 'general',
  showIcons = true,
  variant = 'detection',
  size = 'md',
  ...props
}, ref) => {
  return (
    <Toggle
      ref={ref}
      variant={variant}
      type="detection"
      size={size}
      showIcons={showIcons}
      {...props}
    />
  );
});

DetectionToggle.displayName = 'DetectionToggle';

/**
 * CameraToggle Component
 * 
 * Specialized toggle for camera controls
 */
interface CameraToggleProps extends Omit<ToggleProps, 'type'> {
  cameraAction?: 'stream' | 'recording' | 'audio' | 'privacy';
}

const CameraToggle = forwardRef<HTMLInputElement, CameraToggleProps>(({
  cameraAction = 'stream',
  showIcons = true,
  size = 'md',
  ...props
}, ref) => {
  const typeMap = {
    stream: 'camera' as const,
    recording: 'recording' as const,
    audio: 'audio' as const,
    privacy: 'privacy' as const,
  };

  return (
    <Toggle
      ref={ref}
      type={typeMap[cameraAction]}
      size={size}
      showIcons={showIcons}
      {...props}
    />
  );
});

CameraToggle.displayName = 'CameraToggle';

/**
 * SystemToggle Component
 * 
 * Specialized toggle for system settings
 */
interface SystemToggleProps extends Omit<ToggleProps, 'type'> {
  systemFeature?: 'network' | 'notifications' | 'theme';
}

const SystemToggle = forwardRef<HTMLInputElement, SystemToggleProps>(({
  systemFeature = 'network',
  showIcons = true,
  size = 'md',
  ...props
}, ref) => {
  const typeMap = {
    network: 'network' as const,
    notifications: 'notifications' as const,
    theme: 'theme' as const,
  };

  return (
    <Toggle
      ref={ref}
      type={typeMap[systemFeature]}
      size={size}
      showIcons={showIcons}
      {...props}
    />
  );
});

SystemToggle.displayName = 'SystemToggle';

// Attach sub-components to main Toggle component
const ToggleWithComponents = Toggle as typeof Toggle & {
  Group: typeof ToggleGroup;
  Detection: typeof DetectionToggle;
  Camera: typeof CameraToggle;
  System: typeof SystemToggle;
};

ToggleWithComponents.Group = ToggleGroup;
ToggleWithComponents.Detection = DetectionToggle;
ToggleWithComponents.Camera = CameraToggle;
ToggleWithComponents.System = SystemToggle;

export default ToggleWithComponents;
export type { 
  ToggleProps,
  ToggleGroupProps,
  DetectionToggleProps,
  CameraToggleProps,
  SystemToggleProps,
  ToggleVariant,
  ToggleSize,
  ToggleType
};
export { ToggleGroup, DetectionToggle, CameraToggle, SystemToggle };