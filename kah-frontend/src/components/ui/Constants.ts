/**
 * Kaydan Analytic Hub (KAH) UI Constants
 * 
 * Shared constants and configuration values used across
 * the Kaydan Analytic Hub data analytics UI components.
 */

// =============================================================================
// COLOR PALETTES (Based on your tailwind.config.js)
// =============================================================================

export const ANALYTICS_COLORS = {
    person: '#10b981',      // analytics.person from your config
    vehicle: '#f59e0b',     // analytics.vehicle from your config
    object: '#8b5cf6',      // analytics.object from your config
    zone: '#06b6d4',        // analytics.zone from your config
    weapon: '#ef4444',      // error color
    animal: '#84cc16',      // lime-500
    bicycle: '#06b6d4',     // same as zone
    motorcycle: '#f97316',  // orange-500
    bus: '#eab308',         // yellow-500
    truck: '#dc2626',       // red-600
    suspicious: '#dc2626',  // red-600
  } as const;
  
  export const THEME_COLORS = {
    primary: {
      50: '#fff7ed',
      100: '#ffedd5',
      200: '#fed7aa',
      300: '#fdba74',
      400: '#fb923c',
      500: '#f97316',
      600: '#ea580c',
      700: '#c2410c',
      800: '#9a3412',
      900: '#7c2d12',
      950: '#431407',
    },
    dark: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    },
    status: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#f97316',
    }
  } as const;
  
  // =============================================================================
  // SIZE DEFINITIONS
  // =============================================================================
  
  export const COMPONENT_SIZES = {
    xs: {
      padding: 'px-2 py-1',
      text: 'text-xs',
      height: 'h-6',
      icon: 'w-3 h-3',
    },
    sm: {
      padding: 'px-3 py-1.5',
      text: 'text-sm',
      height: 'h-8',
      icon: 'w-4 h-4',
    },
    md: {
      padding: 'px-4 py-2',
      text: 'text-sm',
      height: 'h-10',
      icon: 'w-4 h-4',
    },
    lg: {
      padding: 'px-5 py-2.5',
      text: 'text-base',
      height: 'h-12',
      icon: 'w-5 h-5',
    },
    xl: {
      padding: 'px-6 py-3',
      text: 'text-lg',
      height: 'h-14',
      icon: 'w-6 h-6',
    },
  } as const;
  
  // =============================================================================
  // ANIMATION DURATIONS
  // =============================================================================
  
  export const DEFAULT_ANIMATION_DURATION = 200; // milliseconds
  
  export const ANIMATION_DURATIONS = {
    fast: 150,
    normal: 200,
    slow: 300,
    'fade-in': 500,
    'slide-up': 300,
    'detection-pulse': 1500,
  } as const;
  
  // =============================================================================
  // DEBOUNCE DELAYS
  // =============================================================================
  
  export const DEFAULT_DEBOUNCE_DELAY = 300; // milliseconds
  
  export const DEBOUNCE_DELAYS = {
    search: 300,
    typing: 500,
    api: 1000,
    resize: 100,
  } as const;
  
  // =============================================================================
  // KEYBOARD SHORTCUTS
  // =============================================================================
  
  export const KEYBOARD_SHORTCUTS = {
    // Global shortcuts
    search: 'cmd+k',
    save: 'cmd+s',
    export: 'cmd+e',
    import: 'cmd+i',
    
    // Navigation shortcuts
    home: 'cmd+h',
    configuration: 'cmd+comma',
    detection: 'cmd+d',
    analytics: 'cmd+a',
    
    // Detection shortcuts
    startDetection: 'space',
    stopDetection: 'esc',
    pauseDetection: 'p',
    resetDetection: 'r',
    
    // Modal shortcuts
    closeModal: 'esc',
    confirmAction: 'enter',
    cancelAction: 'esc',
    
    // Form shortcuts
    submit: 'cmd+enter',
    reset: 'cmd+r',
    clear: 'cmd+delete',
  } as const;
  
  // =============================================================================
  // DETECTION SPECIFIC CONSTANTS
  // =============================================================================
  
  export const DETECTION_OBJECT_TYPES = [
    'person',
    'vehicle',
    'bicycle',
    'motorcycle',
    'bus', 
    'truck',
    'animal',
    'object',
    'weapon',
    'suspicious_object'
  ] as const;
  
  export const DETECTION_STATUSES = [
    'idle',
    'loading',
    'active',
    'paused',
    'stopped',
    'error'
  ] as const;
  
  export const CONFIDENCE_LEVELS = {
    low: 0.3,
    medium: 0.5,
    high: 0.7,
    veryHigh: 0.9
  } as const;
  
  // =============================================================================
  // COMPONENT VARIANTS
  // =============================================================================
  
  export const BUTTON_VARIANTS = [
    'default',
    'primary',
    'secondary',
    'destructive',
    'outline',
    'ghost',
    'link',
    'detection',
    'detection-person',
    'detection-vehicle',
    'detection-object',
    'detection-zone'
  ] as const;
  
  export const BADGE_VARIANTS = [
    'default',
    'secondary',
    'success',
    'warning',
    'destructive',
    'outline',
    'detection-person',
    'detection-vehicle',
    'detection-object',
    'detection-zone'
  ] as const;
  
  export const ALERT_VARIANTS = [
    'default',
    'info',
    'success',
    'warning',
    'destructive'
  ] as const;
  
  // =============================================================================
  // RESPONSIVE BREAKPOINTS (Based on Tailwind defaults)
  // =============================================================================
  
  export const BREAKPOINTS = {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  } as const;
  
  // =============================================================================
  // Z-INDEX LAYERS
  // =============================================================================
  
  export const Z_INDEX = {
    base: 1,
    elevated: 10,
    overlay: 50,
    modal: 100,
    tooltip: 200,
    notification: 300,
    popover: 400,
    dropdown: 500,
    maximum: 9999,
  } as const;
  
  // =============================================================================
  // FILE SIZE LIMITS
  // =============================================================================
  
  export const FILE_SIZE_LIMITS = {
    avatar: 1024 * 1024 * 2,      // 2MB
    image: 1024 * 1024 * 10,      // 10MB
    video: 1024 * 1024 * 100,     // 100MB
    config: 1024 * 1024 * 5,      // 5MB
    log: 1024 * 1024 * 50,        // 50MB
  } as const;
  
  // =============================================================================
  // API TIMEOUTS
  // =============================================================================
  
  export const API_TIMEOUTS = {
    short: 5000,      // 5 seconds
    medium: 15000,    // 15 seconds
    long: 30000,      // 30 seconds
    upload: 120000,   // 2 minutes
  } as const;
  
  // =============================================================================
  // VALIDATION PATTERNS
  // =============================================================================
  
  export const VALIDATION_PATTERNS = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    url: /^https?:\/\/.+/,
    ipv4: /^(\d{1,3}\.){3}\d{1,3}$/,
    port: /^([1-9]|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
    confidence: /^(0(\.\d+)?|1(\.0+)?)$/,
    percentage: /^(100|[1-9]?\d)$/,
  } as const;
  
  // =============================================================================
  // DEFAULT VALUES
  // =============================================================================
  
  export const DEFAULT_VALUES = {
    // Detection defaults
    confidence: 0.75,
    maxDetections: 100,
    processingInterval: 100,
    
    // UI defaults
    animationDuration: DEFAULT_ANIMATION_DURATION,
    debounceDelay: DEFAULT_DEBOUNCE_DELAY,
    
    // Pagination defaults
    pageSize: 20,
    maxPageSize: 100,
    
    // Chart defaults
    chartHeight: 300,
    chartWidth: 600,
    
    // Component defaults
    inputPlaceholder: 'Enter value...',
    selectPlaceholder: 'Select option...',
    searchPlaceholder: 'Search...',
  } as const;
  
  // =============================================================================
  // ERROR MESSAGES
  // =============================================================================
  
  export const ERROR_MESSAGES = {
    required: 'This field is required',
    invalid: 'Please enter a valid value',
    tooShort: 'Value is too short',
    tooLong: 'Value is too long',
    invalidEmail: 'Please enter a valid email address',
    invalidUrl: 'Please enter a valid URL',
    invalidConfidence: 'Confidence must be between 0 and 1',
    invalidPercentage: 'Percentage must be between 0 and 100',
    fileTooBig: 'File size exceeds limit',
    unsupportedFormat: 'Unsupported file format',
    networkError: 'Network error occurred',
    serverError: 'Server error occurred',
    unauthorized: 'You are not authorized to perform this action',
    notFound: 'Resource not found',
  } as const;