import React, { forwardRef, useState, useEffect, useRef } from 'react';
import { 
  Check<PERSON>ircle,
  AlertCircle,
  Clock,
  Play,
  Pause,
  Square,
  Download,
  Upload,
  Eye,
  Camera,
  Settings,
  Zap,
  RefreshCw,
  Activity,
  TrendingUp,
  BarChart3,
  FileText,
  Video,
  Image,
  Database,
  Cpu,
  HardDrive,
  Wifi
} from 'lucide-react';

// Progress variant types
type ProgressVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'gradient';

type ProgressSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type ProgressType = 
  | 'linear'
  | 'circular'
  | 'semi-circular'
  | 'stepped'
  | 'radial';

type ProgressStatus = 
  | 'idle'
  | 'active'
  | 'paused'
  | 'completed'
  | 'error'
  | 'cancelled';

// Process types for VisionGuard operations
type ProcessType = 
  | 'detection'
  | 'export'
  | 'import'
  | 'processing'
  | 'upload'
  | 'download'
  | 'backup'
  | 'analysis'
  | 'training'
  | 'encoding'
  | 'streaming'
  | 'recording';

// Step interface for stepped progress
interface ProgressStep {
  id: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  status: 'pending' | 'active' | 'completed' | 'error' | 'skipped';
  duration?: number;
  startTime?: Date;
  endTime?: Date;
}

// Process details for different VisionGuard operations
interface ProcessDetails {
  type: ProcessType;
  name: string;
  description?: string;
  totalItems?: number;
  processedItems?: number;
  estimatedTimeRemaining?: number;
  speed?: string;
  details?: string;
}

// Icon mapping for different process types
const processIcons = {
  detection: Eye,
  export: Download,
  import: Upload,
  processing: Cpu,
  upload: Upload,
  download: Download,
  backup: HardDrive,
  analysis: BarChart3,
  training: Zap,
  encoding: Video,
  streaming: Wifi,
  recording: Camera,
} as const;

// Base progress props
interface BaseProgressProps {
  variant?: ProgressVariant;
  size?: ProgressSize;
  type?: ProgressType;
  value?: number;
  max?: number;
  status?: ProgressStatus;
  label?: string;
  description?: string;
  showValue?: boolean;
  showPercentage?: boolean;
  animated?: boolean;
  striped?: boolean;
  indeterminate?: boolean;
  color?: string;
  backgroundColor?: string;
  className?: string;
  barClassName?: string;
  labelClassName?: string;
}

type ProgressProps = BaseProgressProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseProgressProps>;

// Variant styles
const variantStyles: Record<ProgressVariant, string> = {
  default: 'bg-gray-200 dark:bg-gray-700',
  primary: 'bg-primary-100 dark:bg-primary-900',
  secondary: 'bg-gray-100 dark:bg-gray-800',
  success: 'bg-green-100 dark:bg-green-900',
  warning: 'bg-yellow-100 dark:bg-yellow-900',
  error: 'bg-red-100 dark:bg-red-900',
  detection: 'bg-cyan-100 dark:bg-cyan-900',
  gradient: 'bg-gradient-to-r from-primary-100 to-cyan-100 dark:from-primary-900 dark:to-cyan-900',
};

// Bar variant styles
const barVariantStyles: Record<ProgressVariant, string> = {
  default: 'bg-gray-500 dark:bg-gray-400',
  primary: 'bg-primary-500 dark:bg-primary-400',
  secondary: 'bg-gray-600 dark:bg-gray-300',
  success: 'bg-green-500 dark:bg-green-400',
  warning: 'bg-yellow-500 dark:bg-yellow-400',
  error: 'bg-red-500 dark:bg-red-400',
  detection: 'bg-cyan-500 dark:bg-cyan-400',
  gradient: 'bg-gradient-to-r from-primary-500 to-cyan-500 dark:from-primary-400 dark:to-cyan-400',
};

// Size styles
const sizeStyles: Record<ProgressSize, { height: string; text: string; icon: string }> = {
  xs: { height: 'h-1', text: 'text-xs', icon: 'w-3 h-3' },
  sm: { height: 'h-2', text: 'text-sm', icon: 'w-4 h-4' },
  md: { height: 'h-3', text: 'text-sm', icon: 'w-5 h-5' },
  lg: { height: 'h-4', text: 'text-base', icon: 'w-6 h-6' },
  xl: { height: 'h-6', text: 'text-lg', icon: 'w-8 h-8' },
};

// Base progress styles
const baseProgressStyles = `
  relative overflow-hidden rounded-full
  transition-all duration-300 ease-in-out
`;

// Animated styles
const animatedStyles = `
  before:absolute before:inset-0
  before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent
  before:translate-x-[-100%] before:animate-[shimmer_2s_infinite]
`;

// Striped styles
const stripedStyles = `
  bg-gradient-to-r from-transparent via-black/10 to-transparent
  bg-[length:20px_20px]
  animate-[stripes_1s_linear_infinite]
`;

/**
 * Progress Component
 * 
 * A versatile progress indicator component for the VisionGuard Detection Analytics platform.
 * Supports various progress types, process tracking, and real-time updates.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Different progress types (linear, circular, stepped)
 * - Process-specific progress tracking
 * - Status indicators and animations
 * - Real-time progress updates
 * - Estimated time remaining
 * - Process speed indicators
 * - Stepped progress for multi-stage operations
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Basic progress bar
 * <Progress 
 *   value={65} 
 *   variant="primary" 
 *   showPercentage 
 * />
 * 
 * // Detection processing progress
 * <Progress 
 *   value={progress}
 *   variant="detection"
 *   label="Processing video stream..."
 *   description="Analyzing frames for object detection"
 *   animated
 * />
 * 
 * // Export progress with details
 * <Progress.Process
 *   type="export"
 *   value={exportProgress}
 *   processDetails={{
 *     type: 'export',
 *     name: 'Analytics Export',
 *     totalItems: 1000,
 *     processedItems: 650,
 *     speed: '12.5 MB/s'
 *   }}
 * />
 * 
 * // Stepped progress for setup wizard
 * <Progress.Stepped
 *   steps={setupSteps}
 *   currentStep={2}
 *   variant="detection"
 * />
 * ```
 */
const Progress = forwardRef<HTMLDivElement, ProgressProps>(({
  variant = 'default',
  size = 'md',
  type = 'linear',
  value = 0,
  max = 100,
  status = 'active',
  label,
  description,
  showValue = false,
  showPercentage = false,
  animated = false,
  striped = false,
  indeterminate = false,
  color,
  backgroundColor,
  className = '',
  barClassName = '',
  labelClassName = '',
  ...props
}, ref) => {
  const progressRef = useRef<HTMLDivElement>(null);
  
  // Calculate percentage
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));
  
  // Determine if progress is complete
  const isComplete = status === 'completed' || percentage >= 100;
  const hasError = status === 'error';
  const isPaused = status === 'paused';

  // Get status icon
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle className={`${sizeStyles[size].icon} text-green-500`} />;
      case 'error':
        return <AlertCircle className={`${sizeStyles[size].icon} text-red-500`} />;
      case 'paused':
        return <Pause className={`${sizeStyles[size].icon} text-yellow-500`} />;
      case 'active':
        return <Play className={`${sizeStyles[size].icon} text-primary-500`} />;
      default:
        return null;
    }
  };

  // Combine styles
  const progressStyles = [
    baseProgressStyles,
    sizeStyles[size].height,
    backgroundColor || variantStyles[variant],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  const barStyles = [
    'h-full transition-all duration-500 ease-out',
    color || barVariantStyles[variant],
    animated && animatedStyles,
    striped && stripedStyles,
    barClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Render different progress types
  const renderLinearProgress = () => (
    <div className={progressStyles} {...props}>
      <div
        className={barStyles}
        style={{
          width: indeterminate ? '30%' : `${percentage}%`,
          animation: indeterminate ? 'progress-indeterminate 2s infinite linear' : undefined,
        }}
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-valuetext={showPercentage ? `${Math.round(percentage)}%` : undefined}
      />
    </div>
  );

  const renderCircularProgress = () => {
    const radius = 45;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;
    
    return (
      <div className={`relative inline-flex items-center justify-center ${className}`}>
        <svg
          className="transform -rotate-90"
          width="100"
          height="100"
          viewBox="0 0 100 100"
        >
          {/* Background circle */}
          <circle
            cx="50"
            cy="50"
            r={radius}
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            className="text-gray-200 dark:text-gray-700"
          />
          {/* Progress circle */}
          <circle
            cx="50"
            cy="50"
            r={radius}
            stroke="currentColor"
            strokeWidth="8"
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className={barVariantStyles[variant].replace('bg-', 'text-')}
            style={{
              transition: 'stroke-dashoffset 0.5s ease-in-out',
            }}
          />
        </svg>
        {/* Center content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={`font-medium ${sizeStyles[size].text}`}>
            {Math.round(percentage)}%
          </span>
        </div>
      </div>
    );
  };

  return (
    <div ref={ref} className="w-full">
      {/* Header */}
      {(label || description || showValue || showPercentage) && (
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <div className="min-w-0 flex-1">
              {label && (
                <div className={`font-medium ${sizeStyles[size].text} ${labelClassName}`}>
                  {label}
                </div>
              )}
              {description && (
                <div className={`text-gray-600 dark:text-gray-400 ${sizeStyles[size].text} text-opacity-75`}>
                  {description}
                </div>
              )}
            </div>
          </div>
          
          {(showValue || showPercentage) && (
            <div className={`font-medium ${sizeStyles[size].text} text-gray-700 dark:text-gray-300`}>
              {showPercentage ? `${Math.round(percentage)}%` : `${value}/${max}`}
            </div>
          )}
        </div>
      )}

      {/* Progress Indicator */}
      {type === 'circular' ? renderCircularProgress() : renderLinearProgress()}
    </div>
  );
});

Progress.displayName = 'Progress';

/**
 * ProcessProgress Component
 * 
 * Specialized progress for VisionGuard process tracking
 */
interface ProcessProgressProps extends Omit<ProgressProps, 'type'> {
  processDetails: ProcessDetails;
  showSpeed?: boolean;
  showTimeRemaining?: boolean;
  showItemCount?: boolean;
  onCancel?: () => void;
  onPause?: () => void;
  onResume?: () => void;
}

const ProcessProgress = forwardRef<HTMLDivElement, ProcessProgressProps>(({
  processDetails,
  showSpeed = true,
  showTimeRemaining = true,
  showItemCount = true,
  onCancel,
  onPause,
  onResume,
  status = 'active',
  variant = 'primary',
  ...props
}, ref) => {
  const ProcessIcon = processIcons[processDetails.type] || Activity;
  
  // Format time remaining
  const formatTimeRemaining = (seconds: number) => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  };

  // Calculate progress percentage
  const percentage = processDetails.totalItems && processDetails.processedItems
    ? (processDetails.processedItems / processDetails.totalItems) * 100
    : props.value || 0;

  return (
    <div ref={ref} className="space-y-3">
      {/* Process Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <ProcessIcon className="w-5 h-5 text-primary-500" />
          <div>
            <h4 className="font-medium text-gray-900 dark:text-gray-100">
              {processDetails.name}
            </h4>
            {processDetails.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {processDetails.description}
              </p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {status === 'active' && onPause && (
            <button
              onClick={onPause}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="Pause process"
            >
              <Pause className="w-4 h-4" />
            </button>
          )}
          
          {status === 'paused' && onResume && (
            <button
              onClick={onResume}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              aria-label="Resume process"
            >
              <Play className="w-4 h-4" />
            </button>
          )}
          
          {onCancel && status !== 'completed' && (
            <button
              onClick={onCancel}
              className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
              aria-label="Cancel process"
            >
              <Square className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <Progress
        value={percentage}
        variant={variant}
        status={status}
        animated={status === 'active'}
        showPercentage
        {...props}
      />

      {/* Process Details */}
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <div className="flex items-center space-x-4">
          {showItemCount && processDetails.totalItems && (
            <span>
              {processDetails.processedItems || 0} / {processDetails.totalItems} items
            </span>
          )}
          
          {showSpeed && processDetails.speed && (
            <span>
              {processDetails.speed}
            </span>
          )}
        </div>

        {showTimeRemaining && processDetails.estimatedTimeRemaining && (
          <span>
            {formatTimeRemaining(processDetails.estimatedTimeRemaining)} remaining
          </span>
        )}
      </div>

      {/* Additional Details */}
      {processDetails.details && (
        <p className="text-xs text-gray-500 dark:text-gray-500">
          {processDetails.details}
        </p>
      )}
    </div>
  );
});

ProcessProgress.displayName = 'ProcessProgress';

/**
 * SteppedProgress Component
 * 
 * Multi-step progress indicator for wizards and workflows
 */
interface SteppedProgressProps extends Omit<ProgressProps, 'value' | 'type'> {
  steps: ProgressStep[];
  currentStep?: number;
  orientation?: 'horizontal' | 'vertical';
  showConnectors?: boolean;
  showTiming?: boolean;
  onStepClick?: (step: ProgressStep, index: number) => void;
}

const SteppedProgress = forwardRef<HTMLDivElement, SteppedProgressProps>(({
  steps,
  currentStep = 0,
  orientation = 'horizontal',
  showConnectors = true,
  showTiming = false,
  variant = 'primary',
  size = 'md',
  onStepClick,
  className = '',
  ...props
}, ref) => {
  const getStepIcon = (step: ProgressStep, index: number) => {
    if (step.icon) return step.icon;
    
    switch (step.status) {
      case 'completed':
        return <CheckCircle className={sizeStyles[size].icon} />;
      case 'error':
        return <AlertCircle className={sizeStyles[size].icon} />;
      case 'active':
        return <div className={`${sizeStyles[size].icon} rounded-full bg-current flex items-center justify-center text-white font-bold text-xs`}>{index + 1}</div>;
      default:
        return <div className={`${sizeStyles[size].icon} rounded-full border-2 border-current flex items-center justify-center font-bold text-xs`}>{index + 1}</div>;
    }
  };

  const getStepColor = (step: ProgressStep) => {
    switch (step.status) {
      case 'completed':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      case 'active':
        return barVariantStyles[variant].replace('bg-', 'text-');
      default:
        return 'text-gray-400 dark:text-gray-500';
    }
  };

  const formatDuration = (duration: number) => {
    if (duration < 60) return `${duration}s`;
    return `${Math.round(duration / 60)}m`;
  };

  if (orientation === 'vertical') {
    return (
      <div ref={ref} className={`space-y-4 ${className}`} {...props}>
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-start space-x-3">
            {/* Step Icon */}
            <div className={`flex-shrink-0 ${getStepColor(step)}`}>
              {getStepIcon(step, index)}
            </div>

            {/* Step Content */}
            <div className="flex-1 min-w-0">
              <div
                className={`font-medium ${sizeStyles[size].text} ${
                  onStepClick && step.status !== 'pending' ? 'cursor-pointer hover:underline' : ''
                }`}
                onClick={() => onStepClick?.(step, index)}
              >
                {step.label}
              </div>
              
              {step.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {step.description}
                </p>
              )}

              {showTiming && step.duration && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  Duration: {formatDuration(step.duration)}
                </p>
              )}
            </div>

            {/* Connector Line */}
            {showConnectors && index < steps.length - 1 && (
              <div className="absolute left-5 mt-8 w-0.5 h-8 bg-gray-200 dark:bg-gray-700" />
            )}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div ref={ref} className={`flex items-center space-x-4 ${className}`} {...props}>
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          {/* Step */}
          <div className="flex flex-col items-center space-y-2">
            <div className={`${getStepColor(step)}`}>
              {getStepIcon(step, index)}
            </div>
            
            <div className="text-center">
              <div
                className={`${sizeStyles[size].text} font-medium ${
                  onStepClick && step.status !== 'pending' ? 'cursor-pointer hover:underline' : ''
                }`}
                onClick={() => onStepClick?.(step, index)}
              >
                {step.label}
              </div>
              
              {showTiming && step.duration && (
                <div className="text-xs text-gray-500 dark:text-gray-500">
                  {formatDuration(step.duration)}
                </div>
              )}
            </div>
          </div>

          {/* Connector */}
          {showConnectors && index < steps.length - 1 && (
            <div className="flex-1 h-0.5 bg-gray-200 dark:bg-gray-700 mx-2" />
          )}
        </React.Fragment>
      ))}
    </div>
  );
});

SteppedProgress.displayName = 'SteppedProgress';

/**
 * MultiProgress Component
 * 
 * Multiple progress bars for tracking different aspects
 */
interface MultiProgressProps extends Omit<ProgressProps, 'value'> {
  items: Array<{
    id: string;
    label: string;
    value: number;
    max?: number;
    variant?: ProgressVariant;
    color?: string;
  }>;
  stacked?: boolean;
  showLegend?: boolean;
}

const MultiProgress = forwardRef<HTMLDivElement, MultiProgressProps>(({
  items,
  stacked = false,
  showLegend = true,
  size = 'md',
  className = '',
  ...props
}, ref) => {
  const totalMax = stacked ? items.reduce((sum, item) => sum + (item.max || 100), 0) : 100;

  if (stacked) {
    return (
      <div ref={ref} className={`space-y-2 ${className}`} {...props}>
        {showLegend && (
          <div className="flex flex-wrap gap-4 text-sm">
            {items.map((item) => (
              <div key={item.id} className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded ${item.color || barVariantStyles[item.variant || 'primary']}`} />
                <span>{item.label}</span>
                <span className="text-gray-500">({item.value}%)</span>
              </div>
            ))}
          </div>
        )}

        <div className={`relative ${baseProgressStyles} ${sizeStyles[size].height} bg-gray-200 dark:bg-gray-700`}>
          {items.map((item, index) => {
            const previousWidth = items.slice(0, index).reduce((sum, prev) => sum + prev.value, 0);
            return (
              <div
                key={item.id}
                className={`absolute top-0 h-full ${item.color || barVariantStyles[item.variant || 'primary']}`}
                style={{
                  left: `${(previousWidth / totalMax) * 100}%`,
                  width: `${(item.value / totalMax) * 100}%`,
                }}
              />
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div ref={ref} className={`space-y-3 ${className}`} {...props}>
      {items.map((item) => (
        <Progress
          key={item.id}
          value={item.value}
          max={item.max}
          variant={item.variant}
          label={item.label}
          showPercentage
          size={size}
          color={item.color}
          {...props}
        />
      ))}
    </div>
  );
});

MultiProgress.displayName = 'MultiProgress';

// Attach sub-components to main Progress component
const ProgressWithComponents = Progress as typeof Progress & {
  Process: typeof ProcessProgress;
  Stepped: typeof SteppedProgress;
  Multi: typeof MultiProgress;
};

ProgressWithComponents.Process = ProcessProgress;
ProgressWithComponents.Stepped = SteppedProgress;
ProgressWithComponents.Multi = MultiProgress;

export default ProgressWithComponents;
export type { 
  ProgressProps,
  ProcessProgressProps,
  SteppedProgressProps,
  MultiProgressProps,
  ProgressStep,
  ProcessDetails,
  ProgressVariant,
  ProgressSize,
  ProgressType,
  ProgressStatus,
  ProcessType
};
export { 
  ProcessProgress, 
  SteppedProgress, 
  MultiProgress 
};