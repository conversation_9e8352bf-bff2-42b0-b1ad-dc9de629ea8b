import React from 'react';
import { Sun, Moon } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';

interface ThemeToggleButtonProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'text' | 'full';
}

const ThemeToggleButton: React.FC<ThemeToggleButtonProps> = ({ 
  className = '', 
  size = 'md',
  variant = 'icon' 
}) => {
  const { currentTheme, toggleTheme, isDark } = useTheme();

  const sizeClasses = {
    sm: 'p-2',
    md: 'p-3',
    lg: 'p-4'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const baseClasses = `rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${sizeClasses[size]} ${className}`;

  const buttonClasses = `${baseClasses} ${
    isDark 
      ? 'bg-gray-800 text-white hover:bg-gray-700' 
      : 'bg-white text-gray-900 hover:bg-gray-100 border border-gray-200'
  }`;

  const handleClick = () => {
    toggleTheme();
    
    // Provide haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  if (variant === 'icon') {
    return (
      <button
        onClick={handleClick}
        className={buttonClasses}
        aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      >
        {isDark ? (
          <Sun className={iconSizes[size]} />
        ) : (
          <Moon className={iconSizes[size]} />
        )}
      </button>
    );
  }

  if (variant === 'text') {
    return (
      <button
        onClick={handleClick}
        className={`${baseClasses} text-sm font-medium ${
          isDark 
            ? 'text-gray-300 hover:text-white' 
            : 'text-gray-700 hover:text-gray-900'
        }`}
        aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      >
        {isDark ? 'Light' : 'Dark'}
      </button>
    );
  }

  // Full variant
  return (
    <button
      onClick={handleClick}
      className={`${buttonClasses} flex items-center space-x-2`}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
    >
      {isDark ? (
        <>
          <Sun className={iconSizes[size]} />
          <span className="text-sm font-medium">Light</span>
        </>
      ) : (
        <>
          <Moon className={iconSizes[size]} />
          <span className="text-sm font-medium">Dark</span>
        </>
      )}
    </button>
  );
};

export default ThemeToggleButton; 