import React, { forwardRef, useState, useRef, useEffect, useId } from 'react';
import { 
  ChevronDown, 
  ChevronUp, 
  Check, 
  X, 
  Search,
  AlertCircle,
  CheckCircle,
  Info,
  Camera,
  Eye,
  Settings,
  Zap,
  Users,
  Car,
  Box,
  MapPin
} from 'lucide-react';

// Select variant types
type SelectVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'ghost';

type SelectSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Option type
interface SelectOption {
  value: string | number;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  group?: string;
}

// Detection-specific option types
interface DetectionModelOption extends SelectOption {
  accuracy?: number;
  speed?: 'slow' | 'medium' | 'fast';
  type?: 'person' | 'vehicle' | 'object' | 'general';
}

interface CameraOption extends SelectOption {
  status?: 'online' | 'offline' | 'error';
  resolution?: string;
  fps?: number;
}

interface ZoneOption extends SelectOption {
  active?: boolean;
  detectionCount?: number;
  coordinates?: { x: number; y: number; width: number; height: number };
}

// Icon mapping for different option types
const typeIcons = {
  camera: Camera,
  model: Eye,
  zone: MapPin,
  person: Users,
  vehicle: Car,
  object: Box,
  settings: Settings,
  analysis: Zap,
} as const;

// Base select props
interface BaseSelectProps {
  variant?: SelectVariant;
  size?: SelectSize;
  label?: string;
  description?: string;
  error?: string;
  success?: string;
  warning?: string;
  info?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  loading?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  multiple?: boolean;
  creatable?: boolean;
  groupByOption?: boolean;
  showOptionIcons?: boolean;
  showOptionDescriptions?: boolean;
  maxHeight?: number;
  leftIcon?: React.ReactNode;
  fullWidth?: boolean;
  className?: string;
  selectClassName?: string;
  labelClassName?: string;
  optionClassName?: string;
  value?: string | number | (string | number)[];
  options: SelectOption[];
  onValueChange?: (value: string | number | (string | number)[]) => void;
  onSearch?: (searchTerm: string) => void;
  onCreate?: (value: string) => void;
  renderOption?: (option: SelectOption) => React.ReactNode;
  renderValue?: (value: string | number | (string | number)[]) => React.ReactNode;
}

type SelectProps = BaseSelectProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseSelectProps>;

// Variant styles
const variantStyles: Record<SelectVariant, string> = {
  default: `
    border-gray-300 bg-white text-gray-900
    focus-within:border-primary-500 focus-within:ring-primary-500
    dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100
    dark:focus-within:border-primary-400 dark:focus-within:ring-primary-400
  `,
  primary: `
    border-primary-300 bg-white text-gray-900
    focus-within:border-primary-500 focus-within:ring-primary-500
    dark:border-primary-600 dark:bg-gray-800 dark:text-gray-100
    dark:focus-within:border-primary-400 dark:focus-within:ring-primary-400
  `,
  secondary: `
    border-gray-200 bg-gray-50 text-gray-900
    focus-within:border-gray-400 focus-within:ring-gray-400 focus-within:bg-white
    dark:border-gray-700 dark:bg-gray-900 dark:text-gray-100
    dark:focus-within:border-gray-500 dark:focus-within:ring-gray-500 dark:focus-within:bg-gray-800
  `,
  success: `
    border-green-300 bg-white text-gray-900
    focus-within:border-green-500 focus-within:ring-green-500
    dark:border-green-600 dark:bg-gray-800 dark:text-gray-100
    dark:focus-within:border-green-400 dark:focus-within:ring-green-400
  `,
  warning: `
    border-yellow-300 bg-white text-gray-900
    focus-within:border-yellow-500 focus-within:ring-yellow-500
    dark:border-yellow-600 dark:bg-gray-800 dark:text-gray-100
    dark:focus-within:border-yellow-400 dark:focus-within:ring-yellow-400
  `,
  error: `
    border-red-300 bg-white text-gray-900
    focus-within:border-red-500 focus-within:ring-red-500
    dark:border-red-600 dark:bg-gray-800 dark:text-gray-100
    dark:focus-within:border-red-400 dark:focus-within:ring-red-400
  `,
  detection: `
    border-cyan-300 bg-white text-gray-900
    focus-within:border-cyan-500 focus-within:ring-cyan-500
    dark:border-cyan-600 dark:bg-gray-800 dark:text-gray-100
    dark:focus-within:border-cyan-400 dark:focus-within:ring-cyan-400
  `,
  ghost: `
    border-transparent bg-transparent text-gray-900
    focus-within:border-gray-300 focus-within:ring-gray-300 focus-within:bg-white
    hover:bg-gray-50
    dark:text-gray-100
    dark:focus-within:border-gray-600 dark:focus-within:ring-gray-600 dark:focus-within:bg-gray-800
    dark:hover:bg-gray-800
  `,
};

// Size styles
const sizeStyles: Record<SelectSize, string> = {
  xs: 'h-6 px-2 text-xs',
  sm: 'h-8 px-3 text-sm',
  md: 'h-10 px-3 text-sm',
  lg: 'h-12 px-4 text-base',
  xl: 'h-14 px-5 text-lg',
};

// Icon size styles
const iconSizeStyles: Record<SelectSize, string> = {
  xs: 'w-3 h-3',
  sm: 'w-3.5 h-3.5',
  md: 'w-4 h-4',
  lg: 'w-5 h-5',
  xl: 'w-6 h-6',
};

// Label size styles
const labelSizeStyles: Record<SelectSize, string> = {
  xs: 'text-xs',
  sm: 'text-sm',
  md: 'text-sm',
  lg: 'text-base',
  xl: 'text-lg',
};

// Base select styles
const baseStyles = `
  relative w-full rounded-md border
  shadow-sm transition-all duration-200 ease-in-out
  focus-within:outline-none focus-within:ring-1
  disabled:opacity-50 disabled:cursor-not-allowed disabled:bg-gray-50
  dark:disabled:bg-gray-900
`;

// Dropdown styles
const dropdownStyles = `
  absolute z-50 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg
  dark:bg-gray-800 dark:border-gray-600
  max-h-60 overflow-auto
`;

// Option styles
const optionBaseStyles = `
  px-3 py-2 cursor-pointer transition-colors duration-150
  hover:bg-gray-100 dark:hover:bg-gray-700
  flex items-center justify-between
`;

// Selected option styles
const selectedOptionStyles = `
  bg-primary-50 text-primary-700
  dark:bg-primary-900/30 dark:text-primary-300
`;

// Disabled option styles
const disabledOptionStyles = `
  opacity-50 cursor-not-allowed
  hover:bg-transparent dark:hover:bg-transparent
`;

/**
 * Select Component
 * 
 * A versatile select dropdown component for the VisionGuard Detection Analytics platform.
 * Supports single/multi-select, search, creation, grouping, and detection-specific features.
 * 
 * Features:
 * - Single and multi-select modes
 * - Searchable options with filtering
 * - Creatable options (add new values)
 * - Option grouping and categorization
 * - Rich option display with icons and descriptions
 * - Detection-specific variants and options
 * - Validation states with visual feedback
 * - Loading states and async support
 * - Full accessibility support
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Basic camera selection
 * <Select 
 *   label="Select Camera"
 *   placeholder="Choose a camera..."
 *   options={cameraOptions}
 *   onValueChange={setCameraId}
 * />
 * 
 * // Detection model with descriptions
 * <Select 
 *   label="Detection Model"
 *   variant="detection"
 *   options={modelOptions}
 *   showOptionDescriptions
 *   showOptionIcons
 * />
 * 
 * // Multi-select zones
 * <Select 
 *   label="Active Zones"
 *   multiple
 *   searchable
 *   clearable
 *   options={zoneOptions}
 *   value={selectedZones}
 *   onValueChange={setSelectedZones}
 * />
 * 
 * // Searchable with creation
 * <Select 
 *   label="Zone Name"
 *   searchable
 *   creatable
 *   placeholder="Search or create zone..."
 *   options={existingZones}
 *   onCreate={createNewZone}
 * />
 * ```
 */
const Select = forwardRef<HTMLDivElement, SelectProps>(({
  variant = 'default',
  size = 'md',
  label,
  description,
  error,
  success,
  warning,
  info,
  placeholder = 'Select option...',
  required = false,
  disabled = false,
  loading = false,
  searchable = false,
  clearable = false,
  multiple = false,
  creatable = false,
  groupByOption = false,
  showOptionIcons = false,
  showOptionDescriptions = false,
  maxHeight = 240,
  leftIcon,
  fullWidth = true,
  className = '',
  selectClassName = '',
  labelClassName = '',
  optionClassName = '',
  value,
  options = [],
  onValueChange,
  onSearch,
  onCreate,
  renderOption,
  renderValue,
  ...props
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  
  const selectRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const optionsRef = useRef<HTMLDivElement>(null);
  const selectId = useId();

  // Determine variant based on validation state
  const getVariant = () => {
    if (error) return 'error';
    if (success) return 'success';
    if (warning) return 'warning';
    return variant;
  };

  const currentVariant = getVariant();

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options.filter(option => 
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // Group options if needed
  const groupedOptions = groupByOption
    ? filteredOptions.reduce((groups, option) => {
        const group = option.group || 'Other';
        if (!groups[group]) groups[group] = [];
        groups[group].push(option);
        return groups;
      }, {} as Record<string, SelectOption[]>)
    : { '': filteredOptions };

  // Get selected options
  const selectedOptions = options.filter(option => {
    if (multiple && Array.isArray(value)) {
      return value.includes(option.value);
    }
    return option.value === value;
  });

  // Handle option selection
  const handleOptionSelect = (option: SelectOption) => {
    if (option.disabled) return;

    let newValue: string | number | (string | number)[];

    if (multiple) {
      const currentValues = Array.isArray(value) ? value : [];
      if (currentValues.includes(option.value)) {
        newValue = currentValues.filter(v => v !== option.value);
      } else {
        newValue = [...currentValues, option.value];
      }
    } else {
      newValue = option.value;
      setIsOpen(false);
    }

    onValueChange?.(newValue);
    setSearchTerm('');
    setHighlightedIndex(-1);
  };

  // Handle clear selection
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onValueChange?.(multiple ? [] : '');
    setSearchTerm('');
  };

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    onSearch?.(term);
    setHighlightedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          setHighlightedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : prev
          );
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (isOpen) {
          setHighlightedIndex(prev => prev > 0 ? prev - 1 : prev);
        }
        break;
      case 'Enter':
        e.preventDefault();
        if (isOpen && highlightedIndex >= 0) {
          handleOptionSelect(filteredOptions[highlightedIndex]);
        } else if (!isOpen) {
          setIsOpen(true);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
        break;
      case 'Tab':
        setIsOpen(false);
        break;
    }
  };

  // Handle create new option
  const handleCreate = () => {
    if (creatable && searchTerm && onCreate) {
      onCreate(searchTerm);
      setSearchTerm('');
      setIsOpen(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, searchable]);

  // Get status icon
  const getStatusIcon = () => {
    if (error) return <AlertCircle className={`${iconSizeStyles[size]} text-red-500`} />;
    if (success) return <CheckCircle className={`${iconSizeStyles[size]} text-green-500`} />;
    if (warning) return <AlertCircle className={`${iconSizeStyles[size]} text-yellow-500`} />;
    if (info) return <Info className={`${iconSizeStyles[size]} text-blue-500`} />;
    return null;
  };

  // Render selected value
  const renderSelectedValue = () => {
    if (renderValue) {
      return renderValue(value || (multiple ? [] : ''));
    }

    if (multiple && Array.isArray(value) && value.length > 0) {
      return `${value.length} selected`;
    }

    if (!multiple && value) {
      const selectedOption = options.find(opt => opt.value === value);
      return selectedOption?.label || value;
    }

    return placeholder;
  };

  // Render option
  const renderOptionContent = (option: SelectOption) => {
    if (renderOption) {
      return renderOption(option);
    }

    return (
      <div className="flex items-center space-x-2 flex-1 min-w-0">
        {showOptionIcons && option.icon && (
          <div className={`${iconSizeStyles[size]} flex-shrink-0`}>
            {option.icon}
          </div>
        )}
        <div className="flex-1 min-w-0">
          <div className="truncate">{option.label}</div>
          {showOptionDescriptions && option.description && (
            <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {option.description}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Combine select styles
  const selectStyles = [
    baseStyles,
    variantStyles[currentVariant],
    sizeStyles[size],
    !fullWidth && 'w-auto',
    selectClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Status message
  const statusMessage = error || success || warning || info;
  const statusColor = error ? 'text-red-600 dark:text-red-400' :
                     success ? 'text-green-600 dark:text-green-400' :
                     warning ? 'text-yellow-600 dark:text-yellow-400' :
                     'text-blue-600 dark:text-blue-400';

  return (
    <div ref={ref} className={`${fullWidth ? 'w-full' : 'w-auto'} ${className}`} {...props}>
      {/* Label */}
      {label && (
        <label
          htmlFor={selectId}
          className={`
            block font-medium text-gray-700 dark:text-gray-300 mb-1
            ${labelSizeStyles[size]}
            ${labelClassName}
          `}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      {/* Description */}
      {description && (
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
          {description}
        </p>
      )}

      {/* Select Container */}
      <div
        ref={selectRef}
        className={selectStyles}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        aria-labelledby={label ? `${selectId}-label` : undefined}
        aria-invalid={!!error}
      >
        {/* Select Trigger */}
        <div
          className="flex items-center justify-between cursor-pointer"
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex items-center space-x-2 flex-1 min-w-0">
            {/* Left Icon */}
            {leftIcon && (
              <div className={`${iconSizeStyles[size]} flex-shrink-0 text-gray-400`}>
                {leftIcon}
              </div>
            )}

            {/* Selected Value */}
            <div className="flex-1 min-w-0 truncate text-left">
              <span className={!value || (Array.isArray(value) && value.length === 0) ? 'text-gray-500' : ''}>
                {renderSelectedValue()}
              </span>
            </div>
          </div>

          {/* Right Icons */}
          <div className="flex items-center space-x-1">
            {/* Status Icon */}
            {getStatusIcon()}

            {/* Clear Button */}
            {clearable && (value || (Array.isArray(value) && value.length > 0)) && !disabled && (
              <button
                type="button"
                onClick={handleClear}
                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
                aria-label="Clear selection"
              >
                <X className={iconSizeStyles[size]} />
              </button>
            )}

            {/* Loading Spinner */}
            {loading && (
              <div className="animate-spin text-gray-400 dark:text-gray-500">
                <div className={`${iconSizeStyles[size]} border-2 border-current border-t-transparent rounded-full`} />
              </div>
            )}

            {/* Dropdown Chevron */}
            {!loading && (
              <div className="text-gray-400 dark:text-gray-500">
                {isOpen ? (
                  <ChevronUp className={iconSizeStyles[size]} />
                ) : (
                  <ChevronDown className={iconSizeStyles[size]} />
                )}
              </div>
            )}
          </div>
        </div>

        {/* Dropdown */}
        {isOpen && (
          <div
            ref={optionsRef}
            className={dropdownStyles}
            style={{ maxHeight: maxHeight }}
            role="listbox"
            aria-multiselectable={multiple}
          >
            {/* Search Input */}
            {searchable && (
              <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search options..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="w-full pl-8 pr-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:border-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100"
                  />
                </div>
              </div>
            )}

            {/* Options */}
            <div className="py-1">
              {Object.entries(groupedOptions).map(([groupName, groupOptions]) => (
                <div key={groupName}>
                  {/* Group Header */}
                  {groupByOption && groupName && (
                    <div className="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide bg-gray-50 dark:bg-gray-700">
                      {groupName}
                    </div>
                  )}

                  {/* Group Options */}
                  {groupOptions.map((option, index) => {
                    const isSelected = multiple 
                      ? Array.isArray(value) && value.includes(option.value)
                      : option.value === value;
                    const isHighlighted = filteredOptions.indexOf(option) === highlightedIndex;

                    return (
                      <div
                        key={option.value}
                        className={`
                          ${optionBaseStyles}
                          ${isSelected ? selectedOptionStyles : ''}
                          ${isHighlighted ? 'bg-gray-100 dark:bg-gray-700' : ''}
                          ${option.disabled ? disabledOptionStyles : ''}
                          ${optionClassName}
                        `}
                        onClick={() => handleOptionSelect(option)}
                        role="option"
                        aria-selected={isSelected}
                      >
                        {renderOptionContent(option)}
                        
                        {/* Selected Indicator */}
                        {isSelected && (
                          <Check className={`${iconSizeStyles[size]} text-primary-600 dark:text-primary-400`} />
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}

              {/* No Options */}
              {filteredOptions.length === 0 && (
                <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                  {searchTerm ? 'No options found' : 'No options available'}
                </div>
              )}

              {/* Create Option */}
              {creatable && searchTerm && !filteredOptions.some(opt => opt.label.toLowerCase() === searchTerm.toLowerCase()) && (
                <div
                  className={`${optionBaseStyles} border-t border-gray-200 dark:border-gray-600 text-primary-600 dark:text-primary-400`}
                  onClick={handleCreate}
                >
                  <span>Create "{searchTerm}"</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Status Message */}
      {statusMessage && (
        <p className={`mt-1 text-sm ${statusColor}`}>
          {statusMessage}
        </p>
      )}
    </div>
  );
});

Select.displayName = 'Select';

/**
 * CameraSelect Component
 * 
 * Specialized select for camera selection with status indicators
 */
interface CameraSelectProps extends Omit<SelectProps, 'options' | 'showOptionIcons' | 'showOptionDescriptions'> {
  cameras: CameraOption[];
}

const CameraSelect = forwardRef<HTMLDivElement, CameraSelectProps>(({
  cameras,
  variant = 'detection',
  leftIcon = <Camera className="w-4 h-4" />,
  placeholder = 'Select camera...',
  ...props
}, ref) => {
  const options = cameras.map(camera => ({
    ...camera,
    icon: (
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${
          camera.status === 'online' ? 'bg-green-500' :
          camera.status === 'offline' ? 'bg-gray-400' :
          'bg-red-500'
        }`} />
        <Camera className="w-4 h-4" />
      </div>
    ),
    description: camera.resolution ? `${camera.resolution} • ${camera.fps}fps` : undefined,
  }));

  return (
    <Select
      ref={ref}
      variant={variant}
      leftIcon={leftIcon}
      placeholder={placeholder}
      options={options}
      showOptionIcons
      showOptionDescriptions
      {...props}
    />
  );
});

CameraSelect.displayName = 'CameraSelect';

/**
 * ModelSelect Component
 * 
 * Specialized select for detection model selection
 */
interface ModelSelectProps extends Omit<SelectProps, 'options' | 'showOptionIcons' | 'showOptionDescriptions'> {
  models: DetectionModelOption[];
}

const ModelSelect = forwardRef<HTMLDivElement, ModelSelectProps>(({
  models,
  variant = 'detection',
  leftIcon = <Eye className="w-4 h-4" />,
  placeholder = 'Select detection model...',
  groupByOption = true,
  ...props
}, ref) => {
  const options = models.map(model => ({
    ...model,
    group: model.type || 'General',
    icon: typeIcons[model.type || 'analysis'],
    description: model.accuracy ? `${model.accuracy}% accuracy • ${model.speed} speed` : undefined,
  }));

  return (
    <Select
      ref={ref}
      variant={variant}
      leftIcon={leftIcon}
      placeholder={placeholder}
      options={options}
      groupByOption={groupByOption}
      showOptionIcons
      showOptionDescriptions
      {...props}
    />
  );
});

ModelSelect.displayName = 'ModelSelect';

/**
 * ZoneSelect Component
 * 
 * Specialized select for detection zone selection
 */
interface ZoneSelectProps extends Omit<SelectProps, 'options' | 'showOptionIcons' | 'showOptionDescriptions'> {
  zones: ZoneOption[];
}

const ZoneSelect = forwardRef<HTMLDivElement, ZoneSelectProps>(({
  zones,
  variant = 'detection',
  leftIcon = <MapPin className="w-4 h-4" />,
  placeholder = 'Select zones...',
  multiple = true,
  ...props
}, ref) => {
  const options = zones.map(zone => ({
    ...zone,
    icon: (
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${zone.active ? 'bg-cyan-500' : 'bg-gray-400'}`} />
        <MapPin className="w-4 h-4" />
      </div>
    ),
    description: zone.detectionCount ? `${zone.detectionCount} detections today` : 'No detections',
  }));

  return (
    <Select
      ref={ref}
      variant={variant}
      leftIcon={leftIcon}
      placeholder={placeholder}
      multiple={multiple}
      options={options}
      showOptionIcons
      showOptionDescriptions
      {...props}
    />
  );
});

ZoneSelect.displayName = 'ZoneSelect';

// Attach sub-components to main Select component
const SelectWithComponents = Select as typeof Select & {
  Camera: typeof CameraSelect;
  Model: typeof ModelSelect;
  Zone: typeof ZoneSelect;
};

SelectWithComponents.Camera = CameraSelect;
SelectWithComponents.Model = ModelSelect;
SelectWithComponents.Zone = ZoneSelect;

export default SelectWithComponents;
export type { 
  SelectProps,
  CameraSelectProps,
  ModelSelectProps,
  ZoneSelectProps,
  SelectOption,
  DetectionModelOption,
  CameraOption,
  ZoneOption,
  SelectVariant,
  SelectSize
};
export { CameraSelect, ModelSelect, ZoneSelect }