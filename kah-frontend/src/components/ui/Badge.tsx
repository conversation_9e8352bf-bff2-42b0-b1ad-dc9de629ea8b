import React, { forwardRef } from 'react';
import { X, Wifi, WifiOff, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock, Zap } from 'lucide-react';

// Badge variant types
type BadgeVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'detection-person'
  | 'detection-vehicle'
  | 'detection-object'
  | 'detection-zone'
  | 'live'
  | 'offline'
  | 'processing'
  | 'outline';

type BadgeSize = 'xs' | 'sm' | 'md' | 'lg';

// Badge shape types
type BadgeShape = 'rounded' | 'pill' | 'square';

// Status icons mapping
const statusIcons = {
  live: Wifi,
  offline: WifiOff,
  processing: Clock,
  success: CheckCircle,
  warning: AlertTriangle,
  error: AlertTriangle,
  info: Zap,
} as const;

// Base badge props
interface BaseBadgeProps {
  variant?: BadgeVariant;
  size?: BadgeSize;
  shape?: BadgeShape;
  dot?: boolean;
  pulse?: boolean;
  icon?: React.ReactNode;
  showIcon?: boolean;
  dismissible?: boolean;
  count?: number;
  maxCount?: number;
  className?: string;
  children?: React.ReactNode;
  onDismiss?: () => void;
}

type BadgeProps = BaseBadgeProps & 
  Omit<React.HTMLAttributes<HTMLSpanElement>, keyof BaseBadgeProps>;

// Variant styles using Tailwind classes
const variantStyles: Record<BadgeVariant, string> = {
  default: `
    bg-gray-100 text-gray-800 border-gray-200
    dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700
  `,
  primary: `
    bg-primary-100 text-primary-800 border-primary-200
    dark:bg-primary-900/30 dark:text-primary-300 dark:border-primary-800
  `,
  secondary: `
    bg-gray-100 text-gray-600 border-gray-200
    hover:bg-gray-200 hover:text-gray-700
    dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700
    dark:hover:bg-gray-700 dark:hover:text-gray-300
  `,
  success: `
    bg-green-100 text-green-800 border-green-200
    dark:bg-green-900/30 dark:text-green-300 dark:border-green-800
  `,
  warning: `
    bg-yellow-100 text-yellow-800 border-yellow-200
    dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800
  `,
  error: `
    bg-red-100 text-red-800 border-red-200
    dark:bg-red-900/30 dark:text-red-300 dark:border-red-800
  `,
  info: `
    bg-blue-100 text-blue-800 border-blue-200
    dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800
  `,
  'detection-person': `
    bg-green-100 text-green-800 border-green-200
    dark:bg-green-900/30 dark:text-green-300 dark:border-green-800
  `,
  'detection-vehicle': `
    bg-orange-100 text-orange-800 border-orange-200
    dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-800
  `,
  'detection-object': `
    bg-purple-100 text-purple-800 border-purple-200
    dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800
  `,
  'detection-zone': `
    bg-cyan-100 text-cyan-800 border-cyan-200
    dark:bg-cyan-900/30 dark:text-cyan-300 dark:border-cyan-800
  `,
  live: `
    bg-green-100 text-green-800 border-green-200
    dark:bg-green-900/30 dark:text-green-300 dark:border-green-800
  `,
  offline: `
    bg-gray-100 text-gray-600 border-gray-200
    dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700
  `,
  processing: `
    bg-blue-100 text-blue-800 border-blue-200
    dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800
  `,
  outline: `
    bg-transparent text-gray-700 border-gray-300
    hover:bg-gray-50 hover:text-gray-800
    dark:text-gray-300 dark:border-gray-600
    dark:hover:bg-gray-800 dark:hover:text-gray-200
  `,
};

// Size styles
const sizeStyles: Record<BadgeSize, string> = {
  xs: 'text-xs px-1.5 py-0.5 gap-1',
  sm: 'text-xs px-2 py-1 gap-1',
  md: 'text-sm px-2.5 py-1 gap-1.5',
  lg: 'text-sm px-3 py-1.5 gap-2',
};

// Shape styles
const shapeStyles: Record<BadgeShape, string> = {
  rounded: 'rounded-md',
  pill: 'rounded-full',
  square: 'rounded-none',
};

// Dot variant styles
const dotStyles: Record<BadgeSize, string> = {
  xs: 'w-1.5 h-1.5',
  sm: 'w-2 h-2',
  md: 'w-2.5 h-2.5',
  lg: 'w-3 h-3',
};

// Icon size styles
const iconSizeStyles: Record<BadgeSize, string> = {
  xs: 'w-3 h-3',
  sm: 'w-3.5 h-3.5',
  md: 'w-4 h-4',
  lg: 'w-4 h-4',
};

// Pulse animation styles
const pulseStyles = `
  relative
  before:absolute before:inset-0 before:rounded-full before:animate-ping
  before:bg-current before:opacity-20
`;

// Base badge styles
const baseStyles = `
  inline-flex items-center justify-center
  font-medium border
  transition-all duration-200 ease-in-out
  select-none
`;

// Dismissible styles
const dismissibleStyles = `
  pr-1 hover:pr-1.5
  transition-all duration-200 ease-in-out
`;

// High contrast mode styles
const highContrastStyles = `
  contrast-more:border-2 contrast-more:border-current
`;

/**
 * Badge Component
 * 
 * A versatile badge component for the VisionGuard Detection Analytics platform.
 * Supports status indicators, counts, detection types, and interactive elements.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to lg
 * - Shape options (rounded, pill, square)
 * - Dot indicators for status
 * - Pulse animation for live states
 * - Count badges with overflow handling
 * - Auto status icons
 * - Dismissible badges
 * - Theme-aware styling
 * - Accessibility compliant
 * 
 * @example
 * ```tsx
 * // Basic status badge
 * <Badge variant="success">Active</Badge>
 * 
 * // Detection type badge
 * <Badge variant="detection-person" showIcon>
 *   12 People
 * </Badge>
 * 
 * // Live status with pulse
 * <Badge variant="live" pulse showIcon>
 *   Live
 * </Badge>
 * 
 * // Count badge
 * <Badge variant="error" count={5} maxCount={99}>
 *   Alerts
 * </Badge>
 * 
 * // Dot indicator
 * <Badge variant="success" dot size="sm" />
 * 
 * // Dismissible badge
 * <Badge variant="warning" dismissible onDismiss={handleDismiss}>
 *   Low Storage
 * </Badge>
 * ```
 */
const Badge = forwardRef<HTMLSpanElement, BadgeProps>(({
  variant = 'default',
  size = 'sm',
  shape = 'rounded',
  dot = false,
  pulse = false,
  icon,
  showIcon = false,
  dismissible = false,
  count,
  maxCount = 99,
  className = '',
  children,
  onDismiss,
  ...props
}, ref) => {
  // Auto-select icon based on variant
  const autoIcon = showIcon && !icon && statusIcons[variant as keyof typeof statusIcons];
  const IconComponent = autoIcon;

  // Handle count display
  const displayCount = count !== undefined ? (count > maxCount ? `${maxCount}+` : count.toString()) : null;

  // For dot badges, don't show children
  const content = dot ? null : (children || displayCount);

  // Combine all styles
  const combinedClassName = [
    baseStyles,
    variantStyles[variant],
    sizeStyles[size],
    shapeStyles[shape],
    highContrastStyles,
    dismissible && dismissibleStyles,
    pulse && pulseStyles,
    className,
  ]
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Render dot indicator
  if (dot) {
    return (
      <span
        ref={ref}
        className={`
          ${dotStyles[size]} 
          ${variantStyles[variant]} 
          rounded-full border-2 border-white dark:border-gray-900
          ${pulse ? pulseStyles : ''}
          ${className}
        `}
        aria-label={`${variant} status indicator`}
        {...props}
      />
    );
  }

  return (
    <span
      ref={ref}
      className={combinedClassName}
      {...props}
    >
      {/* Pulse background for live indicators */}
      {pulse && (
        <span className="absolute inset-0 rounded-full animate-ping opacity-20 bg-current" />
      )}
      
      {/* Icon */}
      {IconComponent && (
        <IconComponent 
          className={`${iconSizeStyles[size]} flex-shrink-0`}
          aria-hidden="true"
        />
      )}
      {icon && !IconComponent && (
        <span className={`${iconSizeStyles[size]} flex-shrink-0`} aria-hidden="true">
          {icon}
        </span>
      )}

      {/* Content */}
      {content && (
        <span className="truncate">
          {content}
        </span>
      )}

      {/* Dismissible button */}
      {dismissible && onDismiss && (
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            onDismiss();
          }}
          className={`
            ${iconSizeStyles[size]} flex-shrink-0 ml-1
            hover:bg-black/10 dark:hover:bg-white/10
            rounded-full transition-colors duration-150
            focus:outline-none focus:ring-1 focus:ring-current
          `}
          aria-label="Dismiss"
        >
          <X className="w-full h-full" />
        </button>
      )}
    </span>
  );
});

Badge.displayName = 'Badge';

/**
 * BadgeGroup Component
 * 
 * Groups multiple badges with consistent spacing
 */
interface BadgeGroupProps {
  className?: string;
  children: React.ReactNode;
  spacing?: 'tight' | 'normal' | 'loose';
}

const BadgeGroup = forwardRef<HTMLDivElement, BadgeGroupProps>(({
  className = '',
  children,
  spacing = 'normal',
  ...props
}, ref) => {
  const spacingStyles = {
    tight: 'gap-1',
    normal: 'gap-2',
    loose: 'gap-3',
  };

  return (
    <div
      ref={ref}
      className={`flex flex-wrap items-center ${spacingStyles[spacing]} ${className}`}
      {...props}
    >
      {children}
    </div>
  );
});

BadgeGroup.displayName = 'BadgeGroup';

/**
 * CountBadge Component
 * 
 * Specialized badge for displaying counts with auto-formatting
 */
interface CountBadgeProps extends Omit<BadgeProps, 'children' | 'count'> {
  count: number;
  maxCount?: number;
  showZero?: boolean;
  label?: string;
}

const CountBadge = forwardRef<HTMLSpanElement, CountBadgeProps>(({
  count,
  maxCount = 99,
  showZero = false,
  label,
  variant = 'primary',
  size = 'sm',
  shape = 'pill',
  ...props
}, ref) => {
  // Don't render if count is 0 and showZero is false
  if (count === 0 && !showZero) {
    return null;
  }

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();
  const ariaLabel = label ? `${count} ${label}` : `Count: ${count}`;

  return (
    <Badge
      ref={ref}
      variant={variant}
      size={size}
      shape={shape}
      aria-label={ariaLabel}
      {...props}
    >
      {displayCount}
    </Badge>
  );
});

CountBadge.displayName = 'CountBadge';

/**
 * StatusBadge Component
 * 
 * Specialized badge for status indicators with auto-icons
 */
interface StatusBadgeProps extends Omit<BadgeProps, 'showIcon'> {
  status: 'live' | 'offline' | 'processing' | 'success' | 'warning' | 'error';
  label?: string;
}

const StatusBadge = forwardRef<HTMLSpanElement, StatusBadgeProps>(({
  status,
  label,
  variant,
  pulse: pulseProp,
  ...props
}, ref) => {
  // Auto-determine variant and pulse based on status
  const statusVariant = variant || status;
  const shouldPulse = pulseProp !== undefined ? pulseProp : status === 'live';

  return (
    <Badge
      ref={ref}
      variant={statusVariant as BadgeVariant}
      showIcon
      pulse={shouldPulse}
      {...props}
    >
      {label || status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
});

StatusBadge.displayName = 'StatusBadge';

// Attach sub-components to main Badge component
const BadgeWithComponents = Badge as typeof Badge & {
  Group: typeof BadgeGroup;
  Count: typeof CountBadge;
  Status: typeof StatusBadge;
};

BadgeWithComponents.Group = BadgeGroup;
BadgeWithComponents.Count = CountBadge;
BadgeWithComponents.Status = StatusBadge;

export default BadgeWithComponents;
export type { 
  BadgeProps, 
  BadgeGroupProps,
  CountBadgeProps,
  StatusBadgeProps,
  BadgeVariant, 
  BadgeSize,
  BadgeShape
};
export { BadgeGroup, CountBadge, StatusBadge };