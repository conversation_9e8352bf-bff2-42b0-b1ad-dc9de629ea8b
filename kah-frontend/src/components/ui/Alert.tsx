import React, { forwardRef, useState, useEffect, useCallback, useId } from 'react';
import { 
  X,
  Check,
  AlertTriangle,
  AlertCircle,
  Info,
  Bell,
  Eye,
  Camera,
  Shield,
  Zap,
  Clock,
  Users,
  Car,
  Box,
  MapPin,
  Wifi,
  WifiOff,
  HardDrive,
  Cpu,
  Activity,
  TrendingUp,
  Download,
  Upload,
  Settings,
  RefreshCw
} from 'lucide-react';

// Alert variant types
type AlertVariant = 
  | 'info'
  | 'success'
  | 'warning'
  | 'error'
  | 'detection'
  | 'system'
  | 'security'
  | 'default';

type AlertSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

type AlertType = 
  | 'detection'
  | 'camera'
  | 'system'
  | 'security'
  | 'storage'
  | 'network'
  | 'performance'
  | 'maintenance'
  | 'export'
  | 'update'
  | 'general';

// Detection-specific alert types
interface DetectionAlert {
  id: string;
  type: 'detection';
  subtype: 'person' | 'vehicle' | 'object' | 'intrusion' | 'loitering';
  cameraId: string;
  cameraName: string;
  zoneId?: string;
  zoneName?: string;
  confidence: number;
  timestamp: Date;
  imageUrl?: string;
  videoUrl?: string;
  coordinates?: { x: number; y: number; width: number; height: number };
}

interface SystemAlert {
  id: string;
  type: 'system' | 'camera' | 'storage' | 'network' | 'performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: string;
  timestamp: Date;
  resolved?: boolean;
  autoResolve?: boolean;
  deviceId?: string;
  deviceName?: string;
}

// Icon mapping for different alert types
const alertTypeIcons = {
  detection: Eye,
  camera: Camera,
  system: Settings,
  security: Shield,
  storage: HardDrive,
  network: Wifi,
  performance: Cpu,
  maintenance: RefreshCw,
  export: Download,
  update: Upload,
  general: Bell,
} as const;

// Detection subtype icons
const detectionIcons = {
  person: Users,
  vehicle: Car,
  object: Box,
  intrusion: Shield,
  loitering: Clock,
} as const;

// Severity icons
const severityIcons = {
  low: Info,
  medium: AlertTriangle,
  high: AlertCircle,
  critical: AlertTriangle,
} as const;

// Base alert props
interface BaseAlertProps {
  variant?: AlertVariant;
  size?: AlertSize;
  type?: AlertType;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  autoIcon?: boolean;
  dismissible?: boolean;
  autoClose?: boolean;
  autoCloseDelay?: number;
  showTimestamp?: boolean;
  timestamp?: Date;
  actions?: React.ReactNode;
  className?: string;
  onDismiss?: () => void;
  onAction?: (actionId: string) => void;
  children?: React.ReactNode;
}

type AlertProps = BaseAlertProps & 
  Omit<React.HTMLAttributes<HTMLDivElement>, keyof BaseAlertProps>;

// Variant styles
const variantStyles: Record<AlertVariant, string> = {
  default: `
    bg-gray-50 border-gray-200 text-gray-800
    dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200
  `,
  info: `
    bg-blue-50 border-blue-200 text-blue-800
    dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-300
  `,
  success: `
    bg-green-50 border-green-200 text-green-800
    dark:bg-green-900/20 dark:border-green-700 dark:text-green-300
  `,
  warning: `
    bg-yellow-50 border-yellow-200 text-yellow-800
    dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-300
  `,
  error: `
    bg-red-50 border-red-200 text-red-800
    dark:bg-red-900/20 dark:border-red-700 dark:text-red-300
  `,
  detection: `
    bg-cyan-50 border-cyan-200 text-cyan-800
    dark:bg-cyan-900/20 dark:border-cyan-700 dark:text-cyan-300
  `,
  system: `
    bg-purple-50 border-purple-200 text-purple-800
    dark:bg-purple-900/20 dark:border-purple-700 dark:text-purple-300
  `,
  security: `
    bg-orange-50 border-orange-200 text-orange-800
    dark:bg-orange-900/20 dark:border-orange-700 dark:text-orange-300
  `,
};

// Size styles
const sizeStyles: Record<AlertSize, { container: string; icon: string; text: string; action: string }> = {
  xs: {
    container: 'p-2 text-xs',
    icon: 'w-3 h-3',
    text: 'text-xs',
    action: 'text-xs px-1.5 py-0.5',
  },
  sm: {
    container: 'p-3 text-sm',
    icon: 'w-4 h-4',
    text: 'text-sm',
    action: 'text-sm px-2 py-1',
  },
  md: {
    container: 'p-4 text-sm',
    icon: 'w-5 h-5',
    text: 'text-sm',
    action: 'text-sm px-3 py-1.5',
  },
  lg: {
    container: 'p-5 text-base',
    icon: 'w-6 h-6',
    text: 'text-base',
    action: 'text-base px-4 py-2',
  },
  xl: {
    container: 'p-6 text-lg',
    icon: 'w-8 h-8',
    text: 'text-lg',
    action: 'text-lg px-5 py-2.5',
  },
};

// Base alert styles
const baseAlertStyles = `
  border rounded-lg
  transition-all duration-300 ease-in-out
  transform
`;

/**
 * Alert Component
 * 
 * A versatile alert/notification component for the VisionGuard Detection Analytics platform.
 * Supports detection alerts, system notifications, and various feedback messages.
 * 
 * Features:
 * - Multiple variants including detection-specific themes
 * - Size variants from xs to xl
 * - Auto-icons based on alert type
 * - Dismissible alerts with close button
 * - Auto-close functionality with customizable delay
 * - Timestamp display for tracking when alerts occurred
 * - Action buttons for user interactions
 * - Rich content support with descriptions and custom elements
 * - Detection-specific alert layouts
 * - System health and status alerts
 * - Theme-aware styling
 * 
 * @example
 * ```tsx
 * // Basic detection alert
 * <Alert 
 *   variant="detection"
 *   title="Person Detected"
 *   description="Motion detected in Main Entrance zone"
 *   showTimestamp
 *   dismissible
 * />
 * 
 * // System warning with actions
 * <Alert 
 *   variant="warning"
 *   type="storage"
 *   title="Storage Warning"
 *   description="Storage is 85% full. Consider cleaning old recordings."
 *   actions={
 *     <div className="space-x-2">
 *       <Button size="sm" variant="warning">Clean Storage</Button>
 *       <Button size="sm" variant="ghost">Settings</Button>
 *     </div>
 *   }
 * />
 * 
 * // Auto-closing success alert
 * <Alert 
 *   variant="success"
 *   title="Export Complete"
 *   description="Analytics data exported successfully"
 *   autoClose
 *   autoCloseDelay={5000}
 * />
 * ```
 */
const Alert = forwardRef<HTMLDivElement, AlertProps>(({
  variant = 'default',
  size = 'md',
  type = 'general',
  title,
  description,
  icon,
  autoIcon = true,
  dismissible = false,
  autoClose = false,
  autoCloseDelay = 5000,
  showTimestamp = false,
  timestamp,
  actions,
  className = '',
  onDismiss,
  onAction,
  children,
  ...props
}, ref) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isClosing, setIsClosing] = useState(false);
  const alertId = useId();

  // Get auto-icon based on type
  const getAutoIcon = useCallback(() => {
    if (!autoIcon || icon) return null;
    const IconComponent = alertTypeIcons[type] || alertTypeIcons.general;
    return <IconComponent className={sizeStyles[size].icon} />;
  }, [autoIcon, icon, type, size]);

  // Handle dismiss
  const handleDismiss = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      setIsVisible(false);
      onDismiss?.();
    }, 300);
  }, [onDismiss]);

  // Auto-close functionality
  useEffect(() => {
    if (autoClose && autoCloseDelay > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoCloseDelay);
      
      return () => clearTimeout(timer);
    }
  }, [autoClose, autoCloseDelay, handleDismiss]);

  // Format timestamp
  const formatTimestamp = useCallback((date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  }, []);

  if (!isVisible) return null;

  // Combine styles
  const alertStyles = [
    baseAlertStyles,
    variantStyles[variant],
    sizeStyles[size].container,
    isClosing ? 'opacity-0 scale-95 translate-y-2' : 'opacity-100 scale-100 translate-y-0',
    className,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div
      ref={ref}
      role="alert"
      aria-labelledby={title ? `${alertId}-title` : undefined}
      aria-describedby={description ? `${alertId}-description` : undefined}
      className={alertStyles}
      {...props}
    >
      <div className="flex items-start space-x-3">
        {/* Icon */}
        {(icon || getAutoIcon()) && (
          <div className="flex-shrink-0 mt-0.5">
            {icon || getAutoIcon()}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              {title && (
                <h3 
                  id={`${alertId}-title`}
                  className={`font-medium ${sizeStyles[size].text} leading-tight`}
                >
                  {title}
                </h3>
              )}
              
              {showTimestamp && (timestamp || showTimestamp) && (
                <div className={`text-xs opacity-75 mt-1`}>
                  {formatTimestamp(timestamp || new Date())}
                </div>
              )}
            </div>

            {/* Dismiss Button */}
            {dismissible && (
              <button
                type="button"
                onClick={handleDismiss}
                className="
                  flex-shrink-0 ml-3 p-1 rounded-md opacity-70 hover:opacity-100
                  hover:bg-black/5 dark:hover:bg-white/5
                  focus:outline-none focus:ring-2 focus:ring-current
                  transition-all duration-200
                "
                aria-label="Dismiss alert"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Description */}
          {description && (
            <div 
              id={`${alertId}-description`}
              className={`mt-1 ${sizeStyles[size].text} opacity-90 leading-relaxed`}
            >
              {description}
            </div>
          )}

          {/* Children */}
          {children && (
            <div className="mt-2">
              {children}
            </div>
          )}

          {/* Actions */}
          {actions && (
            <div className="mt-3">
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

Alert.displayName = 'Alert';

/**
 * DetectionAlert Component
 * 
 * Specialized alert for detection events
 */
interface DetectionAlertProps extends Omit<AlertProps, 'variant' | 'type'> {
  detection: DetectionAlert;
  showImage?: boolean;
  showZone?: boolean;
  showConfidence?: boolean;
  onViewDetails?: (detection: DetectionAlert) => void;
  onViewCamera?: (cameraId: string) => void;
}

const DetectionAlert = forwardRef<HTMLDivElement, DetectionAlertProps>(({
  detection,
  showImage = true,
  showZone = true,
  showConfidence = true,
  onViewDetails,
  onViewCamera,
  ...props
}, ref) => {
  const DetectionIcon = detectionIcons[detection.subtype] || Eye;
  
  const handleViewDetails = () => {
    onViewDetails?.(detection);
  };

  const handleViewCamera = () => {
    onViewCamera?.(detection.cameraId);
  };

  return (
    <Alert
      ref={ref}
      variant="detection"
      type="detection"
      title={`${detection.subtype.charAt(0).toUpperCase() + detection.subtype.slice(1)} Detected`}
      icon={<DetectionIcon className="w-5 h-5" />}
      showTimestamp
      timestamp={detection.timestamp}
      dismissible
      {...props}
    >
      <div className="space-y-3">
        {/* Detection Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Camera:</span> {detection.cameraName}
          </div>
          {showZone && detection.zoneName && (
            <div>
              <span className="font-medium">Zone:</span> {detection.zoneName}
            </div>
          )}
          {showConfidence && (
            <div>
              <span className="font-medium">Confidence:</span> {detection.confidence}%
            </div>
          )}
        </div>

        {/* Detection Image */}
        {showImage && detection.imageUrl && (
          <div className="mt-3">
            <img
              src={detection.imageUrl}
              alt="Detection snapshot"
              className="w-full h-32 object-cover rounded-md border"
            />
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-2">
          {onViewDetails && (
            <button
              onClick={handleViewDetails}
              className="px-3 py-1.5 text-sm font-medium text-cyan-700 hover:text-cyan-800 hover:bg-cyan-100 rounded-md transition-colors"
            >
              View Details
            </button>
          )}
          {onViewCamera && (
            <button
              onClick={handleViewCamera}
              className="px-3 py-1.5 text-sm font-medium text-cyan-700 hover:text-cyan-800 hover:bg-cyan-100 rounded-md transition-colors"
            >
              View Camera
            </button>
          )}
        </div>
      </div>
    </Alert>
  );
});

DetectionAlert.displayName = 'DetectionAlert';

/**
 * SystemAlert Component
 * 
 * Specialized alert for system notifications
 */
interface SystemAlertProps extends Omit<AlertProps, 'variant' | 'type'> {
  alert: SystemAlert;
  showSeverity?: boolean;
  showDevice?: boolean;
  onResolve?: (alertId: string) => void;
  onViewDetails?: (alert: SystemAlert) => void;
}

const SystemAlert = forwardRef<HTMLDivElement, SystemAlertProps>(({
  alert,
  showSeverity = true,
  showDevice = true,
  onResolve,
  onViewDetails,
  ...props
}, ref) => {
  const severityVariants = {
    low: 'info' as const,
    medium: 'warning' as const,
    high: 'error' as const,
    critical: 'error' as const,
  };

  const SeverityIcon = severityIcons[alert.severity];
  const TypeIcon = alertTypeIcons[alert.type];

  const handleResolve = () => {
    onResolve?.(alert.id);
  };

  const handleViewDetails = () => {
    onViewDetails?.(alert);
  };

  return (
    <Alert
      ref={ref}
      variant={severityVariants[alert.severity]}
      type={alert.type}
      title={alert.message}
      description={alert.details}
      icon={<TypeIcon className="w-5 h-5" />}
      showTimestamp
      timestamp={alert.timestamp}
      dismissible={!alert.resolved}
      {...props}
    >
      <div className="space-y-3">
        {/* Alert Metadata */}
        <div className="flex items-center space-x-4 text-sm">
          {showSeverity && (
            <div className="flex items-center space-x-1">
              <SeverityIcon className="w-4 h-4" />
              <span className="font-medium capitalize">{alert.severity}</span>
            </div>
          )}
          
          {showDevice && alert.deviceName && (
            <div>
              <span className="font-medium">Device:</span> {alert.deviceName}
            </div>
          )}

          {alert.resolved && (
            <div className="flex items-center space-x-1 text-green-600">
              <Check className="w-4 h-4" />
              <span className="font-medium">Resolved</span>
            </div>
          )}
        </div>

        {/* Actions */}
        {!alert.resolved && (
          <div className="flex space-x-2">
            {onResolve && (
              <button
                onClick={handleResolve}
                className="px-3 py-1.5 text-sm font-medium text-green-700 hover:text-green-800 hover:bg-green-100 rounded-md transition-colors"
              >
                Mark Resolved
              </button>
            )}
            {onViewDetails && (
              <button
                onClick={handleViewDetails}
                className="px-3 py-1.5 text-sm font-medium text-gray-700 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              >
                View Details
              </button>
            )}
          </div>
        )}
      </div>
    </Alert>
  );
});

SystemAlert.displayName = 'SystemAlert';

/**
 * AlertList Component
 * 
 * Container for displaying multiple alerts
 */
interface AlertListProps {
  alerts: (DetectionAlert | SystemAlert)[];
  maxVisible?: number;
  showViewAll?: boolean;
  className?: string;
  onViewAll?: () => void;
  onAlertAction?: (alert: DetectionAlert | SystemAlert, action: string) => void;
}

const AlertList = forwardRef<HTMLDivElement, AlertListProps>(({
  alerts,
  maxVisible = 5,
  showViewAll = true,
  className = '',
  onViewAll,
  onAlertAction,
  ...props
}, ref) => {
  const visibleAlerts = alerts.slice(0, maxVisible);
  const hasMoreAlerts = alerts.length > maxVisible;

  return (
    <div
      ref={ref}
      className={`space-y-3 ${className}`}
      {...props}
    >
      {visibleAlerts.map((alert) => {
        if ('subtype' in alert) {
          // Detection alert
          return (
            <DetectionAlert
              key={alert.id}
              detection={alert}
              onViewDetails={(detection) => onAlertAction?.(detection, 'view-details')}
              onViewCamera={(cameraId) => onAlertAction?.(alert, 'view-camera')}
            />
          );
        } else {
          // System alert
          return (
            <SystemAlert
              key={alert.id}
              alert={alert}
              onResolve={(alertId) => onAlertAction?.(alert, 'resolve')}
              onViewDetails={(systemAlert) => onAlertAction?.(systemAlert, 'view-details')}
            />
          );
        }
      })}

      {/* View All Button */}
      {hasMoreAlerts && showViewAll && onViewAll && (
        <div className="pt-2">
          <button
            onClick={onViewAll}
            className="
              w-full px-4 py-2 text-sm font-medium text-gray-600 
              hover:text-gray-800 hover:bg-gray-50 
              dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-800
              rounded-md border border-gray-200 dark:border-gray-700
              transition-colors duration-200
            "
          >
            View All Alerts ({alerts.length - maxVisible} more)
          </button>
        </div>
      )}

      {/* Empty State */}
      {alerts.length === 0 && (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p>No alerts at the moment</p>
        </div>
      )}
    </div>
  );
});

AlertList.displayName = 'AlertList';

/**
 * Toast Component
 * 
 * Floating toast notification for temporary messages
 */
interface ToastProps extends Omit<AlertProps, 'dismissible'> {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
  duration?: number;
  onClose?: () => void;
}

const Toast = forwardRef<HTMLDivElement, ToastProps>(({
  position = 'top-right',
  duration = 5000,
  onClose,
  className = '',
  ...props
}, ref) => {
  const [isVisible, setIsVisible] = useState(true);

  const positionStyles = {
    'top-right': 'fixed top-4 right-4 z-50',
    'top-left': 'fixed top-4 left-4 z-50',
    'bottom-right': 'fixed bottom-4 right-4 z-50',
    'bottom-left': 'fixed bottom-4 left-4 z-50',
    'top-center': 'fixed top-4 left-1/2 transform -translate-x-1/2 z-50',
    'bottom-center': 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50',
  };

  const handleDismiss = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      onClose?.();
    }, 300);
  }, [onClose]);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(handleDismiss, duration);
      return () => clearTimeout(timer);
    }
  }, [duration, handleDismiss]);

  if (!isVisible) return null;

  return (
    <div className={positionStyles[position]}>
      <Alert
        ref={ref}
        dismissible
        onDismiss={handleDismiss}
        className={`
          shadow-lg ring-1 ring-black ring-opacity-5 min-w-80 max-w-md
          ${className}
        `}
        {...props}
      />
    </div>
  );
});

Toast.displayName = 'Toast';

// Attach sub-components to main Alert component
const AlertWithComponents = Alert as typeof Alert & {
  Detection: typeof DetectionAlert;
  System: typeof SystemAlert;
  List: typeof AlertList;
  Toast: typeof Toast;
};

AlertWithComponents.Detection = DetectionAlert;
AlertWithComponents.System = SystemAlert;
AlertWithComponents.List = AlertList;
AlertWithComponents.Toast = Toast;

export default AlertWithComponents;
export type { 
  AlertProps,
  DetectionAlertProps,
  SystemAlertProps,
  AlertListProps,
  ToastProps,
  DetectionAlert as DetectionAlertType,
  SystemAlert as SystemAlertType,
  AlertVariant,
  AlertSize,
  AlertType
};
export { 
  DetectionAlert, 
  SystemAlert, 
  AlertList, 
  Toast 
};