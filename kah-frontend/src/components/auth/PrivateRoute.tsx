// src/components/auth/PrivateRoute.tsx
import React, { useEffect, useCallback } from 'react';
import type { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import type { User } from '../../types/auth';

interface PrivateRouteProps {
  children: ReactNode;
  requiredRole?: string;
  fallbackPath?: string;
  showUnauthorizedMessage?: boolean;
}

// Role-based routing configuration
const getRoleBasedPath = (roleName?: string): string => {
  switch (roleName) {
    case 'operation_board':
      return '/operations/dashboard';
    case 'analyst':
      return '/analyst/donnee';
    case 'decision_board':
      return '/board/dashboard';
    case 'super_admin':
    case 'admin':
      return '/operations/dashboard';
    default:
      return '/operations/dashboard';
  }
};

const PrivateRoute: React.FC<PrivateRouteProps> = ({ 
  children, 
  requiredRole,
  fallbackPath = '/auth/login',
  showUnauthorizedMessage = true
}) => {
  const { user, isLoading, isAuthenticated } = useAuth();
  const location = useLocation();

  useEffect(() => {
    console.log('PrivateRoute Render - Loading:', isLoading, 'isAuthenticated:', isAuthenticated, 'User:', user?.username || 'N/A', 'Path:', location.pathname);
  }, [isLoading, isAuthenticated, user, location.pathname]);

  // Check if user has required role
  const hasRequiredRole = useCallback((currentUser: User, role?: string): boolean => {
    if (!role) return true;
    const userRoleName = currentUser.role?.name;
    return userRoleName === role || userRoleName === 'super_admin' || userRoleName === 'admin'; // Admin has access to everything
  }, []);

  // If still loading authentication state, show loading screen
  if (isLoading) {
    console.log('PrivateRoute: Loading state is true, showing loading screen.');
    return (
      <div className="min-h-screen bg-gradient-to-br from-kaydan-gray-50 to-kaydan-gray-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          {/* Kaydan Analytics Logo */}
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-kaydan-500 rounded-xl shadow-lg">
              <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
          
          {/* Loading Spinner */}
          <div className="flex justify-center">
            <div className="w-10 h-10 border-4 border-kaydan-200 border-t-kaydan-500 rounded-full animate-spin"></div>
          </div>
          
          {/* Loading Text */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-kaydan-gray-900">
              Kaydan Analytics Hub
            </h2>
            <p className="text-kaydan-gray-600">
              Authenticating your session...
            </p>
          </div>
          
          {/* Loading Animation */}
          <div className="flex justify-center space-x-1 mt-4">
            <div className="w-2 h-2 bg-kaydan-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-2 h-2 bg-kaydan-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-2 h-2 bg-kaydan-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated || !user) {
    console.log('PrivateRoute: Not authenticated or user is null. Redirecting to login.');
    return (
      <Navigate 
        to={fallbackPath} 
        state={{ 
          from: location,
          message: 'Please log in to access this page.'
        }} 
        replace 
      />
    );
  }

  // Check role-based access
  if (!hasRequiredRole(user, requiredRole)) {
    console.log(`PrivateRoute: User (${user.username}, role: ${user.role?.name}) lacks required role (${requiredRole}).`);
    
    if (showUnauthorizedMessage) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-kaydan-gray-50 to-kaydan-gray-100 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-2xl shadow-xl border border-kaydan-gray-200 p-8 text-center">
            {/* Kaydan Analytics Logo */}
            <div className="flex justify-center mb-6">
              <div className="p-3 bg-red-100 rounded-xl">
                <svg className="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            
            {/* Unauthorized Message */}
            <div className="space-y-4">
              <h2 className="text-2xl font-bold text-kaydan-gray-900">
                Access Denied
              </h2>
              <p className="text-kaydan-gray-600">
                You don't have the required permissions to access this resource.
                {requiredRole && (
                  <span className="block mt-2 text-sm">
                    Required role: <span className="font-medium text-red-600">{requiredRole}</span>
                  </span>
                )}
              </p>
              
              {/* User Info */}
              <div className="bg-kaydan-gray-50 rounded-lg p-4 text-sm">
                <div className="text-kaydan-gray-600">Signed in as:</div>
                <div className="font-medium text-kaydan-gray-900">{user.first_name} {user.last_name}</div>
                <div className="text-kaydan-gray-600">{user.email}</div>
                <div className="text-xs text-kaydan-gray-500 mt-1">
                  Role: {user.role?.name || 'No role assigned'}
                </div>
              </div>
              
              {/* Actions */}
              <div className="space-y-3">
                <button
                  onClick={() => window.history.back()}
                  className="w-full bg-kaydan-600 text-white py-3 px-4 rounded-lg hover:bg-kaydan-700 transition-colors font-medium"
                >
                  Go Back
                </button>
                <button
                  onClick={() => window.location.href = getRoleBasedPath(user.role?.name)}
                  className="w-full bg-kaydan-gray-100 text-kaydan-gray-700 py-3 px-4 rounded-lg hover:bg-kaydan-gray-200 transition-colors font-medium"
                >
                  Go to My Dashboard
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return <Navigate to={getRoleBasedPath(user.role?.name)} replace />;
    }
  }

  // If all checks pass, render the protected content
  console.log('PrivateRoute: All checks passed. Rendering children.');
  return <>{children}</>;
};

export default PrivateRoute;