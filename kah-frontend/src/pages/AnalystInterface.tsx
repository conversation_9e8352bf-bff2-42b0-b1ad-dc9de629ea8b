import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  Card, 
  CardHeader, 
  CardContent,
  Button,
  Badge
} from '../components/ui';
import { 
  Eye,
  Settings,
  Database,
  TrendingUp,
  Activity,
  Zap,
  Bell
} from 'lucide-react';

// Import layout components
import BaseInterface from '../components/layout/BaseInterface';

// Import child components
import Data from './Analyste/Data';
import Analysis from './Analyste/Analysis';
import Publisher from './Analyste/Publisher';
import AnalyticDashboard from './Analyste/AnalyticDashboard';

const AnalystInterface: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Determine which component to show based on the current route
  const getCurrentComponent = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard')) {
      return <AnalyticDashboard />;
    } else if (path.includes('/donnee')) {
      return <Data />;
    } else if (path.includes('/analyse')) {
      return <Analysis />;
    } else if (path.includes('/publie')) {
      return <Publisher />;
    } else {
      // Default to dashboard view
      return <AnalyticDashboard />;
    }
  };

  // Get current page title based on route
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard')) {
      return 'Analytics Dashboard';
    } else if (path.includes('/donnee')) {
      return 'Data Management';
    } else if (path.includes('/analyse')) {
      return 'Data Analysis';
    } else if (path.includes('/publie')) {
      return 'Publish Results';
    } else {
      return 'Analytics Dashboard';
    }
  };

  const getPageDescription = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard')) {
      return 'Overview of analysis processes and results';
    } else if (path.includes('/donnee')) {
      return 'Import, manage, and organize your data sources';
    } else if (path.includes('/analyse')) {
      return 'Visualize, analyze, and model your data';
    } else if (path.includes('/publie')) {
      return 'Distribute analysis results to target dashboards';
    } else {
      return 'Overview of analysis processes and results';
    }
  };

  // Action buttons for the page header
  const actionButtons = (
    <>
      <Button 
        variant="outline" 
        leftIcon={<Settings className="w-4 h-4" />}
        className="bg-white/10 border-white/20 text-white hover:bg-white/20"
      >
        Settings
      </Button>
      <Button 
        variant="primary" 
        leftIcon={<Bell className="w-4 h-4" />}
        className="bg-white text-primary-600 hover:bg-gray-100"
        onClick={() => navigate('/notifications')}
      >
        Notifications
      </Button>
      <Button 
        variant="primary" 
        leftIcon={<Eye className="w-4 h-4" />}
        className="bg-white text-primary-600 hover:bg-gray-100"
      >
        View Reports
      </Button>
    </>
  );

  // Additional content for analyst interface (status cards, etc.)
  const additionalContent = (
    <>
      {/* System Status */}
      <div className="flex items-center justify-between bg-white dark:bg-dark-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-dark-700">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
            <Zap className="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">System Status</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">All analytics services are running normally</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="success" size="sm">
            Online
          </Badge>
          <span className="text-xs text-gray-500 dark:text-gray-400">Last update: 2 minutes ago</span>
        </div>
      </div>
    </>
  );

  return (
    <BaseInterface
      getCurrentComponent={getCurrentComponent}
      getPageTitle={getPageTitle}
      getPageDescription={getPageDescription}
      additionalContent={additionalContent}
      actionButtons={actionButtons}
    >
      {/* Background Processing Status */}
      <Card className="bg-white dark:bg-dark-800 shadow-sm border border-gray-200 dark:border-dark-700">
        <CardHeader 
          title="Background Processing"
          subtitle="Current analysis tasks"
          action={
            <Badge variant="info" size="sm">
              <Activity className="w-3 h-3 mr-1" />
              Active
            </Badge>
          }
        />
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/10 dark:to-blue-800/10 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Database className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Data Processing</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Processing 1,234 records</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full transition-all duration-300" style={{ width: '65%' }} />
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">65%</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/10 dark:to-green-800/10 rounded-xl border border-green-200 dark:border-green-800">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Report Generation</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Generating monthly report</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full transition-all duration-300" style={{ width: '30%' }} />
                </div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">30%</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </BaseInterface>
  );
};

export default AnalystInterface;
