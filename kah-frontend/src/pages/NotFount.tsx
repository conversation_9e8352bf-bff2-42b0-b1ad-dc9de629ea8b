import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Card, 
  CardHeader, 
  CardContent,
  Button
} from '../components/ui';
import { 
  Home,
  ArrowLeft,
  Search,
  HelpCircle,
  Mail,
  RefreshCw
} from 'lucide-react';

// Import layout components
import Header from '../components/layout/Header';
import Sidebar from '../components/layout/Sidebar';
import Footer from '../components/layout/Footer';

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-dark-900">
      {/* Mobile sidebar overlay */}
      <div className="fixed inset-0 z-50 lg:hidden opacity-0 pointer-events-none">
        <div className="absolute inset-0 bg-black opacity-50" />
        <div className="relative w-64 h-full bg-white dark:bg-dark-800 shadow-xl transform -translate-x-full">
          <Sidebar />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:block w-64 bg-white dark:bg-dark-800 shadow-lg border-r border-gray-200 dark:border-dark-700">
        <Sidebar />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header onMenuClick={() => {}} />

        {/* Main content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-dark-900">
          <div className="min-h-full flex items-center justify-center p-6">
            <div className="max-w-2xl w-full">
              {/* 404 Content */}
              <div className="text-center mb-8">
                <div className="mb-6">
                  <div className="text-9xl font-bold text-gray-200 dark:text-gray-700 mb-4">
                    404
                  </div>
                  <div className="text-6xl font-bold text-primary-600 dark:text-primary-400 mb-4">
                    Oops!
                  </div>
                </div>
                
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Page Not Found
                </h1>
                
                <p className="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                  The page you're looking for doesn't exist or has been moved. 
                  Don't worry, we'll help you get back on track.
                </p>
              </div>

              {/* Action Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <Card>
                  <CardHeader title="Quick Actions" />
                  <CardContent>
                    <div className="space-y-3">
                      <Button 
                        variant="primary" 
                        leftIcon={<Home className="w-4 h-4" />}
                        onClick={handleGoHome}
                        fullWidth
                      >
                        Go to Homepage
                      </Button>
                      <Button 
                        variant="outline" 
                        leftIcon={<ArrowLeft className="w-4 h-4" />}
                        onClick={handleGoBack}
                        fullWidth
                      >
                        Go Back
                      </Button>
                      <Button 
                        variant="outline" 
                        leftIcon={<RefreshCw className="w-4 h-4" />}
                        onClick={handleRefresh}
                        fullWidth
                      >
                        Refresh Page
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader title="Need Help?" />
                  <CardContent>
                    <div className="space-y-3">
                      <Link 
                        to="/settings"
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                      >
                        <Search className="w-5 h-5 text-primary-600" />
                        <span className="text-gray-700 dark:text-gray-300">Search Settings</span>
                      </Link>
                      <a 
                        href="mailto:<EMAIL>"
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                      >
                        <Mail className="w-5 h-5 text-primary-600" />
                        <span className="text-gray-700 dark:text-gray-300">Contact Support</span>
                      </a>
                      <button 
                        className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors w-full text-left"
                        onClick={() => window.open('https://help.kaydangroup.com', '_blank')}
                      >
                        <HelpCircle className="w-5 h-5 text-primary-600" />
                        <span className="text-gray-700 dark:text-gray-300">Help Center</span>
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Common Pages */}
              <Card>
                <CardHeader 
                  title="Popular Pages" 
                  subtitle="Quick access to commonly used sections"
                />
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Link 
                      to="/analyst/dashboard"
                      className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-dark-700 hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                    >
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                        <Home className="w-4 h-4 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Analyst Dashboard</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Data analysis overview</div>
                      </div>
                    </Link>

                    <Link 
                      to="/operations/dashboard"
                      className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-dark-700 hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                    >
                      <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                        <Home className="w-4 h-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Operations Board</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Operational management</div>
                      </div>
                    </Link>

                    <Link 
                      to="/board/dashboard"
                      className="flex items-center space-x-3 p-3 rounded-lg border border-gray-200 dark:border-dark-700 hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                    >
                      <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                        <Home className="w-4 h-4 text-purple-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">Decision Board</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">Strategic decisions</div>
                      </div>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* Error Details */}
              <Card className="mt-6">
                <CardHeader title="Error Details" />
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Error Code:</span>
                      <span className="font-mono text-gray-900 dark:text-white">404 Not Found</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Requested URL:</span>
                      <span className="font-mono text-gray-900 dark:text-white break-all">
                        {window.location.pathname}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">Timestamp:</span>
                      <span className="font-mono text-gray-900 dark:text-white">
                        {new Date().toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-gray-400">User Agent:</span>
                      <span className="font-mono text-gray-900 dark:text-white text-xs">
                        {navigator.userAgent.substring(0, 50)}...
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
};

export default NotFound;
