import React, { useState, useRef, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  CardContent,
  Button,
  Badge,
  Progress
} from '../components/ui';
import { 
  Send,
  Bot,
  User,
  Download,
  Share,
  Copy,
  RefreshCw,
  BarChart3,
  TrendingUp,
  Lightbulb,
  MessageSquare,
  Image as ImageIcon,
  Brain
} from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'error';
  graphics?: {
    type: 'chart' | 'table' | 'image';
    data?: Record<string, unknown>;
    url?: string;
  };
}

interface AIInsight {
  id: string;
  title: string;
  description: string;
  category: 'analysis' | 'prediction' | 'recommendation' | 'insight';
  confidence: number;
  timestamp: Date;
  data?: Record<string, unknown>;
}

const AiInsight: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Bonjour! Je suis votre assistant <PERSON><PERSON>. <PERSON> peux vous aider à analyser vos données, générer des insights, créer des graphiques et répondre à vos questions. Que souhaitez-vous explorer aujourd\'hui?',
      timestamp: new Date(),
      status: 'sent'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mock AI insights data
  useEffect(() => {
    const mockInsights: AIInsight[] = [
      {
        id: '1',
        title: 'Tendance de croissance des revenus',
        description: 'Analyse des tendances de revenus sur les 6 derniers mois',
        category: 'analysis',
        confidence: 0.92,
        timestamp: new Date(Date.now() - 3600000),
        data: { revenue: 2500000, growth: 0.125, period: '6 months' }
      },
      {
        id: '2',
        title: 'Prédiction de performance Q4',
        description: 'Projection des performances pour le quatrième trimestre',
        category: 'prediction',
        confidence: 0.87,
        timestamp: new Date(Date.now() - 7200000),
        data: { predictedRevenue: 2800000, confidence: 0.87 }
      },
      {
        id: '3',
        title: 'Optimisation des coûts',
        description: 'Recommandations pour réduire les coûts opérationnels',
        category: 'recommendation',
        confidence: 0.95,
        timestamp: new Date(Date.now() - 10800000),
        data: { potentialSavings: 150000, areas: ['maintenance', 'utilities'] }
      }
    ];
    setInsights(mockInsights);
  }, []);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isGenerating) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      status: 'sent'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsGenerating(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: generateAIResponse(inputValue),
        timestamp: new Date(),
        status: 'sent',
        graphics: shouldGenerateGraphics(inputValue) ? {
          type: 'chart',
          data: generateChartData(inputValue)
        } : undefined
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsGenerating(false);
    }, 2000);
  };

  const generateAIResponse = (query: string): string => {
    // Use the query to determine response type
    if (query.toLowerCase().includes('revenus') || query.toLowerCase().includes('revenue')) {
      return 'Basé sur l\'analyse de vos données, je peux voir une tendance positive dans les revenus.';
    } else if (query.toLowerCase().includes('graphique') || query.toLowerCase().includes('chart')) {
      return 'Voici le graphique généré basé sur vos données.';
    } else if (query.toLowerCase().includes('prédiction') || query.toLowerCase().includes('prediction')) {
      return 'L\'analyse prédictive suggère une amélioration significative dans les prochains mois.';
    } else if (query.toLowerCase().includes('recommandation') || query.toLowerCase().includes('optimisation')) {
      return 'Je recommande de surveiller ces métriques clés pour optimiser vos performances.';
    } else {
      const responses = [
        'Basé sur l\'analyse de vos données, je peux voir une tendance positive dans ce domaine.',
        'Voici mes recommandations basées sur les patterns identifiés dans vos données historiques.',
        'L\'analyse prédictive suggère une amélioration significative dans les prochains mois.',
        'Je recommande de surveiller ces métriques clés pour optimiser vos performances.',
        'D\'après l\'analyse des données, voici les insights les plus pertinents pour votre situation.'
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }
  };

  const shouldGenerateGraphics = (query: string): boolean => {
    const graphicsKeywords = ['graphique', 'chart', 'visualisation', 'trend', 'tendance', 'analyse', 'data'];
    return graphicsKeywords.some(keyword => query.toLowerCase().includes(keyword));
  };

  const generateChartData = (query: string) => {
    // Use query to determine chart type
    if (query.toLowerCase().includes('revenus') || query.toLowerCase().includes('revenue')) {
      return {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
        datasets: [
          {
            label: 'Revenus',
            data: [1200000, 1350000, 1420000, 1580000, 1650000, 1800000],
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)'
          }
        ]
      };
    } else if (query.toLowerCase().includes('performance')) {
      return {
        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
        datasets: [
          {
            label: 'Performance',
            data: [85, 92, 88, 95],
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)'
          }
        ]
      };
    } else {
      return {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
        datasets: [
          {
            label: 'Données',
            data: [1200000, 1350000, 1420000, 1580000, 1650000, 1800000],
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)'
          }
        ]
      };
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const downloadInsight = (insight: AIInsight) => {
    const dataStr = JSON.stringify(insight, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${insight.title.replace(/\s+/g, '_')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'analysis': return <BarChart3 className="w-4 h-4" />;
      case 'prediction': return <TrendingUp className="w-4 h-4" />;
      case 'recommendation': return <Lightbulb className="w-4 h-4" />;
      case 'insight': return <Brain className="w-4 h-4" />;
      default: return <MessageSquare className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'analysis': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'prediction': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'recommendation': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'insight': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-6">
        <button
          onClick={() => setActiveTab('chat')}
          className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
            activeTab === 'chat'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
          }`}
        >
          <MessageSquare className="w-4 h-4" />
          <span>Chat</span>
        </button>
        <button
          onClick={() => setActiveTab('insights')}
          className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
            activeTab === 'insights'
              ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
              : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
          }`}
        >
          <Brain className="w-4 h-4" />
          <span>Insights</span>
        </button>
      </div>

      {/* Chat Interface */}
      {activeTab === 'chat' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Chat Area */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] flex flex-col">
              <CardHeader title="Conversation IA" />
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                        <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            message.type === 'user' 
                              ? 'bg-primary-600' 
                              : 'bg-gray-600'
                          }`}>
                            {message.type === 'user' ? (
                              <User className="w-4 h-4 text-white" />
                            ) : (
                              <Bot className="w-4 h-4 text-white" />
                            )}
                          </div>
                          <div className={`flex-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                            <div className={`inline-block p-3 rounded-lg ${
                              message.type === 'user'
                                ? 'bg-primary-600 text-white'
                                : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                            }`}>
                              <p className="text-sm">{message.content}</p>
                              {message.graphics && (
                                <div className="mt-3 p-3 bg-white dark:bg-gray-700 rounded border">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <ImageIcon className="w-4 h-4 text-primary-600" />
                                    <span className="text-xs font-medium">Visualisation générée</span>
                                  </div>
                                  <div className="h-32 bg-gray-50 dark:bg-gray-600 rounded flex items-center justify-center">
                                    <BarChart3 className="w-8 h-8 text-gray-400" />
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2 mt-2">
                              <span className="text-xs text-gray-500">
                                {message.timestamp.toLocaleTimeString()}
                              </span>
                              {message.type === 'ai' && (
                                <div className="flex space-x-1">
                                  <button
                                    onClick={() => copyMessage(message.content)}
                                    className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                  >
                                    <Copy className="w-3 h-3" />
                                  </button>
                                  <button className="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                                    <Download className="w-3 h-3" />
                                  </button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {isGenerating && (
                    <div className="flex justify-start">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                          <div className="flex items-center space-x-2">
                            <RefreshCw className="w-4 h-4 animate-spin text-primary-600" />
                            <span className="text-sm text-gray-600 dark:text-gray-400">IA génère une réponse...</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex space-x-3">
                    <div className="flex-1">
                      <textarea
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Posez votre question à l'IA..."
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none"
                        rows={3}
                        disabled={isGenerating}
                      />
                    </div>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputValue.trim() || isGenerating}
                      variant="primary"
                      leftIcon={<Send className="w-4 h-4" />}
                    >
                      Envoyer
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* AI Capabilities */}
          <div className="space-y-6">
            <Card>
              <CardHeader title="Capacités IA" />
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 p-2 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium">Analyse de données</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 rounded-lg bg-green-50 dark:bg-green-900/20">
                    <TrendingUp className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium">Prédictions</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                    <Lightbulb className="w-5 h-5 text-yellow-600" />
                    <span className="text-sm font-medium">Recommandations</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 rounded-lg bg-purple-50 dark:bg-purple-900/20">
                    <ImageIcon className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium">Génération graphiques</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader title="Suggestions" />
              <CardContent>
                <div className="space-y-2">
                  <button
                    onClick={() => setInputValue("Analysez les tendances de revenus")}
                    className="w-full text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                  >
                    📈 Analyser les tendances de revenus
                  </button>
                  <button
                    onClick={() => setInputValue("Générez un graphique de performance")}
                    className="w-full text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                  >
                    📊 Générer un graphique de performance
                  </button>
                  <button
                    onClick={() => setInputValue("Quelles sont les prédictions pour Q4?")}
                    className="w-full text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                  >
                    🔮 Prédictions pour Q4
                  </button>
                  <button
                    onClick={() => setInputValue("Recommandations d'optimisation")}
                    className="w-full text-left p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                  >
                    💡 Recommandations d'optimisation
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Insights Tab */}
      {activeTab === 'insights' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {insights.map((insight) => (
              <Card key={insight.id} className="hover:shadow-lg transition-shadow">
                <CardHeader title={insight.title} />
                <CardContent>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      {getCategoryIcon(insight.category)}
                      <Badge 
                        variant="outline" 
                        size="sm"
                        className={getCategoryColor(insight.category)}
                      >
                        {insight.category}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {insight.timestamp.toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-400">
                        {insight.confidence * 100}% confiance
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {insight.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <Progress 
                      value={insight.confidence * 100} 
                      max={100} 
                      variant="primary" 
                      className="flex-1 mr-3"
                    />
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Download className="w-3 h-3" />}
                        onClick={() => downloadInsight(insight)}
                      >
                        Télécharger
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        leftIcon={<Share className="w-3 h-3" />}
                      >
                        Partager
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AiInsight;
