import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>ontent,
  <PERSON>ge,
  <PERSON>ton,
  Tabs,
  Alert,
  Progress,
  Dropdown,
  Input,
  Select
} from '../components/ui';
import {
  TrendChart,
  DistributionChart
} from '../components/analytics';
import { 
  CheckCircle, 
  XCircle, 
  Eye,
  Download,
  Settings,
  Plus,
  MoreVertical,
  Users,
  BarChart3,
  FileText,
  TrendingUp
} from 'lucide-react';

const BordDecision: React.FC = () => {
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for decisions
  const decisionsData = [
    { 
      id: 'dec-1', 
      title: 'Data Processing Pipeline Approval', 
      status: 'pending', 
      priority: 'high',
      requester: '<PERSON>',
      department: 'Analytics',
      createdAt: '2024-01-15T10:30:00Z',
      deadline: '2024-01-20T17:00:00Z',
      description: 'Request for approval of new data processing pipeline for real estate analytics.'
    },
    { 
      id: 'dec-2', 
      title: 'System Upgrade Authorization', 
      status: 'approved', 
      priority: 'medium',
      requester: '<PERSON>',
      department: 'IT',
      createdAt: '2024-01-14T14:20:00Z',
      approvedAt: '2024-01-16T09:15:00Z',
      description: 'Authorization for system upgrade to improve performance and security.'
    },
    { 
      id: 'dec-3', 
      title: 'Budget Allocation for Q1', 
      status: 'rejected', 
      priority: 'high',
      requester: 'Mike Wilson',
      department: 'Finance',
      createdAt: '2024-01-13T16:45:00Z',
      rejectedAt: '2024-01-15T11:30:00Z',
      description: 'Budget allocation request for Q1 analytics projects and infrastructure.'
    },
    { 
      id: 'dec-4', 
      title: 'New Data Source Integration', 
      status: 'pending', 
      priority: 'low',
      requester: 'Lisa Chen',
      department: 'Data Science',
      createdAt: '2024-01-16T08:15:00Z',
      deadline: '2024-01-25T17:00:00Z',
      description: 'Integration of new external data source for enhanced analytics capabilities.'
    },
  ];

  const decisionTrends = [
    { time: 'Jan', approved: 12, rejected: 3, pending: 8 },
    { time: 'Feb', approved: 15, rejected: 2, pending: 6 },
    { time: 'Mar', approved: 18, rejected: 4, pending: 10 },
    { time: 'Apr', approved: 14, rejected: 1, pending: 7 },
    { time: 'May', approved: 20, rejected: 3, pending: 5 },
    { time: 'Jun', approved: 16, rejected: 2, pending: 9 },
  ];

  const decisionDistribution = [
    { name: 'Approved', value: 65, color: '#10b981' },
    { name: 'Rejected', value: 15, color: '#ef4444' },
    { name: 'Pending', value: 20, color: '#f59e0b' },
  ];

  const decisionTabs = [
    {
      id: 'overview',
      label: 'Decision Overview',
      icon: <BarChart3 className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card variant="metric">
              <CardHeader title="Pending Decisions" />
              <CardContent>
                <div className="text-3xl font-bold text-orange-600">15</div>
                <div className="text-sm text-gray-600">Awaiting approval</div>
              </CardContent>
            </Card>
            
            <Card variant="metric">
              <CardHeader title="Approved Today" />
              <CardContent>
                <div className="text-3xl font-bold text-green-600">8</div>
                <div className="text-sm text-gray-600">Successfully approved</div>
              </CardContent>
            </Card>
            
            <Card variant="metric">
              <CardHeader title="Rejected This Week" />
              <CardContent>
                <div className="text-3xl font-bold text-red-600">3</div>
                <div className="text-sm text-gray-600">Rejected decisions</div>
              </CardContent>
            </Card>
            
            <Card variant="metric">
              <CardHeader title="Avg Response Time" />
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">2.4h</div>
                <div className="text-sm text-gray-600">Average approval time</div>
              </CardContent>
            </Card>
          </div>

          {/* Decision Trends */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader 
                title="Decision Trends"
                subtitle="Last 6 months"
              />
              <CardContent>
                <TrendChart
                  data={decisionTrends}
                  lines={[
                    { dataKey: 'approved', name: 'Approved', color: '#10b981' },
                    { dataKey: 'rejected', name: 'Rejected', color: '#ef4444' },
                    { dataKey: 'pending', name: 'Pending', color: '#f59e0b' }
                  ]}
                  height={300}
                  showLegend
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader 
                title="Decision Distribution"
                subtitle="Current status breakdown"
              />
              <CardContent>
                <DistributionChart
                  data={decisionDistribution}
                  showLabels
                  showLegend
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )
    },
    {
      id: 'decisions',
      label: 'Active Decisions',
      icon: <FileText className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          {/* Filters */}
          <Card>
            <CardHeader title="Filters" />
            <CardContent>
              <div className="flex space-x-4">
                <Select
                  value={filterStatus}
                  onValueChange={(value) => setFilterStatus(value as string)}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'pending', label: 'Pending' },
                    { value: 'approved', label: 'Approved' },
                    { value: 'rejected', label: 'Rejected' }
                  ]}
                  placeholder="Filter by status"
                  className="w-48"
                />
                <Input
                  type="search"
                  placeholder="Search decisions..."
                  className="w-64"
                />
                <Button variant="outline">
                  Clear Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Decisions List */}
          <Card>
            <CardHeader 
              title="Recent Decisions"
              action={
                <Button variant="primary" size="sm" leftIcon={<Plus className="w-4 h-4" />}>
                  New Decision
                </Button>
              }
            />
            <CardContent>
              <div className="space-y-4">
                {decisionsData.map((decision) => (
                  <div key={decision.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-medium text-gray-900">{decision.title}</h4>
                          <Badge 
                            variant={
                              decision.status === 'approved' ? 'success' : 
                              decision.status === 'rejected' ? 'error' : 'warning'
                            }
                          >
                            {decision.status}
                          </Badge>
                          <Badge 
                            variant={decision.priority === 'high' ? 'error' : 'secondary'}
                            size="sm"
                          >
                            {decision.priority}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-3">{decision.description}</p>
                        
                        <div className="flex items-center space-x-6 text-xs text-gray-500">
                          <span>Requester: {decision.requester}</span>
                          <span>Department: {decision.department}</span>
                          <span>Created: {new Date(decision.createdAt).toLocaleDateString()}</span>
                          {decision.deadline && (
                            <span>Deadline: {new Date(decision.deadline).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                          View
                        </Button>
                        <Dropdown
                          items={[
                            { id: 'approve', label: 'Approve', icon: <CheckCircle className="w-4 h-4" /> },
                            { id: 'reject', label: 'Reject', icon: <XCircle className="w-4 h-4" /> },
                            { id: 'details', label: 'View Details', icon: <Eye className="w-4 h-4" /> },
                            { id: 'export', label: 'Export', icon: <Download className="w-4 h-4" /> },
                          ]}
                          trigger="click"
                        >
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </Dropdown>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )
    },
    {
      id: 'analytics',
      label: 'Decision Analytics',
      icon: <TrendingUp className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader title="Approval Rate" />
              <CardContent>
                <div className="text-center">
                  <div className="text-4xl font-bold text-green-600 mb-2">81%</div>
                  <div className="text-sm text-gray-600">Overall approval rate</div>
                  <div className="mt-4">
                    <Progress value={81} max={100} variant="success" size="sm" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader title="Avg Response Time" />
              <CardContent>
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-600 mb-2">2.4h</div>
                  <div className="text-sm text-gray-600">Average response time</div>
                  <div className="mt-4 text-xs text-gray-500">
                    Target: less than 4 hours
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader title="Decision Volume" />
              <CardContent>
                <div className="text-center">
                  <div className="text-4xl font-bold text-purple-600 mb-2">156</div>
                  <div className="text-sm text-gray-600">Decisions this month</div>
                  <div className="mt-4 text-xs text-green-600">
                    <TrendingUp className="w-3 h-3 inline mr-1" />
                    +12% vs last month
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader title="Department Performance" />
            <CardContent>
              <div className="space-y-4">
                {[
                  { dept: 'Analytics', approved: 25, total: 30, rate: 83 },
                  { dept: 'IT', approved: 18, total: 22, rate: 82 },
                  { dept: 'Finance', approved: 12, total: 15, rate: 80 },
                  { dept: 'Data Science', approved: 20, total: 25, rate: 80 },
                ].map((dept, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{dept.dept}</h4>
                      <p className="text-sm text-gray-600">{dept.approved}/{dept.total} approved</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-green-600">{dept.rate}%</div>
                      <Progress value={dept.rate} max={100} variant="success" size="sm" className="w-20" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Board Decision</h1>
          <p className="text-gray-600">Decision management and approval workflow</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            leftIcon={<Settings className="w-4 h-4" />}
          >
            Settings
          </Button>
          <Button 
            variant="primary" 
            leftIcon={<Plus className="w-4 h-4" />}
          >
            New Decision
          </Button>
        </div>
      </div>

      {/* Status Alert */}
      <Alert variant="info" title="System Status" description="Decision workflow is active. 15 pending decisions require attention." />

      {/* Main Content */}
      <Tabs
        items={decisionTabs}
        variant="detection"
        size="lg"
        className="bg-white rounded-lg shadow-sm"
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<CheckCircle className="w-4 h-4" />} fullWidth>
              Approve All
            </Button>
            <Button variant="outline" leftIcon={<XCircle className="w-4 h-4" />} fullWidth>
              Reject All
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export Report
            </Button>
            <Button variant="outline" leftIcon={<Users className="w-4 h-4" />} fullWidth>
              Manage Users
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BordDecision;
