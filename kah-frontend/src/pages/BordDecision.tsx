import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  Button
} from '../components/ui';
import { 
  Bell
} from 'lucide-react';
import BaseInterface from '../components/layout/BaseInterface';
import BoardDashboard from './DecisionBoard/BoardDashboard';
import Kpi from './DecisionBoard/Kpi';
import RentabilityAnalysis from './DecisionBoard/RentabilityAnalysis';
import Forecasting from './DecisionBoard/Forecasting';
import Benchmark from './DecisionBoard/Benchmark';
import Report from './DecisionBoard/Report';
import AiInsight from './AiInsight';

const BordDecision: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Determine which component to show based on the current route
  const getCurrentComponent = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/board') {
      return <BoardDashboard />;
    } else if (path.includes('/kpi')) {
      return <Kpi />;
    } else if (path.includes('/rentability')) {
      return <RentabilityAnalysis />;
    } else if (path.includes('/forecasting')) {
      return <Forecasting />;
    } else if (path.includes('/benchmark')) {
      return <Benchmark />;
    } else if (path.includes('/report')) {
      return <Report />;
    } else if (path.includes('/ai_insight')) {
      return <AiInsight />;
    } else {
      // Default to dashboard view
      return <BoardDashboard />;
    }
  };

  // Get current page title based on route
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/board') {
      return 'Tableau de Bord Décisionnel';
    } else if (path.includes('/kpi')) {
      return 'Indicateurs de Performance';
    } else if (path.includes('/rentability')) {
      return 'Analyse de Rentabilité';
    } else if (path.includes('/forecasting')) {
      return 'Projections et Prévisions';
    } else if (path.includes('/benchmark')) {
      return 'Benchmark et Comparaisons';
    } else if (path.includes('/report')) {
      return 'Rapports Décisionnels';
    } else if (path.includes('/ai_insight')) {
      return 'Insights IA';
    } else {
      return 'Tableau de Bord Décisionnel';
    }
  };

  const getPageDescription = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/board') {
      return 'Vue d\'ensemble des indicateurs clés et métriques de performance';
    } else if (path.includes('/kpi')) {
      return 'Suivi des indicateurs de performance clés';
    } else if (path.includes('/rentability')) {
      return 'Analyse de la rentabilité et des marges';
    } else if (path.includes('/forecasting')) {
      return 'Projections et prévisions basées sur les données historiques';
    } else if (path.includes('/benchmark')) {
      return 'Comparaisons avec les standards du marché';
    } else if (path.includes('/report')) {
      return 'Rapports détaillés pour la prise de décision';
    } else if (path.includes('/ai_insight')) {
      return 'Analyse et insights basés sur les données pour prendre des décisions éclairées.';
    } else {
      return 'Vue d\'ensemble des indicateurs clés et métriques de performance';
    }
  };

  // Action buttons for the page header
  const actionButtons = (
    <>
      <Button 
        variant="primary" 
        leftIcon={<Bell className="w-4 h-4" />}
        className="bg-white text-primary-600 hover:bg-gray-100"
        onClick={() => navigate('/notifications')}
      >
        Notifications
      </Button>
    </>
  );

  return (
    <BaseInterface
      getCurrentComponent={getCurrentComponent}
      getPageTitle={getPageTitle}
      getPageDescription={getPageDescription}
      actionButtons={actionButtons}
    />
  );
};

export default BordDecision;
