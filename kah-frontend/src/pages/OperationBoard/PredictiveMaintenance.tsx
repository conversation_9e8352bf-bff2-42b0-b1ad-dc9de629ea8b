import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON><PERSON>nt,
  <PERSON>ge,
  Button,
  Progress,
  Alert
} from '../../components/ui';
import { 
  Wrench,
  AlertTriangle,
  Clock,
  Activity,
  Calendar
} from 'lucide-react';

const PredictiveMaintenance: React.FC = () => {
  // Mock data for predictive maintenance
  const equipment = [
    {
      id: 'eq-1',
      name: 'Machine de Production A',
      type: 'Production',
      status: 'warning',
      healthScore: 65,
      lastMaintenance: '2024-01-10',
      nextMaintenance: '2024-02-15',
      daysUntilMaintenance: 15,
      criticalIssues: 2,
      warnings: 3,
      location: 'Zone A'
    },
    {
      id: 'eq-2',
      name: 'Système de Refroidissement B',
      type: 'Climatisation',
      status: 'critical',
      healthScore: 35,
      lastMaintenance: '2024-01-05',
      nextMaintenance: '2024-01-30',
      daysUntilMaintenance: 5,
      criticalIssues: 4,
      warnings: 1,
      location: 'Zone B'
    },
    {
      id: 'eq-3',
      name: '<PERSON>mpresseur C',
      type: 'Pneumatique',
      status: 'normal',
      healthScore: 85,
      lastMaintenance: '2024-01-15',
      nextMaintenance: '2024-03-10',
      daysUntilMaintenance: 45,
      criticalIssues: 0,
      warnings: 1,
      location: 'Zone C'
    },
    {
      id: 'eq-4',
      name: 'Générateur D',
      type: 'Électrique',
      status: 'warning',
      healthScore: 70,
      lastMaintenance: '2024-01-12',
      nextMaintenance: '2024-02-20',
      daysUntilMaintenance: 20,
      criticalIssues: 1,
      warnings: 2,
      location: 'Zone D'
    }
  ];

  const maintenanceSchedule = [
    {
      id: 'maint-1',
      equipment: 'Machine de Production A',
      type: 'Préventive',
      priority: 'high',
      scheduledDate: '2024-02-15',
      estimatedDuration: '4h',
      technician: 'Pierre Martin',
      parts: ['Filtre A', 'Joint B'],
      status: 'scheduled'
    },
    {
      id: 'maint-2',
      equipment: 'Système de Refroidissement B',
      type: 'Corrective',
      priority: 'urgent',
      scheduledDate: '2024-01-30',
      estimatedDuration: '6h',
      technician: 'Marie Dubois',
      parts: ['Compresseur', 'Thermostat'],
      status: 'urgent'
    },
    {
      id: 'maint-3',
      equipment: 'Compresseur C',
      type: 'Préventive',
      priority: 'medium',
      scheduledDate: '2024-03-10',
      estimatedDuration: '2h',
      technician: 'Jean Bernard',
      parts: ['Filtre à air'],
      status: 'planned'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'success';
      case 'warning': return 'warning';
      case 'critical': return 'error';
      default: return 'secondary';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'primary';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const criticalEquipment = equipment.filter(eq => eq.status === 'critical').length;
  const warningEquipment = equipment.filter(eq => eq.status === 'warning').length;
  const scheduledMaintenance = maintenanceSchedule.filter(m => m.status === 'scheduled').length;
  const urgentMaintenance = maintenanceSchedule.filter(m => m.status === 'urgent').length;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="metric">
          <CardHeader title="Équipements Critiques" />
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {criticalEquipment}
            </div>
            <div className="text-sm text-gray-600">nécessitent attention</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Équipements en Alerte" />
          <CardContent>
            <div className="text-3xl font-bold text-orange-600">
              {warningEquipment}
            </div>
            <div className="text-sm text-gray-600">surveillance requise</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Maintenance Planifiée" />
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {scheduledMaintenance}
            </div>
            <div className="text-sm text-gray-600">interventions programmées</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Maintenance Urgente" />
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {urgentMaintenance}
            </div>
            <div className="text-sm text-gray-600">interventions urgentes</div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {criticalEquipment > 0 && (
        <Alert variant="error">
          <AlertTriangle className="w-4 h-4" />
          <span>{criticalEquipment} équipement(s) critique(s) détecté(s). Intervention immédiate requise.</span>
        </Alert>
      )}

      {warningEquipment > 0 && (
        <Alert variant="warning">
          <Clock className="w-4 h-4" />
          <span>{warningEquipment} équipement(s) en alerte. Planifier la maintenance préventive.</span>
        </Alert>
      )}

      {/* Equipment Health */}
      <Card>
        <CardHeader title="État des Équipements" />
        <CardContent>
          <div className="space-y-4">
            {equipment.map((eq) => (
              <div key={eq.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{eq.name}</h3>
                      <Badge variant={getStatusColor(eq.status)}>
                        {eq.status === 'normal' ? 'Normal' :
                         eq.status === 'warning' ? 'Alerte' :
                         eq.status === 'critical' ? 'Critique' : eq.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Type: {eq.type}</span>
                      <span>•</span>
                      <span>Localisation: {eq.location}</span>
                      <span>•</span>
                      <span>Dernière maintenance: {new Date(eq.lastMaintenance).toLocaleDateString('fr-FR')}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      Détails
                    </Button>
                    <Button variant="outline" size="sm">
                      Planifier
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {/* Health Score */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Score de Santé</span>
                      <span className="text-sm text-gray-600">{eq.healthScore}%</span>
                    </div>
                    <Progress 
                      value={eq.healthScore} 
                      max={100} 
                      size="sm"
                      variant={getHealthColor(eq.healthScore) as 'success' | 'warning' | 'error'}
                    />
                  </div>

                  {/* Next Maintenance */}
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span className="text-sm font-medium text-gray-700">Prochaine Maintenance</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {new Date(eq.nextMaintenance).toLocaleDateString('fr-FR')}
                    </div>
                    <div className="text-xs text-gray-500">
                      {eq.daysUntilMaintenance} jour(s) restant(s)
                    </div>
                  </div>

                  {/* Issues */}
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-2">Problèmes</div>
                    <div className="space-y-1">
                      {eq.criticalIssues > 0 && (
                        <div className="flex items-center space-x-1 text-xs text-red-600">
                          <AlertTriangle className="w-3 h-3" />
                          <span>{eq.criticalIssues} critique(s)</span>
                        </div>
                      )}
                      {eq.warnings > 0 && (
                        <div className="flex items-center space-x-1 text-xs text-orange-600">
                          <Clock className="w-3 h-3" />
                          <span>{eq.warnings} alerte(s)</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-2">Actions</div>
                    <div className="space-y-1">
                      {eq.status === 'critical' && (
                        <Button variant="error" size="sm" className="w-full">
                          Intervention Urgente
                        </Button>
                      )}
                      {eq.status === 'warning' && (
                        <Button variant="warning" size="sm" className="w-full">
                          Planifier Maintenance
                        </Button>
                      )}
                      {eq.status === 'normal' && (
                        <Button variant="outline" size="sm" className="w-full">
                          Vérifier
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Schedule */}
      <Card>
        <CardHeader title="Planning de Maintenance" />
        <CardContent>
          <div className="space-y-4">
            {maintenanceSchedule.map((maint) => (
              <div key={maint.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{maint.equipment}</h3>
                      <Badge variant={getPriorityColor(maint.priority)}>
                        {maint.priority === 'urgent' ? 'Urgent' :
                         maint.priority === 'high' ? 'Élevée' :
                         maint.priority === 'medium' ? 'Moyenne' :
                         maint.priority === 'low' ? 'Faible' : maint.priority}
                      </Badge>
                      <Badge variant={maint.type === 'Préventive' ? 'success' : 'warning'}>
                        {maint.type}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Date: {new Date(maint.scheduledDate).toLocaleDateString('fr-FR')}</span>
                      <span>•</span>
                      <span>Durée: {maint.estimatedDuration}</span>
                      <span>•</span>
                      <span>Technicien: {maint.technician}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">
                      Détails
                    </Button>
                    <Button variant="outline" size="sm">
                      Modifier
                    </Button>
                  </div>
                </div>

                {/* Parts Required */}
                <div className="mt-3">
                  <div className="text-sm font-medium text-gray-700 mb-2">Pièces Requises</div>
                  <div className="flex flex-wrap gap-2">
                    {maint.parts.map((part, index) => (
                      <Badge key={index} variant="secondary" size="sm">
                        {part}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Actions Rapides" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" fullWidth leftIcon={<Wrench className="w-4 h-4" />}>
              Nouvelle Intervention
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Calendar className="w-4 h-4" />}>
              Planifier Maintenance
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Activity className="w-4 h-4" />}>
              Surveillance
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Wrench className="w-4 h-4" />}>
              Inventaire Pièces
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PredictiveMaintenance;
