import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON><PERSON>nt,
  <PERSON><PERSON>,
  But<PERSON>,
  Progress
} from '../../components/ui';
import { 
  Download,
  FileText,
  BarChart3,
  TrendingUp,
  Calendar,
  Eye,
  Filter,
  Share
} from 'lucide-react';

const OperationReport: React.FC = () => {
  // Mock data for operation reports
  const reportTypes = [
    {
      id: 'daily',
      name: 'Rapport Quotidien',
      description: 'Résumé des activités du jour',
      lastGenerated: '2024-01-15T08:00:00Z',
      status: 'ready',
      size: '2.3 MB'
    },
    {
      id: 'weekly',
      name: 'Rapport Hebdomadaire',
      description: 'Synthèse des performances de la semaine',
      lastGenerated: '2024-01-12T18:00:00Z',
      status: 'ready',
      size: '8.7 MB'
    },
    {
      id: 'monthly',
      name: 'Rapport Mensuel',
      description: 'Analyse complète des métriques mensuelles',
      lastGenerated: '2024-01-01T00:00:00Z',
      status: 'ready',
      size: '15.2 MB'
    },
    {
      id: 'quarterly',
      name: 'Rapport Trimestriel',
      description: 'Évaluation stratégique trimestrielle',
      lastGenerated: '2023-12-31T23:59:00Z',
      status: 'generating',
      size: '32.1 MB'
    }
  ];

  const recentReports = [
    {
      id: 1,
      title: 'Rapport Opérationnel - Semaine 2',
      type: 'weekly',
      generatedAt: '2024-01-12T18:00:00Z',
      author: 'Système Automatique',
      downloads: 24,
      status: 'published'
    },
    {
      id: 2,
      title: 'Analyse Performance Commerciale - Janvier',
      type: 'monthly',
      generatedAt: '2024-01-01T00:00:00Z',
      author: 'Marie Dubois',
      downloads: 18,
      status: 'published'
    },
    {
      id: 3,
      title: 'Rapport Maintenance Prédictive - Q4 2023',
      type: 'quarterly',
      generatedAt: '2023-12-31T23:59:00Z',
      author: 'Jean Martin',
      downloads: 12,
      status: 'draft'
    }
  ];

  const reportMetrics = [
    {
      metric: 'Rapports Générés',
      value: 156,
      change: '+12%',
      trend: 'up'
    },
    {
      metric: 'Téléchargements',
      value: 892,
      change: '+8%',
      trend: 'up'
    },
    {
      metric: 'Temps Moyen de Génération',
      value: '2.3 min',
      change: '-15%',
      trend: 'down'
    },
    {
      metric: 'Taux de Satisfaction',
      value: '94%',
      change: '+2%',
      trend: 'up'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Report Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {reportMetrics.map((metric, index) => (
          <Card key={index} variant="metric">
            <CardHeader title={metric.metric} />
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {metric.value}
                </div>
                <Badge variant="success">{metric.change}</Badge>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                vs période précédente
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Report Types */}
      <Card>
        <CardHeader 
          title="Types de Rapports"
          subtitle="Générez et téléchargez différents types de rapports opérationnels"
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reportTypes.map((report) => (
              <div key={report.id} className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{report.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{report.description}</p>
                  </div>
                  <Badge 
                    variant={report.status === 'ready' ? 'success' : 'warning'}
                    size="sm"
                  >
                    {report.status === 'ready' ? 'Prêt' : 'En cours'}
                  </Badge>
                </div>
                
                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>Dernière génération:</span>
                    <span>{new Date(report.lastGenerated).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>Taille:</span>
                    <span>{report.size}</span>
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    leftIcon={<Download className="w-4 h-4" />}
                    disabled={report.status !== 'ready'}
                  >
                    Télécharger
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    leftIcon={<Eye className="w-4 h-4" />}
                  >
                    Aperçu
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reports */}
      <Card>
        <CardHeader 
          title="Rapports Récents"
          subtitle="Historique des rapports générés"
          action={
            <Button variant="outline" size="sm" leftIcon={<Filter className="w-4 h-4" />}>
              Filtrer
            </Button>
          }
        />
        <CardContent>
          <div className="space-y-4">
            {recentReports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <FileText className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{report.title}</h4>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <span>{report.type}</span>
                      <span>•</span>
                      <span>{new Date(report.generatedAt).toLocaleDateString()}</span>
                      <span>•</span>
                      <span>{report.author}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {report.downloads} téléchargements
                    </div>
                    <Badge 
                      variant={report.status === 'published' ? 'success' : 'warning'}
                      size="sm"
                    >
                      {report.status === 'published' ? 'Publié' : 'Brouillon'}
                    </Badge>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                      Télécharger
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Share className="w-4 h-4" />}>
                      Partager
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Report Generation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader title="Génération de Rapports" />
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center space-x-3 mb-3">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                  <h4 className="font-medium">Rapport Quotidien</h4>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progression</span>
                    <span>75%</span>
                  </div>
                  <Progress value={75} max={100} variant="primary" />
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Génération en cours... Terminé dans 2 minutes
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <Button variant="primary" leftIcon={<FileText className="w-4 h-4" />} fullWidth>
                  Générer Nouveau Rapport
                </Button>
                <Button variant="outline" leftIcon={<Calendar className="w-4 h-4" />} fullWidth>
                  Planifier Génération
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="Statistiques Rapides" />
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <div>
                    <div className="font-medium">Rapports ce mois</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">+15% vs mois dernier</div>
                  </div>
                </div>
                <div className="text-2xl font-bold text-green-600">42</div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Download className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Téléchargements</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Cette semaine</div>
                  </div>
                </div>
                <div className="text-2xl font-bold text-blue-600">156</div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Eye className="w-5 h-5 text-purple-600" />
                  <div>
                    <div className="font-medium">Consultations</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Aujourd'hui</div>
                  </div>
                </div>
                <div className="text-2xl font-bold text-purple-600">23</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Actions Rapides" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<FileText className="w-4 h-4" />} fullWidth>
              Nouveau Rapport
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Exporter Tout
            </Button>
            <Button variant="outline" leftIcon={<Share className="w-4 h-4" />} fullWidth>
              Partager
            </Button>
            <Button variant="outline" leftIcon={<Calendar className="w-4 h-4" />} fullWidth>
              Planifier
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OperationReport;
