import React from 'react';
import { 
  Card, 
  CardHeader, 
  CardContent,
  Badge,
  Button,
  Progress,
  Alert
} from '../../components/ui';
import { 
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  Plus,
  Eye,
  Download
} from 'lucide-react';

const StockManagement: React.FC = () => {
  // Mock data for stock items
  const stockItems = [
    {
      id: 'item-1',
      name: 'Composant A',
      category: 'Électronique',
      currentStock: 45,
      minStock: 50,
      maxStock: 200,
      unit: 'pièces',
      unitPrice: 12.50,
      supplier: 'Fournisseur ABC',
      lastRestock: '2024-01-15',
      consumption: 15,
      status: 'low'
    },
    {
      id: 'item-2',
      name: 'Matériel B',
      category: 'Mécanique',
      currentStock: 0,
      minStock: 10,
      maxStock: 100,
      unit: 'unités',
      unitPrice: 25.00,
      supplier: 'Fournisseur XYZ',
      lastRestock: '2024-01-10',
      consumption: 8,
      status: 'out'
    },
    {
      id: 'item-3',
      name: 'Produit C',
      category: 'Consommables',
      currentStock: 150,
      minStock: 20,
      maxStock: 300,
      unit: 'boîtes',
      unitPrice: 8.75,
      supplier: 'Fournisseur DEF',
      lastRestock: '2024-01-20',
      consumption: 5,
      status: 'normal'
    },
    {
      id: 'item-4',
      name: 'Composant D',
      category: 'Électronique',
      currentStock: 12,
      minStock: 15,
      maxStock: 80,
      unit: 'pièces',
      unitPrice: 45.00,
      supplier: 'Fournisseur GHI',
      lastRestock: '2024-01-18',
      consumption: 12,
      status: 'low'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'success';
      case 'low': return 'warning';
      case 'out': return 'error';
      default: return 'secondary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal': return 'Normal';
      case 'low': return 'Stock Faible';
      case 'out': return 'Rupture';
      default: return status;
    }
  };

  const totalValue = stockItems.reduce((sum, item) => sum + (item.currentStock * item.unitPrice), 0);
  const lowStockCount = stockItems.filter(item => item.status === 'low').length;
  const outOfStockCount = stockItems.filter(item => item.status === 'out').length;

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="metric">
          <CardHeader title="Valeur Totale" />
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {totalValue.toLocaleString()}€
            </div>
            <div className="text-sm text-gray-600">valeur du stock</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Articles en Stock" />
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {stockItems.length}
            </div>
            <div className="text-sm text-gray-600">références</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Stock Faible" />
          <CardContent>
            <div className="text-3xl font-bold text-orange-600">
              {lowStockCount}
            </div>
            <div className="text-sm text-gray-600">nécessitent réappro</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Ruptures" />
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {outOfStockCount}
            </div>
            <div className="text-sm text-gray-600">articles épuisés</div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {(lowStockCount > 0 || outOfStockCount > 0) && (
        <div className="space-y-3">
          {outOfStockCount > 0 && (
            <Alert variant="error">
              <AlertTriangle className="w-4 h-4" />
              <span>{outOfStockCount} article(s) en rupture de stock. Réapprovisionnement urgent requis.</span>
            </Alert>
          )}
          {lowStockCount > 0 && (
            <Alert variant="warning">
              <TrendingDown className="w-4 h-4" />
              <span>{lowStockCount} article(s) avec stock faible. Planifier le réapprovisionnement.</span>
            </Alert>
          )}
        </div>
      )}

      {/* Stock Items Table */}
      <Card>
        <CardHeader title="Inventaire Détaillé" />
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Article</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Catégorie</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Stock</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Consommation</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Valeur</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Statut</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {stockItems.map((item) => (
                  <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{item.name}</div>
                        <div className="text-sm text-gray-500">{item.supplier}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge variant="secondary" size="sm">
                        {item.category}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">{item.currentStock}</span>
                          <span className="text-xs text-gray-500">/ {item.maxStock} {item.unit}</span>
                        </div>
                        <div className="w-24">
                          <Progress 
                            value={(item.currentStock / item.maxStock) * 100} 
                            max={100} 
                            size="sm"
                            variant={item.status === 'out' ? 'error' : item.status === 'low' ? 'warning' : 'primary'}
                          />
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-1">
                        <TrendingDown className="w-4 h-4 text-gray-400" />
                        <span className="text-sm">{item.consumption} {item.unit}/mois</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm font-medium">
                        {(item.currentStock * item.unitPrice).toLocaleString()}€
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.unitPrice}€/{item.unit}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge variant={getStatusColor(item.status)}>
                        {getStatusText(item.status)}
                      </Badge>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Actions Rapides" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" fullWidth leftIcon={<Plus className="w-4 h-4" />}>
              Réapprovisionner
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Download className="w-4 h-4" />}>
              Inventaire
            </Button>
            <Button variant="outline" fullWidth leftIcon={<TrendingUp className="w-4 h-4" />}>
              Consommation
            </Button>
            <Button variant="outline" fullWidth leftIcon={<AlertTriangle className="w-4 h-4" />}>
              Alertes
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StockManagement;
