import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>nt,
  <PERSON>ge,
  Button,
  Progress,
  Alert
} from '../../components/ui';
import { 
  Calendar,
  DollarSign,
  AlertTriangle,
  Users
} from 'lucide-react';

const ProjectCheck: React.FC = () => {
  // Mock data for projects
  const projects = [
    {
      id: 'proj-1',
      name: 'Projet Alpha',
      status: 'active',
      progress: 75,
      deadline: '2024-02-15',
      budget: 150000,
      spent: 112500,
      team: ['<PERSON>', '<PERSON>', '<PERSON>'],
      description: 'Développement application mobile'
    },
    {
      id: 'proj-2',
      name: 'Projet Beta',
      status: 'delayed',
      progress: 45,
      deadline: '2024-01-30',
      budget: 80000,
      spent: 72000,
      team: ['<PERSON>', '<PERSON>'],
      description: 'Refonte site web'
    },
    {
      id: 'proj-3',
      name: 'Projet Gamma',
      status: 'completed',
      progress: 100,
      deadline: '2024-01-20',
      budget: 120000,
      spent: 118000,
      team: ['<PERSON>', '<PERSON>'],
      description: '<PERSON><PERSON><PERSON><PERSON> de gestion'
    },
    {
      id: 'proj-4',
      name: 'Projet Delta',
      status: 'active',
      progress: 30,
      deadline: '2024-03-10',
      budget: 200000,
      spent: 60000,
      team: ['<PERSON>', '<PERSON> <PERSON>', '<PERSON> Dubois'],
      description: 'Plateforme e-commerce'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'delayed': return 'error';
      case 'completed': return 'primary';
      default: return 'secondary';
    }
  };

  const getDaysUntilDeadline = (deadline: string) => {
    const today = new Date();
    const deadlineDate = new Date(deadline);
    const diffTime = deadlineDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getBudgetStatus = (spent: number, budget: number) => {
    const percentage = (spent / budget) * 100;
    if (percentage > 90) return 'critical';
    if (percentage > 75) return 'warning';
    return 'success';
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="metric">
          <CardHeader title="Projets Actifs" />
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {projects.filter(p => p.status === 'active').length}
            </div>
            <div className="text-sm text-gray-600">en cours</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Projets en Retard" />
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {projects.filter(p => p.status === 'delayed').length}
            </div>
            <div className="text-sm text-gray-600">nécessitent attention</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Budget Total" />
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {projects.reduce((sum, p) => sum + p.budget, 0).toLocaleString()}€
            </div>
            <div className="text-sm text-gray-600">budget alloué</div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Taux de Réussite" />
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">
              {Math.round((projects.filter(p => p.status === 'completed').length / projects.length) * 100)}%
            </div>
            <div className="text-sm text-gray-600">projets terminés</div>
          </CardContent>
        </Card>
      </div>

      {/* Projects List */}
      <Card>
        <CardHeader title="Détail des Projets" />
        <CardContent>
          <div className="space-y-6">
            {projects.map((project) => {
              const daysUntilDeadline = getDaysUntilDeadline(project.deadline);
              const budgetStatus = getBudgetStatus(project.spent, project.budget);
              
              return (
                <div key={project.id} className="border rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
                        <Badge variant={getStatusColor(project.status)}>
                          {project.status === 'active' ? 'Actif' :
                           project.status === 'delayed' ? 'En Retard' :
                           project.status === 'completed' ? 'Terminé' : project.status}
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-3">{project.description}</p>
                      
                      {/* Team */}
                      <div className="flex items-center space-x-2 mb-3">
                        <Users className="w-4 h-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {project.team.length} membre(s): {project.team.join(', ')}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex space-x-3">
                      <Button variant="outline" size="sm">
                        Détails
                      </Button>
                      <Button variant="outline" size="sm">
                        Modifier
                      </Button>
                    </div>
                  </div>

                  {/* Progress and Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Progress */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">Progression</span>
                        <span className="text-sm text-gray-600">{project.progress}%</span>
                      </div>
                      <Progress value={project.progress} max={100} size="sm" />
                    </div>

                    {/* Deadline */}
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="text-sm font-medium text-gray-700">Échéance</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">
                          {new Date(project.deadline).toLocaleDateString('fr-FR')}
                        </span>
                        {daysUntilDeadline < 0 && (
                          <Badge variant="error" size="sm">
                            {Math.abs(daysUntilDeadline)}j de retard
                          </Badge>
                        )}
                        {daysUntilDeadline >= 0 && daysUntilDeadline <= 7 && (
                          <Badge variant="warning" size="sm">
                            {daysUntilDeadline}j restant(s)
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Budget */}
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <DollarSign className="w-4 h-4 text-gray-400" />
                        <span className="text-sm font-medium text-gray-700">Budget</span>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>Dépensé: {project.spent.toLocaleString()}€</span>
                          <span>Budget: {project.budget.toLocaleString()}€</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Reste: {(project.budget - project.spent).toLocaleString()}€</span>
                          <span className={`${
                            budgetStatus === 'critical' ? 'text-red-600' :
                            budgetStatus === 'warning' ? 'text-yellow-600' :
                            'text-green-600'
                          }`}>
                            {Math.round((project.spent / project.budget) * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Alerts */}
                  {project.status === 'delayed' && (
                    <Alert variant="error" className="mt-4">
                      <AlertTriangle className="w-4 h-4" />
                      <span>Ce projet est en retard. Action requise.</span>
                    </Alert>
                  )}
                  
                  {budgetStatus === 'critical' && (
                    <Alert variant="warning" className="mt-4">
                      <DollarSign className="w-4 h-4" />
                      <span>Budget presque épuisé ({Math.round((project.spent / project.budget) * 100)}%)</span>
                    </Alert>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectCheck;
