import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  CardContent,
  <PERSON>ge,
  Button,
  Progress
} from '../../components/ui';
import { 
  Users,
  DollarSign,
  Target,
  BarChart3,
  Phone,
  Mail,
  Globe,
  User
} from 'lucide-react';

const CommercialPerformance: React.FC = () => {
  // Mock data for commercial performance
  const salesData = {
    monthlySales: 450000,
    targetSales: 500000,
    leads: 156,
    qualifiedLeads: 89,
    conversions: 23,
    conversionRate: 14.7
  };

  const agents = [
    {
      id: 'agent-1',
      name: '<PERSON>',
      sales: 125000,
      target: 100000,
      leads: 45,
      conversions: 8,
      conversionRate: 17.8,
      channels: ['Téléphone', 'Email', 'LinkedIn']
    },
    {
      id: 'agent-2',
      name: '<PERSON>',
      sales: 98000,
      target: 100000,
      leads: 38,
      conversions: 6,
      conversionRate: 15.8,
      channels: ['Téléphone', 'Email']
    },
    {
      id: 'agent-3',
      name: '<PERSON>',
      sales: 145000,
      target: 120000,
      leads: 52,
      conversions: 9,
      conversionRate: 17.3,
      channels: ['<PERSON>éléphone', 'Email', 'LinkedIn', 'Site Web']
    }
  ];

  const channels = [
    {
      name: 'Téléphone',
      leads: 67,
      conversions: 12,
      conversionRate: 17.9,
      revenue: 180000
    },
    {
      name: 'Email',
      leads: 45,
      conversions: 6,
      conversionRate: 13.3,
      revenue: 95000
    },
    {
      name: 'LinkedIn',
      leads: 28,
      conversions: 4,
      conversionRate: 14.3,
      revenue: 85000
    },
    {
      name: 'Site Web',
      leads: 16,
      conversions: 1,
      conversionRate: 6.3,
      revenue: 90000
    }
  ];

  const recentLeads = [
    {
      id: 'lead-1',
      company: 'Entreprise ABC',
      contact: 'Pierre Durand',
      source: 'Téléphone',
      agent: 'Marie Dubois',
      status: 'Qualifié',
      value: 25000,
      date: '2024-01-25'
    },
    {
      id: 'lead-2',
      company: 'Société XYZ',
      contact: 'Sophie Martin',
      source: 'Email',
      agent: 'Jean Martin',
      status: 'En cours',
      value: 15000,
      date: '2024-01-24'
    },
    {
      id: 'lead-3',
      company: 'Groupe DEF',
      contact: 'Lucas Bernard',
      source: 'LinkedIn',
      agent: 'Sophie Bernard',
      status: 'Qualifié',
      value: 35000,
      date: '2024-01-23'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="metric">
          <CardHeader title="Ventes Mensuelles" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">
                  {salesData.monthlySales.toLocaleString()}€
                </div>
                <div className="text-sm text-gray-600">
                  Objectif: {salesData.targetSales.toLocaleString()}€
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-3">
              <Progress 
                value={(salesData.monthlySales / salesData.targetSales) * 100} 
                max={100} 
                size="sm" 
              />
              <div className="text-xs text-gray-500 mt-1">
                {Math.round((salesData.monthlySales / salesData.targetSales) * 100)}% de l'objectif
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Leads" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{salesData.leads}</div>
                <div className="text-sm text-gray-600">nouveaux leads</div>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-3">
              <div className="text-xs text-gray-600">
                {salesData.qualifiedLeads} qualifiés ({Math.round((salesData.qualifiedLeads / salesData.leads) * 100)}%)
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Conversions" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-purple-600">{salesData.conversions}</div>
                <div className="text-sm text-gray-600">conversions</div>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Target className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-3">
              <div className="text-xs text-gray-600">
                {salesData.conversionRate}% taux de conversion
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Performance Équipe" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-orange-600">
                  {Math.round(agents.reduce((sum, agent) => sum + (agent.sales / agent.target) * 100, 0) / agents.length)}%
                </div>
                <div className="text-sm text-gray-600">moyenne équipe</div>
              </div>
              <div className="p-3 bg-orange-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance by Agent */}
      <Card>
        <CardHeader title="Performance par Agent" />
        <CardContent>
          <div className="space-y-4">
            {agents.map((agent) => (
              <div key={agent.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{agent.name}</h3>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        {agent.channels.map((channel, index) => (
                          <div key={index} className="flex items-center space-x-1">
                            {channel === 'Téléphone' && <Phone className="w-3 h-3" />}
                            {channel === 'Email' && <Mail className="w-3 h-3" />}
                            {channel === 'LinkedIn' && <Globe className="w-3 h-3" />}
                            {channel === 'Site Web' && <Globe className="w-3 h-3" />}
                            <span>{channel}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      {agent.sales.toLocaleString()}€
                    </div>
                    <div className="text-sm text-gray-600">
                      Objectif: {agent.target.toLocaleString()}€
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <div className="text-sm text-gray-600">Leads</div>
                    <div className="text-lg font-semibold">{agent.leads}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Conversions</div>
                    <div className="text-lg font-semibold">{agent.conversions}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Taux</div>
                    <div className="text-lg font-semibold">{agent.conversionRate}%</div>
                  </div>
                </div>
                
                <div className="mt-3">
                  <Progress 
                    value={(agent.sales / agent.target) * 100} 
                    max={100} 
                    size="sm"
                    variant={(agent.sales / agent.target) >= 1 ? 'success' : 'primary'}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance by Channel */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader title="Performance par Canal" />
          <CardContent>
            <div className="space-y-4">
              {channels.map((channel) => (
                <div key={channel.name} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {channel.name === 'Téléphone' && <Phone className="w-4 h-4 text-gray-600" />}
                      {channel.name === 'Email' && <Mail className="w-4 h-4 text-gray-600" />}
                      {channel.name === 'LinkedIn' && <Globe className="w-4 h-4 text-gray-600" />}
                      {channel.name === 'Site Web' && <Globe className="w-4 h-4 text-gray-600" />}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{channel.name}</div>
                      <div className="text-sm text-gray-600">
                        {channel.leads} leads, {channel.conversions} conversions
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      {channel.revenue.toLocaleString()}€
                    </div>
                    <div className="text-sm text-gray-600">
                      {channel.conversionRate}% conversion
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="Leads Récents" />
          <CardContent>
            <div className="space-y-3">
              {recentLeads.map((lead) => (
                <div key={lead.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{lead.company}</div>
                    <div className="text-sm text-gray-600">{lead.contact}</div>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{lead.source}</span>
                      <span>•</span>
                      <span>{lead.agent}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={lead.status === 'Qualifié' ? 'success' : 'warning'}>
                      {lead.status}
                    </Badge>
                    <div className="text-sm font-medium text-gray-900 mt-1">
                      {lead.value.toLocaleString()}€
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Actions Rapides" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" fullWidth leftIcon={<Users className="w-4 h-4" />}>
              Nouveau Lead
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Phone className="w-4 h-4" />}>
              Appels Sortants
            </Button>
            <Button variant="outline" fullWidth leftIcon={<BarChart3 className="w-4 h-4" />}>
              Rapport Détaillé
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Target className="w-4 h-4" />}>
              Objectifs
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CommercialPerformance;
