import React from 'react';
import { 
  Card, 
  CardHeader, 
  CardContent,
  Badge,
  Button,
  Progress
} from '../../components/ui';
import { 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Activity,
  Package,
  Wrench,
  Target
} from 'lucide-react';

const OperationDashboard: React.FC = () => {
  // Mock data for dashboard overview
  const overviewData = {
    projects: {
      total: 12,
      active: 8,
      completed: 3,
      delayed: 1,
      completionRate: 75
    },
    stock: {
      totalItems: 1450,
      lowStock: 23,
      outOfStock: 5,
      stockValue: 125000
    },
    commercial: {
      monthlySales: 450000,
      leads: 156,
      conversionRate: 12.5,
      targetAchievement: 85
    },
    maintenance: {
      scheduled: 8,
      urgent: 2,
      completed: 15,
      nextWeek: 5
    }
  };

  const recentActivities = [
    { type: 'project', message: 'Projet Alpha - Phase 2 terminée', time: '2h ago', status: 'success' },
    { type: 'stock', message: 'Alerte: Stock faible - Produit XYZ', time: '4h ago', status: 'warning' },
    { type: 'commercial', message: 'Nouveau lead qualifié - Entreprise ABC', time: '6h ago', status: 'info' },
    { type: 'maintenance', message: 'Maintenance préventive - Machine 3', time: '8h ago', status: 'success' }
  ];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card variant="metric">
          <CardHeader title="Projets Actifs" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-blue-600">{overviewData.projects.active}</div>
                <div className="text-sm text-gray-600">sur {overviewData.projects.total} projets</div>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-3">
              <Progress value={overviewData.projects.completionRate} max={100} size="sm" />
              <div className="text-xs text-gray-500 mt-1">{overviewData.projects.completionRate}% complété</div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Stock" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-orange-600">{overviewData.stock.totalItems}</div>
                <div className="text-sm text-gray-600">articles en stock</div>
              </div>
              <div className="p-3 bg-orange-100 rounded-lg">
                <Package className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-3">
              <div className="text-xs text-red-600">{overviewData.stock.lowStock} alertes de stock</div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Performance Commerciale" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-green-600">{overviewData.commercial.monthlySales.toLocaleString()}€</div>
                <div className="text-sm text-gray-600">ventes mensuelles</div>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-3">
              <div className="text-xs text-gray-600">{overviewData.commercial.conversionRate}% taux de conversion</div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Maintenance" />
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-3xl font-bold text-purple-600">{overviewData.maintenance.scheduled}</div>
                <div className="text-sm text-gray-600">interventions planifiées</div>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Wrench className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-3">
              <div className="text-xs text-red-600">{overviewData.maintenance.urgent} interventions urgentes</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card>
        <CardHeader title="Activités Récentes" />
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    activity.status === 'success' ? 'bg-green-100' :
                    activity.status === 'warning' ? 'bg-yellow-100' :
                    'bg-blue-100'
                  }`}>
                    {activity.status === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
                    {activity.status === 'warning' && <AlertTriangle className="w-4 h-4 text-yellow-600" />}
                    {activity.status === 'info' && <Activity className="w-4 h-4 text-blue-600" />}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
                <Badge variant={activity.status as 'success' | 'warning' | 'info'} size="sm">
                  {activity.type}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Actions Rapides" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" fullWidth leftIcon={<Target className="w-4 h-4" />}>
              Nouveau Projet
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Package className="w-4 h-4" />}>
              Gestion Stock
            </Button>
            <Button variant="outline" fullWidth leftIcon={<TrendingUp className="w-4 h-4" />}>
              Rapport Commercial
            </Button>
            <Button variant="outline" fullWidth leftIcon={<Wrench className="w-4 h-4" />}>
              Planifier Maintenance
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OperationDashboard;
