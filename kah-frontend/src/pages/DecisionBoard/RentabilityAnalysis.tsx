import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  CardContent,
  Badge,
  Button,
  Progress
} from '../../components/ui';
import { 
  TrendingUp,
  BarChart3,
  DollarSign,
  Eye,
  Download
} from 'lucide-react';

const RentabilityAnalysis: React.FC = () => {
  // Mock data for rentability analysis
  const marginData = {
    overall: {
      current: 23.4,
      previous: 21.3,
      change: 2.1,
      target: 25.0
    },
    byProgram: [
      {
        name: 'Residential',
        margin: 26.8,
        change: 3.2,
        revenue: 1250000,
        properties: 89
      },
      {
        name: 'Commercial',
        margin: 18.5,
        change: 1.8,
        revenue: 850000,
        properties: 45
      },
      {
        name: 'Industrial',
        margin: 22.1,
        change: 2.5,
        revenue: 350000,
        properties: 22
      }
    ],
    byTypology: [
      {
        name: 'Luxury',
        margin: 32.5,
        change: 4.1,
        avgRent: 3200,
        occupancy: 96.2
      },
      {
        name: 'Standard',
        margin: 21.8,
        change: 1.9,
        avgRent: 1850,
        occupancy: 94.5
      },
      {
        name: 'Economy',
        margin: 15.2,
        change: 0.8,
        avgRent: 1200,
        occupancy: 92.1
      }
    ]
  };

  const costBreakdown = [
    { category: 'Maintenance', percentage: 35, amount: 857500 },
    { category: 'Utilities', percentage: 25, amount: 612500 },
    { category: 'Insurance', percentage: 15, amount: 367500 },
    { category: 'Management', percentage: 20, amount: 490000 },
    { category: 'Other', percentage: 5, amount: 122500 }
  ];

  const profitabilityTrends = [
    { month: 'Jan', margin: 21.2, revenue: 2100000 },
    { month: 'Feb', margin: 21.8, revenue: 2150000 },
    { month: 'Mar', margin: 22.1, revenue: 2200000 },
    { month: 'Apr', margin: 22.5, revenue: 2250000 },
    { month: 'May', margin: 22.9, revenue: 2300000 },
    { month: 'Jun', margin: 23.4, revenue: 2450000 }
  ];

  return (
    <div className="space-y-6">
      {/* Overall Margin Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card variant="metric">
          <CardHeader title="Current Margin" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {marginData.overall.current}%
                </div>
                <Badge variant="success">+{marginData.overall.change}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Target: {marginData.overall.target}%</span>
                  <span>{((marginData.overall.current / marginData.overall.target) * 100).toFixed(1)}%</span>
                </div>
                <Progress 
                  value={(marginData.overall.current / marginData.overall.target) * 100} 
                  max={100} 
                  variant="success" 
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Revenue Growth" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  +12.5%
                </div>
                <Badge variant="success">+2.1%</Badge>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                vs previous period
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Cost Efficiency" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  76.6%
                </div>
                <Badge variant="success">+1.8%</Badge>
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Revenue to cost ratio
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Margin by Program */}
      <Card>
        <CardHeader 
          title="Margin by Program"
          subtitle="Profitability comparison across property programs"
        />
        <CardContent>
          <div className="space-y-4">
            {marginData.byProgram.map((program, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <BarChart3 className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{program.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {program.properties} properties • ${(program.revenue / 1000).toFixed(0)}k revenue
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {program.margin}%
                  </div>
                  <div className="text-sm text-green-600">
                    +{program.change}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Margin by Typology */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader 
            title="Margin by Typology"
            subtitle="Profitability by property category"
          />
          <CardContent>
            <div className="space-y-4">
              {marginData.byTypology.map((typology, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      typology.margin > 25 ? 'bg-green-100 dark:bg-green-900/20' :
                      typology.margin > 20 ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                      'bg-red-100 dark:bg-red-900/20'
                    }`}>
                      <DollarSign className={`w-4 h-4 ${
                        typology.margin > 25 ? 'text-green-600' :
                        typology.margin > 20 ? 'text-yellow-600' :
                        'text-red-600'
                      }`} />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{typology.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        ${typology.avgRent} avg rent • {typology.occupancy}% occupancy
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                      {typology.margin}%
                    </div>
                    <div className="text-sm text-green-600">
                      +{typology.change}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader 
            title="Cost Breakdown"
            subtitle="Expense distribution analysis"
          />
          <CardContent>
            <div className="space-y-4">
              {costBreakdown.map((cost, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{cost.category}</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      ${(cost.amount / 1000).toFixed(0)}k
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${cost.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {cost.percentage}% of total costs
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Profitability Trends */}
      <Card>
        <CardHeader 
          title="Profitability Trends"
          subtitle="Margin and revenue evolution over time"
        />
        <CardContent>
          <div className="space-y-4">
            {profitabilityTrends.map((trend, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                    <TrendingUp className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">{trend.month}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      ${(trend.revenue / 1000000).toFixed(1)}M revenue
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {trend.margin}%
                  </div>
                  <div className="text-sm text-green-600">
                    +{((trend.margin - profitabilityTrends[Math.max(0, index - 1)].margin)).toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<Eye className="w-4 h-4" />} fullWidth>
              Detailed Analysis
            </Button>
            <Button variant="outline" leftIcon={<BarChart3 className="w-4 h-4" />} fullWidth>
              KPI Overview
            </Button>
            <Button variant="outline" leftIcon={<TrendingUp className="w-4 h-4" />} fullWidth>
              Forecasting
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RentabilityAnalysis;
