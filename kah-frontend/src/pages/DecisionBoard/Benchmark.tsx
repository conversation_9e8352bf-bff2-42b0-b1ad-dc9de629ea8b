import React from 'react';
import { 
  <PERSON>, 
  Card<PERSON>eader, 
  CardContent,
  Badge,
  Button,
  Progress
} from '../../components/ui';
import { 
  TrendingUp,
  TrendingDown,
  Target,
  BarChart3,
  Trophy,
  Award,
  Eye,
  Download
} from 'lucide-react';

const Benchmark: React.FC = () => {
  // Mock data for benchmarking
  const marketPosition = {
    revenue: {
      our: 2450000,
      market: 2200000,
      top: 2800000,
      rank: 2
    },
    margin: {
      our: 23.4,
      market: 20.1,
      top: 26.8,
      rank: 3
    },
    occupancy: {
      our: 94.2,
      market: 91.5,
      top: 97.1,
      rank: 2
    }
  };

  const competitors = [
    {
      name: 'RealEstate Pro',
      revenue: 2800000,
      margin: 26.8,
      occupancy: 97.1,
      marketShare: 18.5,
      rating: 4.8
    },
    {
      name: 'PropertyMax',
      revenue: 2450000,
      margin: 23.4,
      occupancy: 94.2,
      marketShare: 15.2,
      rating: 4.6
    },
    {
      name: 'Urban Living',
      revenue: 2100000,
      margin: 20.1,
      occupancy: 91.5,
      marketShare: 12.8,
      rating: 4.3
    },
    {
      name: 'City Properties',
      revenue: 1950000,
      margin: 18.9,
      occupancy: 89.2,
      marketShare: 11.5,
      rating: 4.1
    }
  ];

  const marketMetrics = [
    {
      metric: 'Market Share',
      our: 15.2,
      market: 12.8,
      change: 2.4,
      trend: 'up'
    },
    {
      metric: 'Customer Satisfaction',
      our: 4.6,
      market: 4.2,
      change: 0.4,
      trend: 'up'
    },
    {
      metric: 'Growth Rate',
      our: 12.5,
      market: 8.7,
      change: 3.8,
      trend: 'up'
    },
    {
      metric: 'Efficiency Score',
      our: 87.3,
      market: 82.1,
      change: 5.2,
      trend: 'up'
    }
  ];

  const strengths = [
    'High occupancy rates',
    'Strong customer satisfaction',
    'Efficient cost management',
    'Diversified portfolio'
  ];

  const opportunities = [
    'Expand luxury segment',
    'Improve digital presence',
    'Enhance customer service',
    'Optimize pricing strategy'
  ];

  return (
    <div className="space-y-6">
      {/* Market Position Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card variant="metric">
          <CardHeader title="Revenue Ranking" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  #{marketPosition.revenue.rank}
                </div>
                <Badge variant="success">+11.4%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Market: ${(marketPosition.revenue.market / 1000000).toFixed(1)}M</span>
                  <span>Top: ${(marketPosition.revenue.top / 1000000).toFixed(1)}M</span>
                </div>
                <Progress 
                  value={(marketPosition.revenue.our / marketPosition.revenue.top) * 100} 
                  max={100} 
                  variant="success" 
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Margin Ranking" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  #{marketPosition.margin.rank}
                </div>
                <Badge variant="success">+16.4%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Market: {marketPosition.margin.market}%</span>
                  <span>Top: {marketPosition.margin.top}%</span>
                </div>
                <Progress 
                  value={(marketPosition.margin.our / marketPosition.margin.top) * 100} 
                  max={100} 
                  variant="success" 
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Occupancy Ranking" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  #{marketPosition.occupancy.rank}
                </div>
                <Badge variant="success">+3.0%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Market: {marketPosition.occupancy.market}%</span>
                  <span>Top: {marketPosition.occupancy.top}%</span>
                </div>
                <Progress 
                  value={(marketPosition.occupancy.our / marketPosition.occupancy.top) * 100} 
                  max={100} 
                  variant="success" 
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Competitor Comparison */}
      <Card>
        <CardHeader 
          title="Competitor Analysis"
          subtitle="Performance comparison with key competitors"
        />
        <CardContent>
          <div className="space-y-4">
            {competitors.map((competitor, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`p-2 rounded-lg ${
                    index === 1 ? 'bg-blue-100 dark:bg-blue-900/20' :
                    index === 0 ? 'bg-green-100 dark:bg-green-900/20' :
                    'bg-gray-100 dark:bg-gray-700'
                  }`}>
                    {index === 1 ? (
                      <Trophy className="w-5 h-5 text-blue-600" />
                    ) : index === 0 ? (
                      <Award className="w-5 h-5 text-green-600" />
                    ) : (
                      <BarChart3 className="w-5 h-5 text-gray-600" />
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium">{competitor.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {competitor.margin}% margin • {competitor.occupancy}% occupancy
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    ${(competitor.revenue / 1000000).toFixed(1)}M
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="info" size="sm">
                      {competitor.marketShare}% share
                    </Badge>
                    <div className="flex items-center text-sm text-yellow-600">
                      ★ {competitor.rating}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Market Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader 
            title="Market Performance"
            subtitle="How we compare to market averages"
          />
          <CardContent>
            <div className="space-y-4">
              {marketMetrics.map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      metric.trend === 'up' ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'
                    }`}>
                      {metric.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{metric.metric}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Market avg: {metric.market}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                      {metric.our}
                    </div>
                    <div className={`text-sm ${
                      metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      +{metric.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader 
            title="SWOT Analysis"
            subtitle="Strengths, Weaknesses, Opportunities, Threats"
          />
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-green-600 mb-2">Strengths</h4>
                <div className="space-y-2">
                  {strengths.map((strength, index) => (
                    <div key={index} className="flex items-center space-x-2 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>{strength}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-blue-600 mb-2">Opportunities</h4>
                <div className="space-y-2">
                  {opportunities.map((opportunity, index) => (
                    <div key={index} className="flex items-center space-x-2 text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>{opportunity}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Market Insights */}
      <Card>
        <CardHeader 
          title="Market Insights"
          subtitle="Key trends and positioning insights"
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">Strong</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Market position with competitive advantages
              </div>
            </div>
            
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">Growing</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Market share and customer base
              </div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">Innovative</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Technology and service offerings
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<Eye className="w-4 h-4" />} fullWidth>
              Detailed Analysis
            </Button>
            <Button variant="outline" leftIcon={<BarChart3 className="w-4 h-4" />} fullWidth>
              KPI Overview
            </Button>
            <Button variant="outline" leftIcon={<Target className="w-4 h-4" />} fullWidth>
              Forecasting
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Benchmark;
