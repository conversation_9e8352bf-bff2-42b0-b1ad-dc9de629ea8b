import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>ontent,
  <PERSON>ge,
  Button,
  Progress
} from '../../components/ui';
import { 
  TrendingUp,
  TrendingDown,
  Calendar,
  Target,
  BarChart3,
  Eye,
  Download
} from 'lucide-react';

const Forecasting: React.FC = () => {
  // Mock data for forecasting
  const projections = {
    revenue: {
      current: 2450000,
      nextYear: 2750000,
      growth: 12.2,
      confidence: 85
    },
    occupancy: {
      current: 94.2,
      nextYear: 96.5,
      growth: 2.3,
      confidence: 92
    },
    margin: {
      current: 23.4,
      nextYear: 25.8,
      growth: 2.4,
      confidence: 78
    }
  };

  const quarterlyForecast = [
    {
      quarter: 'Q1 2024',
      revenue: 2600000,
      margin: 24.2,
      occupancy: 95.1,
      confidence: 'high'
    },
    {
      quarter: 'Q2 2024',
      revenue: 2750000,
      margin: 25.1,
      occupancy: 95.8,
      confidence: 'high'
    },
    {
      quarter: 'Q3 2024',
      revenue: 2850000,
      margin: 25.6,
      occupancy: 96.2,
      confidence: 'medium'
    },
    {
      quarter: 'Q4 2024',
      revenue: 2950000,
      margin: 25.8,
      occupancy: 96.5,
      confidence: 'medium'
    }
  ];

  const marketTrends = [
    {
      factor: 'Market Demand',
      impact: 'positive',
      strength: 'high',
      description: 'Growing demand in urban areas'
    },
    {
      factor: 'Interest Rates',
      impact: 'negative',
      strength: 'medium',
      description: 'Potential rate increases'
    },
    {
      factor: 'Supply Growth',
      impact: 'neutral',
      strength: 'low',
      description: 'Stable supply pipeline'
    },
    {
      factor: 'Economic Growth',
      impact: 'positive',
      strength: 'high',
      description: 'Strong economic indicators'
    }
  ];

  const riskFactors = [
    {
      risk: 'Market Volatility',
      probability: 'medium',
      impact: 'high',
      mitigation: 'Diversified portfolio'
    },
    {
      risk: 'Regulatory Changes',
      probability: 'low',
      impact: 'medium',
      mitigation: 'Compliance monitoring'
    },
    {
      risk: 'Economic Downturn',
      probability: 'low',
      impact: 'high',
      mitigation: 'Reserve funds'
    },
    {
      risk: 'Competition',
      probability: 'medium',
      impact: 'medium',
      mitigation: 'Value proposition'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Projection Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card variant="metric">
          <CardHeader title="Revenue Projection" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  ${(projections.revenue.nextYear / 1000000).toFixed(1)}M
                </div>
                <Badge variant="success">+{projections.revenue.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Confidence: {projections.revenue.confidence}%</span>
                </div>
                <Progress value={projections.revenue.confidence} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Occupancy Forecast" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {projections.occupancy.nextYear}%
                </div>
                <Badge variant="success">+{projections.occupancy.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Confidence: {projections.occupancy.confidence}%</span>
                </div>
                <Progress value={projections.occupancy.confidence} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Margin Forecast" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {projections.margin.nextYear}%
                </div>
                <Badge variant="success">+{projections.margin.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Confidence: {projections.margin.confidence}%</span>
                </div>
                <Progress value={projections.margin.confidence} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quarterly Forecast */}
      <Card>
        <CardHeader 
          title="Quarterly Forecast"
          subtitle="Detailed projections by quarter"
        />
        <CardContent>
          <div className="space-y-4">
            {quarterlyForecast.map((quarter, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <Calendar className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{quarter.quarter}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {quarter.occupancy}% occupancy • {quarter.margin}% margin
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    ${(quarter.revenue / 1000000).toFixed(1)}M
                  </div>
                  <Badge 
                    variant={
                      quarter.confidence === 'high' ? 'success' :
                      quarter.confidence === 'medium' ? 'warning' : 'error'
                    }
                    size="sm"
                  >
                    {quarter.confidence} confidence
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Market Trends and Risk Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader 
            title="Market Trends"
            subtitle="Key factors influencing projections"
          />
          <CardContent>
            <div className="space-y-4">
              {marketTrends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      trend.impact === 'positive' ? 'bg-green-100 dark:bg-green-900/20' :
                      trend.impact === 'negative' ? 'bg-red-100 dark:bg-red-900/20' :
                      'bg-yellow-100 dark:bg-yellow-900/20'
                    }`}>
                      {trend.impact === 'positive' ? (
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      ) : trend.impact === 'negative' ? (
                        <TrendingDown className="w-4 h-4 text-red-600" />
                      ) : (
                        <BarChart3 className="w-4 h-4 text-yellow-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{trend.factor}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {trend.description}
                      </div>
                    </div>
                  </div>
                  <Badge 
                    variant={
                      trend.strength === 'high' ? 'success' :
                      trend.strength === 'medium' ? 'warning' : 'secondary'
                    }
                    size="sm"
                  >
                    {trend.strength}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader 
            title="Risk Analysis"
            subtitle="Potential risks and mitigation strategies"
          />
          <CardContent>
            <div className="space-y-4">
              {riskFactors.map((risk, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">{risk.risk}</h4>
                    <div className="flex space-x-2">
                      <Badge 
                        variant={
                          risk.probability === 'high' ? 'error' :
                          risk.probability === 'medium' ? 'warning' : 'success'
                        }
                        size="sm"
                      >
                        {risk.probability}
                      </Badge>
                      <Badge 
                        variant={
                          risk.impact === 'high' ? 'error' :
                          risk.impact === 'medium' ? 'warning' : 'success'
                        }
                        size="sm"
                      >
                        {risk.impact}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Mitigation: {risk.mitigation}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Scenario Analysis */}
      <Card>
        <CardHeader 
          title="Scenario Analysis"
          subtitle="Best, expected, and worst case scenarios"
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">Best Case</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Revenue: $3.2M<br />
                Margin: 27.5%<br />
                Occupancy: 98.2%
              </div>
              <Badge variant="success">+30% growth</Badge>
            </div>
            
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">Expected</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Revenue: $2.95M<br />
                Margin: 25.8%<br />
                Occupancy: 96.5%
              </div>
              <Badge variant="info">+12% growth</Badge>
            </div>
            
            <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600 mb-2">Worst Case</div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Revenue: $2.6M<br />
                Margin: 22.1%<br />
                Occupancy: 92.8%
              </div>
              <Badge variant="error">+6% growth</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<Eye className="w-4 h-4" />} fullWidth>
              Detailed Analysis
            </Button>
            <Button variant="outline" leftIcon={<BarChart3 className="w-4 h-4" />} fullWidth>
              KPI Overview
            </Button>
            <Button variant="outline" leftIcon={<Target className="w-4 h-4" />} fullWidth>
              Benchmark
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export Forecast
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Forecasting;
