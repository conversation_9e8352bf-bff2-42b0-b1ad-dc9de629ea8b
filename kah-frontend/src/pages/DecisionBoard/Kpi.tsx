import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>eader, 
  CardContent,
  Badge,
  Button,
  Progress
} from '../../components/ui';
import { 
  TrendingUp,
  TrendingDown,
  Home,
  BarChart3,
  Eye,
  Download
} from 'lucide-react';

const Kpi: React.FC = () => {
  // Mock data for KPI overview
  const kpiData = {
    revenue: {
      total: 2450000,
      growth: 12.5,
      target: 2500000,
      achievement: 98
    },
    occupancy: {
      rate: 94.2,
      growth: 1.2,
      target: 95,
      achievement: 99.2
    },
    profit: {
      margin: 23.4,
      growth: 2.1,
      target: 25,
      achievement: 93.6
    },
    rent: {
      average: 1850,
      growth: 8.3,
      target: 1900,
      achievement: 97.4
    }
  };

  const propertyMetrics = [
    {
      category: 'Residential',
      total: 89,
      occupied: 85,
      revenue: 1250000,
      growth: 15.2
    },
    {
      category: 'Commercial',
      total: 45,
      occupied: 42,
      revenue: 850000,
      growth: 8.7
    },
    {
      category: 'Industrial',
      total: 22,
      occupied: 20,
      revenue: 350000,
      growth: 12.1
    }
  ];

  const performanceIndicators = [
    {
      name: 'Revenue per Property',
      value: 15705,
      change: '+12.3%',
      trend: 'up',
      target: 16000
    },
    {
      name: 'Occupancy Rate',
      value: 94.2,
      change: '+1.2%',
      trend: 'up',
      target: 95
    },
    {
      name: 'Profit Margin',
      value: 23.4,
      change: '+2.1%',
      trend: 'up',
      target: 25
    },
    {
      name: 'Customer Satisfaction',
      value: 4.6,
      change: '+0.2',
      trend: 'up',
      target: 4.8
    }
  ];

  return (
    <div className="space-y-6">
      {/* Main KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card variant="metric">
          <CardHeader title="Total Revenue" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  ${(kpiData.revenue.total / 1000000).toFixed(1)}M
                </div>
                <Badge variant="success">+{kpiData.revenue.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Target: ${(kpiData.revenue.target / 1000000).toFixed(1)}M</span>
                  <span>{kpiData.revenue.achievement}%</span>
                </div>
                <Progress value={kpiData.revenue.achievement} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Occupancy Rate" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {kpiData.occupancy.rate}%
                </div>
                <Badge variant="success">+{kpiData.occupancy.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Target: {kpiData.occupancy.target}%</span>
                  <span>{kpiData.occupancy.achievement}%</span>
                </div>
                <Progress value={kpiData.occupancy.achievement} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Profit Margin" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  {kpiData.profit.margin}%
                </div>
                <Badge variant="success">+{kpiData.profit.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Target: {kpiData.profit.target}%</span>
                  <span>{kpiData.profit.achievement}%</span>
                </div>
                <Progress value={kpiData.profit.achievement} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card variant="metric">
          <CardHeader title="Average Rent" />
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="text-3xl font-bold text-gray-900 dark:text-white">
                  ${kpiData.rent.average}
                </div>
                <Badge variant="success">+{kpiData.rent.growth}%</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Target: ${kpiData.rent.target}</span>
                  <span>{kpiData.rent.achievement}%</span>
                </div>
                <Progress value={kpiData.rent.achievement} max={100} variant="success" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Property Performance by Category */}
      <Card>
        <CardHeader 
          title="Property Performance by Category"
          subtitle="Revenue and occupancy by property type"
        />
        <CardContent>
          <div className="space-y-4">
            {propertyMetrics.map((property, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <Home className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium">{property.category}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {property.occupied}/{property.total} properties
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    ${(property.revenue / 1000).toFixed(0)}k
                  </div>
                  <div className="text-sm text-green-600">
                    +{property.growth}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Indicators */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader title="Key Performance Indicators" />
          <CardContent>
            <div className="space-y-4">
              {performanceIndicators.map((indicator, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      indicator.trend === 'up' ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20'
                    }`}>
                      {indicator.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4 text-green-600" />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{indicator.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Target: {indicator.target}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">
                      {typeof indicator.value === 'number' && indicator.value > 1000 
                        ? indicator.value.toLocaleString() 
                        : indicator.value}
                    </div>
                    <div className={`text-sm ${
                      indicator.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {indicator.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader title="Performance Summary" />
          <CardContent>
            <div className="space-y-4">
              <div className="text-center p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg">
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Excellent
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Overall performance exceeds targets
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-gray-50 dark:bg-dark-700 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">98%</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Target Achievement</div>
                </div>
                <div className="text-center p-4 bg-gray-50 dark:bg-dark-700 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">4.6/5</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Customer Rating</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<Eye className="w-4 h-4" />} fullWidth>
              Detailed Analysis
            </Button>
            <Button variant="outline" leftIcon={<BarChart3 className="w-4 h-4" />} fullWidth>
              Rentability Analysis
            </Button>
            <Button variant="outline" leftIcon={<TrendingUp className="w-4 h-4" />} fullWidth>
              Forecasting
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export KPI Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Kpi;
