import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>eader, 
  CardContent,
  <PERSON>ge,
  But<PERSON>
} from '../../components/ui';
import { 
  TrendingUp,
  TrendingDown,
  BarChart3,
  DollarSign,
  Target,
  Activity,
  Eye,
  Download
} from 'lucide-react';

const BoardDashboard: React.FC = () => {
  // Mock data for dashboard overview
  const overviewData = {
    totalRevenue: 2450000,
    revenueGrowth: 12.5,
    totalProperties: 156,
    occupancyRate: 94.2,
    avgRent: 1850,
    rentGrowth: 8.3,
    profitMargin: 23.4,
    marginGrowth: 2.1
  };

  const recentActivities = [
    {
      id: 1,
      type: 'revenue',
      title: 'Q4 Revenue Target Achieved',
      value: '+15.2%',
      status: 'success',
      time: '2 hours ago'
    },
    {
      id: 2,
      type: 'property',
      title: 'New Property Acquisition',
      value: '3 properties',
      status: 'info',
      time: '1 day ago'
    },
    {
      id: 3,
      type: 'rent',
      title: 'Rent Increase Applied',
      value: '+5.8%',
      status: 'success',
      time: '2 days ago'
    },
    {
      id: 4,
      type: 'maintenance',
      title: 'Maintenance Cost Reduction',
      value: '-12.3%',
      status: 'success',
      time: '3 days ago'
    }
  ];

  const quickMetrics = [
    {
      title: 'Revenue Growth',
      value: `${overviewData.revenueGrowth}%`,
      change: '+2.1%',
      trend: 'up',
      color: 'green'
    },
    {
      title: 'Occupancy Rate',
      value: `${overviewData.occupancyRate}%`,
      change: '+1.2%',
      trend: 'up',
      color: 'blue'
    },
    {
      title: 'Profit Margin',
      value: `${overviewData.profitMargin}%`,
      change: '+0.8%',
      trend: 'up',
      color: 'purple'
    },
    {
      title: 'Avg Rent',
      value: `$${overviewData.avgRent}`,
      change: '+$45',
      trend: 'up',
      color: 'orange'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickMetrics.map((metric, index) => (
          <Card key={index} variant="metric">
            <CardHeader title={metric.title} />
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {metric.value}
                  </div>
                  <div className={`text-sm ${
                    metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.change}
                  </div>
                </div>
                <div className={`p-2 rounded-lg ${
                  metric.color === 'green' ? 'bg-green-100 dark:bg-green-900/20' :
                  metric.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/20' :
                  metric.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/20' :
                  'bg-orange-100 dark:bg-orange-900/20'
                }`}>
                  {metric.trend === 'up' ? (
                    <TrendingUp className={`w-5 h-5 ${
                      metric.color === 'green' ? 'text-green-600' :
                      metric.color === 'blue' ? 'text-blue-600' :
                      metric.color === 'purple' ? 'text-purple-600' :
                      'text-orange-600'
                    }`} />
                  ) : (
                    <TrendingDown className="w-5 h-5 text-red-600" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Key Performance Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader 
            title="Financial Performance"
            subtitle="Key metrics overview"
          />
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <DollarSign className="w-5 h-5 text-green-600" />
                  <div>
                    <div className="font-medium">Total Revenue</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      ${(overviewData.totalRevenue / 1000000).toFixed(1)}M
                    </div>
                  </div>
                </div>
                <Badge variant="success">+{overviewData.revenueGrowth}%</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Target className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Properties</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {overviewData.totalProperties} units
                    </div>
                  </div>
                </div>
                <Badge variant="info">{overviewData.occupancyRate}% occupied</Badge>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <BarChart3 className="w-5 h-5 text-purple-600" />
                  <div>
                    <div className="font-medium">Profit Margin</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {overviewData.profitMargin}% average
                    </div>
                  </div>
                </div>
                <Badge variant="success">+{overviewData.marginGrowth}%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader 
            title="Recent Activities"
            subtitle="Latest updates and changes"
          />
          <CardContent>
            <div className="space-y-3">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${
                      activity.status === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                      activity.status === 'info' ? 'bg-blue-100 dark:bg-blue-900/20' :
                      'bg-yellow-100 dark:bg-yellow-900/20'
                    }`}>
                      <Activity className={`w-4 h-4 ${
                        activity.status === 'success' ? 'text-green-600' :
                        activity.status === 'info' ? 'text-blue-600' :
                        'text-yellow-600'
                      }`} />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{activity.title}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {activity.time}
                      </div>
                    </div>
                  </div>
                  <Badge 
                    variant={
                      activity.status === 'success' ? 'success' :
                      activity.status === 'info' ? 'info' : 'warning'
                    }
                    size="sm"
                  >
                    {activity.value}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<Eye className="w-4 h-4" />} fullWidth>
              View KPI Details
            </Button>
            <Button variant="outline" leftIcon={<BarChart3 className="w-4 h-4" />} fullWidth>
              Rentability Analysis
            </Button>
            <Button variant="outline" leftIcon={<TrendingUp className="w-4 h-4" />} fullWidth>
              Forecasting
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BoardDashboard;
