import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, Card<PERSON>ontent, But<PERSON> } from '../../components/ui';
import { Download, FileText } from 'lucide-react';

const Report: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader title="Rapports et Exportations" subtitle="Générez et téléchargez des rapports décisionnels" />
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12">
            <FileText className="w-12 h-12 text-primary-600 mb-4" />
            <h2 className="text-xl font-bold mb-2">Rapports du Board</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 text-center">
              Accédez à l'ensemble des rapports générés pour le pôle décisionnel. Exportez les analyses, synthèses et documents clés pour vos réunions et suivis stratégiques.
            </p>
            <Button variant="primary" leftIcon={<Download className="w-4 h-4" />}>
              Télécharger le dernier rapport
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Report;
