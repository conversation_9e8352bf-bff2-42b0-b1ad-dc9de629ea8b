import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    roleId: '',
  });
  const [errors, setErrors] = useState<{ username?: string; password?: string; roleId?: string }>({});
  const { login, isLoading, isAuthenticated, user, roles } = useAuth();
  const { isDark } = useTheme();
  const navigate = useNavigate();

  // Debug logging for roles
  useEffect(() => {
    console.log('Login component: roles received:', roles);
  }, [roles]);

  // Redirect to appropriate page after successful login
  useEffect(() => {
    if (isAuthenticated && user) {
      switch (user.role?.name) {
        case 'operation_board':
          navigate('/operations/dashboard');
          break;
        case 'analyst':
          navigate('/analyst/donnee');
          break;
        case 'decision_board':
          navigate('/board/dashboard');
          break;
        default:
          navigate('/operations/dashboard');
          break;
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const validateForm = () => {
    const newErrors: { username?: string; password?: string; roleId?: string } = {};

    if (!formData.username) {
      newErrors.username = 'Username is required';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!formData.roleId) {
      newErrors.roleId = 'Please select a role';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const success = await login(formData.username, formData.password, parseInt(formData.roleId));
    if (!success) {
      setErrors({
        password: 'Invalid username, password, or role selection'
      });
    }
  };

  return (
    <div className={`min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 ${
      isDark 
        ? 'bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900' 
        : 'bg-gradient-to-br from-kaydan-gray-50 to-kaydan-gray-100'
    }`}>
      <div className="max-w-md w-full space-y-8">
        {/* Logo and Header */}
        <div className="text-center">
          <div className={`mx-auto h-16 w-16 rounded-xl flex items-center justify-center mb-4 shadow-lg ${
            isDark 
              ? 'bg-gradient-to-br from-primary-500 to-primary-600 shadow-primary-500/25' 
              : 'bg-kaydan-500'
          }`}>
            <svg className="h-8 w-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className={`text-3xl font-bold ${
            isDark ? 'text-white' : 'text-kaydan-gray-900'
          }`}>
            Welcome back
          </h2>
          <p className={`mt-2 text-sm ${
            isDark ? 'text-gray-300' : 'text-kaydan-gray-600'
          }`}>
            Sign in to your Kaydan Analytics Hub account
          </p>
        </div>

        {/* Login Form */}
        <div className={`py-8 px-6 shadow-2xl rounded-xl border ${
          isDark 
            ? 'bg-dark-800 border-dark-700 shadow-dark-900/50' 
            : 'bg-white border-kaydan-gray-200'
        }`}>
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Username Field */}
            <div>
              <label htmlFor="username" className={`block text-sm font-medium mb-2 ${
                isDark ? 'text-gray-200' : 'text-kaydan-gray-700'
              }`}>
                Username
              </label>
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                required
                value={formData.username}
                onChange={handleInputChange}
                className={`appearance-none relative block w-full px-3 py-3 border rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 ${
                  isDark 
                    ? 'bg-dark-700 border-dark-600 text-white placeholder-gray-500' 
                    : 'text-kaydan-gray-900 border-kaydan-gray-300'
                } ${
                  errors.username 
                    ? 'border-red-400 focus:ring-red-500 focus:border-red-500' 
                    : ''
                }`}
                placeholder="Enter your username"
              />
              {errors.username && (
                <p className="mt-1 text-sm text-red-400">{errors.username}</p>
              )}
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className={`block text-sm font-medium mb-2 ${
                isDark ? 'text-gray-200' : 'text-kaydan-gray-700'
              }`}>
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={formData.password}
                onChange={handleInputChange}
                className={`appearance-none relative block w-full px-3 py-3 border rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 ${
                  isDark 
                    ? 'bg-dark-700 border-dark-600 text-white placeholder-gray-500' 
                    : 'text-kaydan-gray-900 border-kaydan-gray-300'
                } ${
                  errors.password 
                    ? 'border-red-400 focus:ring-red-500 focus:border-red-500' 
                    : ''
                }`}
                placeholder="Enter your password"
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-400">{errors.password}</p>
              )}
            </div>

            {/* Role Selection Field */}
            <div>
              <label htmlFor="roleId" className={`block text-sm font-medium mb-2 ${
                isDark ? 'text-gray-200' : 'text-kaydan-gray-700'
              }`}>
                Select Your Role
              </label>
              <select
                id="roleId"
                name="roleId"
                required
                value={formData.roleId}
                onChange={handleInputChange}
                disabled={roles.length === 0}
                className={`appearance-none relative block w-full px-3 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm transition-all duration-200 ${
                  isDark 
                    ? 'bg-dark-700 border-dark-600 text-white' 
                    : 'text-kaydan-gray-900 border-kaydan-gray-300'
                } ${
                  errors.roleId 
                    ? 'border-red-400 focus:ring-red-500 focus:border-red-500' 
                    : ''
                } ${roles.length === 0 ? (isDark ? 'bg-dark-600 cursor-not-allowed' : 'bg-gray-100 cursor-not-allowed') : ''}`}
              >
                <option value="">
                  {roles.length === 0 ? 'Loading roles...' : 'Select a role'}
                </option>
                {roles.map((role) => (
                  <option key={role.id} value={role.id}>
                    {role.name.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                  </option>
                ))}
              </select>
              {errors.roleId && (
                <p className="mt-1 text-sm text-red-400">{errors.roleId}</p>
              )}
              {roles.length === 0 && (
                <p className={`mt-1 text-sm ${
                  isDark ? 'text-gray-400' : 'text-kaydan-gray-500'
                }`}>
                  Loading available roles...
                </p>
              )}
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className={`h-4 w-4 text-primary-500 focus:ring-primary-500 rounded transition-colors ${
                    isDark ? 'border-dark-600 bg-dark-700' : 'border-kaydan-gray-300'
                  }`}
                />
                <label htmlFor="remember-me" className={`ml-2 block text-sm ${
                  isDark ? 'text-gray-300' : 'text-kaydan-gray-700'
                }`}>
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <a href="#" className={`font-medium hover:text-primary-400 transition-colors ${
                  isDark ? 'text-primary-400' : 'text-kaydan-600'
                }`}>
                  Forgot your password?
                </a>
              </div>
            </div>

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className={`group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${
                  isDark 
                    ? 'bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 focus:ring-primary-500 shadow-lg shadow-primary-500/25' 
                    : 'bg-kaydan-600 hover:bg-kaydan-700 focus:ring-kaydan-500'
                }`}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </div>
                ) : (
                  'Sign in'
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Sign Up Link */}
        <div className="text-center">
          <p className={`text-sm ${
            isDark ? 'text-gray-400' : 'text-kaydan-gray-600'
          }`}>
            Don't have an account?{' '}
            <a href="#" className={`font-medium hover:text-primary-400 transition-colors ${
              isDark ? 'text-primary-400' : 'text-kaydan-600'
            }`}>
              Sign up for free
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
