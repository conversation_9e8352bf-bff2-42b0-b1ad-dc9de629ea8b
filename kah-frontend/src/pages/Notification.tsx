import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  Search, 
  Filter, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  X, 
  Clock, 
  Trash2, 
  Archive,
  Settings,
  MoreVertical,
  Eye,
  EyeOff,
  Send,
  Users,
  MessageSquare,
  Plus
} from 'lucide-react';
import { useTheme } from '../context/ThemeContext';
import { AppGeneraleService } from '../services/appGeneraleService';
import { AuthService } from '../services/auth';
import api from '../services/axios';
import type { MessagingUser } from '../types/messaging';

// Import layout components
import Header from '../components/layout/Header';
import Sidebar from '../components/layout/Sidebar';
import Footer from '../components/layout/Footer';

interface Notification {
  id: number;
  title: string;
  message: string;
  time: string;
  type: 'warning' | 'success' | 'info' | 'error';
  unread: boolean;
  actionable?: boolean;
  href?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  sender?: string;
  senderId?: number; // Add sender ID for replies
  isMessage?: boolean;
  canReply?: boolean;
}

const Notification: React.FC = () => {
  const { isDark } = useTheme();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [selectedType, setSelectedType] = useState<'all' | 'warning' | 'success' | 'info' | 'error'>('all');
  const [selectedPriority, setSelectedPriority] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  // Messaging states
  const [showComposeModal, setShowComposeModal] = useState(false);
  const [messageType, setMessageType] = useState<'individual' | 'broadcast'>('individual');
  const [selectedRecipients, setSelectedRecipients] = useState<number[]>([]);
  const [messageTitle, setMessageTitle] = useState('');
  const [messageContent, setMessageContent] = useState('');
  const [messagePriority, setMessagePriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [users, setUsers] = useState<MessagingUser[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [usersError, setUsersError] = useState<string | null>(null);
  
  // Loading states
  const [loadingNotifications, setLoadingNotifications] = useState(false);
  const [notificationsError, setNotificationsError] = useState<string | null>(null);

  // Notification detail modal states
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [sendingReply, setSendingReply] = useState(false);
  const [messageHistory, setMessageHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);

  // Fetch notifications from backend
  const fetchNotifications = async () => {
    const token = AuthService.getToken();
    if (!token) {
      console.log('No token available for fetching notifications');
      return;
    }
    
    console.log('Fetching notifications from backend...');
    setLoadingNotifications(true);
    setNotificationsError(null);
    
    try {
      const response = await api.get('/auth/notifications/');
      console.log('Notifications fetched successfully:', response.data);
      
      // Transform backend data to frontend format
      const transformedNotifications: Notification[] = response.data.map((notification: any) => ({
        id: notification.id,
        title: notification.title,
        message: notification.message,
        time: formatTimeAgo(new Date(notification.created_at)),
        type: mapNotificationType(notification.notification_type),
        unread: !notification.is_read,
        actionable: notification.is_message,
        href: notification.is_message ? '/messages' : undefined,
        category: notification.notification_type === 'message' ? 'Messages' : 
                 notification.notification_type === 'broadcast' ? 'Broadcast' : 
                 notification.notification_type === 'alert' ? 'Alerts' : 'System',
        priority: notification.priority,
        sender: notification.sender ? `${notification.sender.first_name} ${notification.sender.last_name}` : undefined,
        senderId: notification.sender ? notification.sender.id : undefined,
        isMessage: notification.is_message,
        canReply: notification.can_reply,
      }));
      
      setNotifications(transformedNotifications);
    } catch (error: any) {
      console.error('Error fetching notifications:', error);
      setNotificationsError('Failed to load notifications. Please try again.');
    } finally {
      setLoadingNotifications(false);
    }
  };

  // Helper function to format time ago
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
  };

  // Helper function to map notification types
  const mapNotificationType = (type: string): 'warning' | 'success' | 'info' | 'error' => {
    switch (type) {
      case 'alert': return 'error';
      case 'broadcast': return 'warning';
      case 'message': return 'info';
      default: return 'info';
    }
  };

  // Fetch users from backend using the service
  const fetchUsers = async () => {
    const token = AuthService.getToken();
    if (!token) {
      console.log('No token available for fetching users');
      return;
    }
    
    console.log('Fetching users from backend using AppGeneraleService...');
    setLoadingUsers(true);
    setUsersError(null);
    
    try {
      const usersData = await AppGeneraleService.getMessagingUsers();
      console.log('Users fetched successfully:', usersData);
      setUsers(usersData);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
        setUsersError(`Server error: ${error.response.status} - ${error.response.data?.error || 'Unknown error'}`);
      } else if (error.request) {
        console.error('Network error:', error.request);
        setUsersError('Network error - please check your connection');
      } else {
        console.error('Other error:', error.message);
        setUsersError('Failed to load users. Please try again.');
      }
    } finally {
      setLoadingUsers(false);
    }
  };

  // Fetch data when component mounts
  useEffect(() => {
    console.log('Notification component mounted, token:', AuthService.getToken() ? 'available' : 'not available');
    if (AuthService.getToken()) {
      fetchNotifications();
      fetchUsers();
    }
  }, []);

  // Filter notifications based on search and filters
  useEffect(() => {
    let filtered = notifications;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(notification =>
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Read/Unread filter
    if (selectedFilter === 'unread') {
      filtered = filtered.filter(notification => notification.unread);
    } else if (selectedFilter === 'read') {
      filtered = filtered.filter(notification => !notification.unread);
    }

    // Type filter
    if (selectedType !== 'all') {
      filtered = filtered.filter(notification => notification.type === selectedType);
    }

    // Priority filter
    if (selectedPriority !== 'all') {
      filtered = filtered.filter(notification => notification.priority === selectedPriority);
    }

    setFilteredNotifications(filtered);
  }, [notifications, searchTerm, selectedFilter, selectedType, selectedPriority]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return AlertCircle;
      case 'success': return CheckCircle;
      case 'error': return X;
      default: return Info;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'success': return 'text-green-600 dark:text-green-400';
      case 'error': return 'text-red-600 dark:text-red-400';
      default: return 'text-blue-600 dark:text-blue-400';
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high': return 'text-red-600 dark:text-red-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-green-600 dark:text-green-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityBgColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high': return 'bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'bg-green-100 dark:bg-green-900/20';
      default: return 'bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const markAsRead = async (id: number) => {
    try {
      const response = await api.patch(`/auth/notifications/${id}/`, {
        is_read: true
      });

      if (response.status === 200) {
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === id ? { ...notification, unread: false } : notification
          )
        );
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await api.post('/auth/notifications/mark-all-read/');

      if (response.status === 200) {
        setNotifications(prev =>
          prev.map(notification => ({ ...notification, unread: false }))
        );
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const deleteNotification = async (id: number) => {
    try {
      const response = await api.delete(`/auth/notifications/${id}/`);

      if (response.status === 204) {
        setNotifications(prev => prev.filter(notification => notification.id !== id));
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  const deleteSelected = async () => {
    try {
      // Delete each selected notification
      for (const id of selectedNotifications) {
        await api.delete(`/auth/notifications/${id}/`);
      }

      setNotifications(prev => prev.filter(notification => !selectedNotifications.includes(notification.id)));
      setSelectedNotifications([]);
    } catch (error) {
      console.error('Error deleting selected notifications:', error);
    }
  };

  const toggleSelection = (id: number) => {
    setSelectedNotifications(prev =>
      prev.includes(id) ? prev.filter(n => n !== id) : [...prev, id]
    );
  };

  const selectAll = () => {
    setSelectedNotifications(filteredNotifications.map(n => n.id));
  };

  const clearSelection = () => {
    setSelectedNotifications([]);
  };

  // Messaging functions
  const handleSendMessage = async () => {
    if (!messageTitle.trim() || !messageContent.trim()) return;

    try {
      const response = await api.post('/auth/messages/', {
        message_type: messageType,
        title: messageTitle,
        content: messageContent,
        priority: messagePriority,
        recipient_ids: messageType === 'individual' ? selectedRecipients : [],
      });

      if (response.status === 201) {
        const data = await response.data;
        console.log('Message sent successfully:', data);
        
        // Refresh notifications to show the new message
        await fetchNotifications();
        
        // Reset form
        setMessageTitle('');
        setMessageContent('');
        setSelectedRecipients([]);
        setShowComposeModal(false);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const toggleRecipient = (userId: number) => {
    setSelectedRecipients(prev =>
      prev.includes(userId) ? prev.filter(id => id !== userId) : [...prev, userId]
    );
  };

  const getUserDisplayName = (user: MessagingUser) => {
    return `${user.first_name} ${user.last_name}`.trim() || user.email;
  };

  const unreadCount = notifications.filter(n => n.unread).length;

  // Fetch message history for a notification
  const fetchMessageHistory = async (notification: Notification) => {
    if (!notification.isMessage) return;
    
    setLoadingHistory(true);
    try {
      const response = await api.get('/auth/messages/history/');
      console.log('Message history fetched:', response.data);
      
      // Find conversation that matches this notification
      const conversations = response.data.conversations;
      const baseTitle = notification.title.replace('Re: ', '').replace('Re:', '');
      
      const matchingConversation = conversations.find((conv: any) => 
        conv.title === baseTitle || conv.title.includes(baseTitle) || baseTitle.includes(conv.title)
      );
      
      if (matchingConversation) {
        setMessageHistory(matchingConversation.messages);
      } else {
        setMessageHistory([]);
      }
    } catch (error) {
      console.error('Error fetching message history:', error);
      setMessageHistory([]);
    } finally {
      setLoadingHistory(false);
    }
  };

  // Open notification detail modal
  const openNotificationDetail = (notification: Notification) => {
    setSelectedNotification(notification);
    setShowNotificationModal(true);
    
    // Mark as read if it's unread
    if (notification.unread) {
      markAsRead(notification.id);
    }
    
    // Fetch message history if it's a message notification
    if (notification.isMessage) {
      fetchMessageHistory(notification);
    }
  };

  // Handle reply to notification
  const handleReply = async () => {
    if (!selectedNotification || !replyContent.trim()) return;

    setSendingReply(true);
    try {
      // Use the sender ID from the notification for the reply
      const recipientIds = selectedNotification.senderId ? [selectedNotification.senderId] : [];
      
      console.log('Sending reply with data:', {
        message_type: 'individual',
        title: `Re: ${selectedNotification.title}`,
        content: replyContent,
        priority: selectedNotification.priority || 'medium',
        recipient_ids: recipientIds,
      });
      
      const response = await api.post('/auth/messages/', {
        message_type: 'individual',
        title: `Re: ${selectedNotification.title}`,
        content: replyContent,
        priority: selectedNotification.priority || 'medium',
        recipient_ids: recipientIds,
      });

      if (response.status === 201) {
        console.log('Reply sent successfully:', response.data);
        
        // Refresh notifications
        await fetchNotifications();
        
        // Refresh message history
        if (selectedNotification.isMessage) {
          await fetchMessageHistory(selectedNotification);
        }
        
        // Reset form and close modal
        setReplyContent('');
        setShowNotificationModal(false);
        setSelectedNotification(null);
      }
    } catch (error: any) {
      console.error('Error sending reply:', error);
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    } finally {
      setSendingReply(false);
    }
  };

  // Close notification modal
  const closeNotificationModal = () => {
    setShowNotificationModal(false);
    setSelectedNotification(null);
    setReplyContent('');
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-dark-900">
      {/* Mobile sidebar overlay */}
      <div 
        className={`fixed inset-0 z-50 lg:hidden transition-opacity duration-300 ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
      >
        <div 
          className="absolute inset-0 bg-black opacity-50"
          onClick={() => setSidebarOpen(false)}
        />
        <div className={`relative w-64 h-full bg-white dark:bg-dark-800 shadow-xl transform transition-transform duration-300 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}>
          <Sidebar />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:block w-64 bg-white dark:bg-dark-800 shadow-lg border-r border-gray-200 dark:border-dark-700">
        <Sidebar />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Main content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-dark-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-xl ${isDark ? 'bg-dark-800' : 'bg-white'} shadow-lg`}>
                    <Bell className={`w-6 h-6 ${isDark ? 'text-primary-400' : 'text-primary-600'}`} />
                  </div>
                  <div>
                    <h1 className={`text-3xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      Notifications
                    </h1>
                    <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                      {unreadCount} unread • {notifications.length} total
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setShowComposeModal(true)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isDark 
                        ? 'bg-primary-600 text-white hover:bg-primary-700' 
                        : 'bg-primary-600 text-white hover:bg-primary-700'
                    }`}
                  >
                    <Plus className="w-4 h-4 mr-2 inline" />
                    New Message
                  </button>
                  <button
                    onClick={markAllAsRead}
                    disabled={unreadCount === 0}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isDark 
                        ? 'bg-dark-800 text-gray-300 hover:bg-dark-700 disabled:opacity-50' 
                        : 'bg-white text-gray-700 hover:bg-gray-50 disabled:opacity-50'
                    }`}
                  >
                    Mark all read
                  </button>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isDark 
                        ? 'bg-dark-800 text-gray-300 hover:bg-dark-700' 
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Filter className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Search and Filters */}
            <div className={`mb-6 space-y-4 ${showFilters ? 'block' : 'hidden'}`}>
              <div className="flex items-center space-x-4">
                {/* Search */}
                <div className="flex-1 relative">
                  <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
                    isDark ? 'text-gray-400' : 'text-gray-500'
                  }`} />
                  <input
                    type="text"
                    placeholder="Search notifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={`w-full pl-10 pr-4 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      isDark 
                        ? 'bg-dark-800 border-dark-600 text-white placeholder-gray-400' 
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                </div>

                {/* Filters */}
                <select
                  value={selectedFilter}
                  onChange={(e) => setSelectedFilter(e.target.value as any)}
                  className={`px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    isDark 
                      ? 'bg-dark-800 border-dark-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="all">All notifications</option>
                  <option value="unread">Unread only</option>
                  <option value="read">Read only</option>
                </select>

                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value as any)}
                  className={`px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    isDark 
                      ? 'bg-dark-800 border-dark-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="all">All types</option>
                  <option value="info">Info</option>
                  <option value="success">Success</option>
                  <option value="warning">Warning</option>
                  <option value="error">Error</option>
                </select>

                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value as any)}
                  className={`px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    isDark 
                      ? 'bg-dark-800 border-dark-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="all">All priorities</option>
                  <option value="high">High priority</option>
                  <option value="medium">Medium priority</option>
                  <option value="low">Low priority</option>
                </select>
              </div>

              {/* Bulk Actions */}
              {selectedNotifications.length > 0 && (
                <div className={`p-4 rounded-lg border ${
                  isDark ? 'bg-dark-800 border-dark-600' : 'bg-white border-gray-200'
                }`}>
                  <div className="flex items-center justify-between">
                    <span className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                      {selectedNotifications.length} notification(s) selected
                    </span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={clearSelection}
                        className={`px-3 py-1 text-sm rounded ${
                          isDark ? 'text-gray-400 hover:text-gray-300' : 'text-gray-600 hover:text-gray-700'
                        }`}
                      >
                        Clear
                      </button>
                      <button
                        onClick={deleteSelected}
                        className="px-3 py-1 text-sm text-red-600 hover:text-red-700 rounded"
                      >
                        Delete selected
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Notifications List */}
            <div className={`space-y-4 ${isDark ? 'text-white' : 'text-gray-900'}`}>
              {loadingNotifications ? (
                <div className={`text-center py-12 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-4"></div>
                  <p>Loading notifications...</p>
                </div>
              ) : notificationsError ? (
                <div className={`text-center py-12 ${isDark ? 'text-red-400' : 'text-red-600'}`}>
                  <p className="mb-2">{notificationsError}</p>
                  <button
                    onClick={fetchNotifications}
                    className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                  >
                    Retry
                  </button>
                </div>
              ) : filteredNotifications.length === 0 ? (
                <div className={`text-center py-12 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">No notifications found</h3>
                  <p className="text-sm">Try adjusting your search or filter criteria</p>
                </div>
              ) : (
                filteredNotifications.map((notification) => {
                  const IconComponent = notification.isMessage ? MessageSquare : getNotificationIcon(notification.type);
                  return (
                    <div
                      key={notification.id}
                      className={`p-6 rounded-xl border transition-all duration-200 cursor-pointer ${
                        isDark 
                          ? 'bg-dark-800 border-dark-700 hover:bg-dark-700' 
                          : 'bg-white border-gray-200 hover:bg-gray-50'
                      } ${
                        notification.unread 
                          ? (isDark ? 'ring-2 ring-primary-500/20' : 'ring-2 ring-primary-200') 
                          : ''
                      }`}
                      onClick={() => openNotificationDetail(notification)}
                    >
                      <div className="flex items-start space-x-4">
                        {/* Selection Checkbox */}
                        <input
                          type="checkbox"
                          checked={selectedNotifications.includes(notification.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            toggleSelection(notification.id);
                          }}
                          className={`mt-1 h-4 w-4 text-primary-500 focus:ring-primary-500 border rounded ${
                            isDark ? 'border-dark-600 bg-dark-700' : 'border-gray-300'
                          }`}
                        />

                        {/* Icon */}
                        <div className={`p-3 rounded-lg ${
                          notification.isMessage ? 'bg-blue-100 dark:bg-blue-900/20' :
                          notification.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                          notification.type === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                          notification.type === 'error' ? 'bg-red-100 dark:bg-red-900/20' :
                          'bg-blue-100 dark:bg-blue-900/20'
                        }`}>
                          <IconComponent className={`w-5 h-5 ${
                            notification.isMessage ? 'text-blue-600 dark:text-blue-400' : getNotificationColor(notification.type)
                          }`} />
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <h3 className={`text-sm font-semibold ${
                                  isDark ? 'text-white' : 'text-gray-900'
                                }`}>
                                  {notification.title}
                                </h3>
                                {notification.unread && (
                                  <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
                                )}
                                {notification.actionable && (
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    isDark ? 'bg-primary-900/20 text-primary-400' : 'bg-primary-100 text-primary-700'
                                  }`}>
                                    Action required
                                  </span>
                                )}
                                {notification.isMessage && (
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    isDark ? 'bg-blue-900/20 text-blue-400' : 'bg-blue-100 text-blue-700'
                                  }`}>
                                    Message
                                  </span>
                                )}
                              </div>
                              <p className={`text-sm mb-3 ${
                                isDark ? 'text-gray-300' : 'text-gray-600'
                              }`}>
                                {notification.message.length > 150 
                                  ? `${notification.message.substring(0, 150)}...` 
                                  : notification.message
                                }
                              </p>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4 text-xs">
                                  <span className={`flex items-center ${
                                    isDark ? 'text-gray-400' : 'text-gray-500'
                                  }`}>
                                    <Clock className="w-3 h-3 mr-1" />
                                    {notification.time}
                                  </span>
                                  {notification.sender && (
                                    <span className={`px-2 py-1 rounded-full ${
                                      isDark ? 'bg-dark-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                                    }`}>
                                      From: {notification.sender}
                                    </span>
                                  )}
                                  {notification.category && (
                                    <span className={`px-2 py-1 rounded-full ${
                                      isDark ? 'bg-dark-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                                    }`}>
                                      {notification.category}
                                    </span>
                                  )}
                                  {notification.priority && (
                                    <span className={`px-2 py-1 rounded-full ${getPriorityBgColor(notification.priority)} ${getPriorityColor(notification.priority)}`}>
                                      {notification.priority}
                                    </span>
                                  )}
                                </div>
                                <div className="flex items-center space-x-2">
                                  {notification.isMessage && notification.canReply && (
                                    <button 
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        openNotificationDetail(notification);
                                      }}
                                      className={`p-1 rounded ${
                                        isDark ? 'hover:bg-dark-700' : 'hover:bg-gray-100'
                                      }`}
                                    >
                                      <MessageSquare className="w-4 h-4" />
                                    </button>
                                  )}
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      markAsRead(notification.id);
                                    }}
                                    className={`p-1 rounded ${
                                      isDark ? 'hover:bg-dark-700' : 'hover:bg-gray-100'
                                    }`}
                                  >
                                    {notification.unread ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      deleteNotification(notification.id);
                                    }}
                                    className="p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 text-red-600"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>

            {/* Pagination or Load More */}
            {filteredNotifications.length > 0 && (
              <div className="mt-8 text-center">
                <button className={`px-6 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isDark 
                    ? 'bg-dark-800 text-gray-300 hover:bg-dark-700' 
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}>
                  Load more notifications
                </button>
              </div>
            )}
          </div>
        </main>

        {/* Footer */}
        <Footer />
      </div>

      {/* Compose Message Modal */}
      {showComposeModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black opacity-50" onClick={() => setShowComposeModal(false)} />
          <div className={`relative w-full max-w-2xl ${isDark ? 'bg-dark-800' : 'bg-white'} rounded-xl shadow-2xl`}>
            <div className="p-6 border-b border-gray-200 dark:border-dark-700">
              <div className="flex items-center justify-between">
                <h3 className={`text-lg font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                  Compose Message
                </h3>
                <button
                  onClick={() => setShowComposeModal(false)}
                  className={`p-2 rounded-lg ${isDark ? 'hover:bg-dark-700' : 'hover:bg-gray-100'}`}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Message Type Selection */}
              <div>
                <label className={`block text-sm font-medium mb-3 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Message Type
                </label>
                <div className="flex space-x-4">
                  <button
                    onClick={() => setMessageType('individual')}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                      messageType === 'individual'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                        : `${isDark ? 'border-dark-600 text-gray-300' : 'border-gray-300 text-gray-700'}`
                    }`}
                  >
                    <Users className="w-4 h-4" />
                    <span>Individual Message</span>
                  </button>
                  <button
                    onClick={() => setMessageType('broadcast')}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
                      messageType === 'broadcast'
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                        : `${isDark ? 'border-dark-600 text-gray-300' : 'border-gray-300 text-gray-700'}`
                    }`}
                  >
                    <Bell className="w-4 h-4" />
                    <span>Broadcast Alert</span>
                  </button>
                </div>
              </div>

              {/* Recipients Selection (for individual messages) */}
              {messageType === 'individual' && (
                <div>
                  <label className={`block text-sm font-medium mb-3 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                    Select Recipients
                  </label>
                  {loadingUsers ? (
                    <div className={`text-center py-8 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto mb-2"></div>
                      <p>Loading users...</p>
                    </div>
                  ) : usersError ? (
                    <div className={`text-center py-8 ${isDark ? 'text-red-400' : 'text-red-600'}`}>
                      <p className="mb-2">{usersError}</p>
                      <button
                        onClick={fetchUsers}
                        className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700"
                      >
                        Retry
                      </button>
                    </div>
                  ) : users.length === 0 ? (
                    <div className={`text-center py-8 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      <p>No users available</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto">
                      {users.map((user) => (
                        <button
                          key={user.id}
                          onClick={() => toggleRecipient(user.id)}
                          className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors text-left ${
                            selectedRecipients.includes(user.id)
                              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                              : `${isDark ? 'border-dark-600 hover:bg-dark-700' : 'border-gray-300 hover:bg-gray-50'}`
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={selectedRecipients.includes(user.id)}
                            onChange={() => toggleRecipient(user.id)}
                            className="h-4 w-4 text-primary-500 focus:ring-primary-500 border rounded"
                          />
                          <div>
                            <p className={`text-sm font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                              {getUserDisplayName(user)}
                            </p>
                            <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                              {user.role?.name || 'No role'} • {user.email}
                            </p>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Priority Selection */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Priority
                </label>
                <select
                  value={messagePriority}
                  onChange={(e) => setMessagePriority(e.target.value as any)}
                  className={`w-full px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    isDark 
                      ? 'bg-dark-700 border-dark-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="low">Low Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="high">High Priority</option>
                </select>
              </div>

              {/* Message Title */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Title
                </label>
                <input
                  type="text"
                  value={messageTitle}
                  onChange={(e) => setMessageTitle(e.target.value)}
                  placeholder="Enter message title..."
                  className={`w-full px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    isDark 
                      ? 'bg-dark-700 border-dark-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>

              {/* Message Content */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                  Message
                </label>
                <textarea
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                  placeholder="Enter your message..."
                  rows={4}
                  className={`w-full px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                    isDark 
                      ? 'bg-dark-700 border-dark-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowComposeModal(false)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isDark ? 'text-gray-300 hover:text-gray-200' : 'text-gray-700 hover:text-gray-800'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSendMessage}
                  disabled={!messageTitle.trim() || !messageContent.trim() || (messageType === 'individual' && selectedRecipients.length === 0)}
                  className="px-4 py-2 rounded-lg text-sm font-medium bg-primary-600 text-white hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="w-4 h-4 mr-2 inline" />
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Notification Detail Modal */}
      {showNotificationModal && selectedNotification && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black opacity-50" onClick={closeNotificationModal} />
          <div className={`relative w-full max-w-2xl ${isDark ? 'bg-dark-800' : 'bg-white'} rounded-xl shadow-2xl`}>
            <div className="p-6 border-b border-gray-200 dark:border-dark-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-3 rounded-lg ${
                    selectedNotification.isMessage ? 'bg-blue-100 dark:bg-blue-900/20' :
                    selectedNotification.type === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                    selectedNotification.type === 'success' ? 'bg-green-100 dark:bg-green-900/20' :
                    selectedNotification.type === 'error' ? 'bg-red-100 dark:bg-red-900/20' :
                    'bg-blue-100 dark:bg-blue-900/20'
                  }`}>
                    {selectedNotification.isMessage ? (
                      <MessageSquare className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    ) : (
                      getNotificationIcon(selectedNotification.type)({ className: `w-6 h-6 ${getNotificationColor(selectedNotification.type)}` })
                    )}
                  </div>
                  <div>
                    <h3 className={`text-lg font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                      {selectedNotification.title}
                    </h3>
                    <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      {selectedNotification.time}
                    </p>
                  </div>
                </div>
                <button
                  onClick={closeNotificationModal}
                  className={`p-2 rounded-lg ${isDark ? 'hover:bg-dark-700' : 'hover:bg-gray-100'}`}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Notification Details */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4 text-sm">
                  {selectedNotification.sender && (
                    <span className={`px-3 py-1 rounded-full ${
                      isDark ? 'bg-dark-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                    }`}>
                      From: {selectedNotification.sender}
                    </span>
                  )}
                  {selectedNotification.category && (
                    <span className={`px-3 py-1 rounded-full ${
                      isDark ? 'bg-dark-700 text-gray-300' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {selectedNotification.category}
                    </span>
                  )}
                  {selectedNotification.priority && (
                    <span className={`px-3 py-1 rounded-full ${getPriorityBgColor(selectedNotification.priority)} ${getPriorityColor(selectedNotification.priority)}`}>
                      {selectedNotification.priority} Priority
                    </span>
                  )}
                </div>

                <div className={`p-4 rounded-lg ${
                  isDark ? 'bg-dark-700' : 'bg-gray-50'
                }`}>
                  <p className={`text-sm leading-relaxed ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    {selectedNotification.message}
                  </p>
                </div>
              </div>

              {/* Message History (only for message notifications) */}
              {selectedNotification.isMessage && (
                <div className="border-t border-gray-200 dark:border-dark-700 pt-6">
                  <h4 className={`text-sm font-medium mb-3 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                    Message History
                  </h4>
                  
                  {loadingHistory ? (
                    <div className="text-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto mb-2"></div>
                      <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>Loading conversation...</p>
                    </div>
                  ) : messageHistory.length > 0 ? (
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {messageHistory.map((message: any, index: number) => (
                        <div
                          key={message.id}
                          className={`p-3 rounded-lg border ${
                            message.sender.id === selectedNotification.senderId
                              ? isDark ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
                              : isDark ? 'bg-dark-700 border-dark-600' : 'bg-gray-100 border-gray-200'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className={`text-xs font-medium ${
                              message.sender.id === selectedNotification.senderId
                                ? 'text-blue-600 dark:text-blue-400'
                                : isDark ? 'text-gray-300' : 'text-gray-600'
                            }`}>
                              {message.sender.first_name} {message.sender.last_name}
                            </span>
                            <span className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                              {formatTimeAgo(new Date(message.created_at))}
                            </span>
                          </div>
                          <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                            {message.content}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className={`text-center py-4 ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                      <p className="text-sm">No conversation history found</p>
                    </div>
                  )}
                </div>
              )}

              {/* Reply Section (only for messages that can be replied to) */}
              {selectedNotification.isMessage && selectedNotification.canReply && (
                <div className="border-t border-gray-200 dark:border-dark-700 pt-6">
                  <h4 className={`text-sm font-medium mb-3 ${isDark ? 'text-gray-200' : 'text-gray-700'}`}>
                    Reply to this message
                  </h4>
                  <textarea
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                    placeholder="Type your reply..."
                    rows={4}
                    className={`w-full px-3 py-2 rounded-lg border focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                      isDark 
                        ? 'bg-dark-700 border-dark-600 text-white placeholder-gray-400' 
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    }`}
                  />
                  <div className="flex items-center justify-end space-x-3 mt-3">
                    <button
                      onClick={closeNotificationModal}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isDark ? 'text-gray-300 hover:text-gray-200' : 'text-gray-700 hover:text-gray-800'
                      }`}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleReply}
                      disabled={!replyContent.trim() || sendingReply}
                      className="px-4 py-2 rounded-lg text-sm font-medium bg-primary-600 text-white hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {sendingReply ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline"></div>
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2 inline" />
                          Send Reply
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-dark-700">
                <div className="flex items-center space-x-2">
                  {selectedNotification.href && (
                    <button className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isDark 
                        ? 'bg-dark-700 text-gray-300 hover:bg-dark-600' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}>
                      View Details
                    </button>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      markAsRead(selectedNotification.id);
                      closeNotificationModal();
                    }}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isDark 
                        ? 'bg-dark-700 text-gray-300 hover:bg-dark-600' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Mark as Read
                  </button>
                  <button
                    onClick={() => {
                      deleteNotification(selectedNotification.id);
                      closeNotificationModal();
                    }}
                    className="px-4 py-2 rounded-lg text-sm font-medium bg-red-600 text-white hover:bg-red-700 transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Notification;
