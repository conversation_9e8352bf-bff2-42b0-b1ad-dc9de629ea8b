import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>ontent,
  Badge,
  Progress
} from '../../components/ui';
import {
  TrendChart,
  DistributionChart
} from '../../components/analytics';
import { 
  BarChart3, 
  Activity,
  TrendingUp,
  Target,
  CheckCircle,
  Clock,
  AlertCircle,
  Database
} from 'lucide-react';

interface AnalysisSummary {
  totalAnalyses: number;
  completedAnalyses: number;
  runningAnalyses: number;
  failedAnalyses: number;
  averageProcessingTime: string;
  totalDataProcessed: string;
  accuracyRate: number;
}

interface RecentActivity {
  id: string;
  type: 'analysis' | 'publication' | 'data_import';
  title: string;
  description: string;
  timestamp: string;
  status: 'completed' | 'running' | 'failed';
  progress?: number;
}

const AnalyticDashboard: React.FC = () => {
  const summary: AnalysisSummary = {
    totalAnalyses: 156,
    completedAnalyses: 142,
    runningAnalyses: 8,
    failedAnalyses: 6,
    averageProcessingTime: '2.3 minutes',
    totalDataProcessed: '2.4 GB',
    accuracyRate: 94.2
  };

  const recentActivities: RecentActivity[] = [
    {
      id: '1',
      type: 'analysis',
      title: 'Price Trend Analysis',
      description: 'Completed analysis of property price trends',
      timestamp: '2 minutes ago',
      status: 'completed'
    },
    {
      id: '2',
      type: 'publication',
      title: 'Q4 Market Report',
      description: 'Published to BordDecision dashboard',
      timestamp: '15 minutes ago',
      status: 'completed'
    },
    {
      id: '3',
      type: 'analysis',
      title: 'Market Segmentation',
      description: 'Statistical analysis of market segments',
      timestamp: '1 hour ago',
      status: 'running',
      progress: 65
    },
    {
      id: '4',
      type: 'data_import',
      title: 'Real Estate Data Import',
      description: 'Imported 15,420 records from CSV file',
      timestamp: '2 hours ago',
      status: 'completed'
    }
  ];

  const trendData = [
    { time: 'Mon', value: 12, target: 10 },
    { time: 'Tue', value: 15, target: 10 },
    { time: 'Wed', value: 18, target: 10 },
    { time: 'Thu', value: 22, target: 10 },
    { time: 'Fri', value: 25, target: 10 },
    { time: 'Sat', value: 20, target: 10 },
    { time: 'Sun', value: 16, target: 10 },
  ];

  const distributionData = [
    { name: 'Completed', value: 142, color: '#10b981' },
    { name: 'Running', value: 8, color: '#3b82f6' },
    { name: 'Failed', value: 6, color: '#ef4444' },
  ];

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'analysis':
        return <BarChart3 className="w-4 h-4" />;
      case 'publication':
        return <TrendingUp className="w-4 h-4" />;
      case 'data_import':
        return <Database className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: RecentActivity['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: RecentActivity['status']) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'info';
      case 'failed':
        return 'error';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-800">
          <CardHeader title="Total Analyses" className="pb-2" />
          <CardContent>
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">156</div>
            <div className="text-sm text-blue-600 dark:text-blue-400 font-medium">All time</div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-800">
          <CardHeader title="Completed" className="pb-2" />
          <CardContent>
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">142</div>
            <div className="text-sm text-green-600 dark:text-green-400 font-medium">Successful analyses</div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-orange-200 dark:border-orange-800">
          <CardHeader title="Running" className="pb-2" />
          <CardContent>
            <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">8</div>
            <div className="text-sm text-orange-600 dark:text-orange-400 font-medium">Currently processing</div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-800">
          <CardHeader title="Accuracy Rate" className="pb-2" />
          <CardContent>
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">94.2%</div>
            <div className="text-sm text-purple-600 dark:text-purple-400 font-medium">Overall accuracy</div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
          <CardHeader 
            title="Analysis Activity"
            subtitle="Daily analysis completion trend"
            badge={<Badge variant="success" size="sm">Live</Badge>}
          />
          <CardContent>
            <div className="mb-4 flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-green-600">+33.3% vs previous period</span>
            </div>
            <TrendChart
              data={trendData}
              lines={[
                { dataKey: 'value', name: 'Analyses Completed', color: '#3b82f6' },
                { dataKey: 'target', name: 'Target', color: '#ef4444', strokeDasharray: '5 5' }
              ]}
              height={300}
              showTrendIndicator
              showReferenceLine
              referenceValue={10}
            />
          </CardContent>
        </Card>

        <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
          <CardHeader 
            title="Analysis Status Distribution"
            subtitle="Current analysis status breakdown"
            badge={<Badge variant="info" size="sm">Updated</Badge>}
          />
          <CardContent>
            <DistributionChart
              data={distributionData}
              showLabels
              showLegend
            />
          </CardContent>
        </Card>
      </div>

      {/* System Performance */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader title="System Performance" />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/10 dark:to-blue-800/10 rounded-xl border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full mx-auto mb-3">
                <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{summary.averageProcessingTime}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Average Processing Time</p>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/10 dark:to-green-800/10 rounded-xl border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full mx-auto mb-3">
                <Database className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{summary.totalDataProcessed}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Data Processed</p>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/10 dark:to-purple-800/10 rounded-xl border border-purple-200 dark:border-purple-800">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-full mx-auto mb-3">
                <Target className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{summary.accuracyRate}%</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Accuracy Rate</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Recent Activity"
          subtitle="Latest analysis and publication activities"
        />
        <CardContent>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-white dark:bg-dark-800 rounded-lg shadow-sm">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{activity.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{activity.description}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">{activity.timestamp}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge variant={getStatusColor(activity.status)}>
                    {getStatusIcon(activity.status)}
                    <span className="ml-1">{activity.status}</span>
                  </Badge>
                  
                  {activity.status === 'running' && activity.progress && (
                    <div className="w-24">
                      <Progress value={activity.progress} max={100} size="sm" />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/10 dark:to-blue-800/10 rounded-xl border border-blue-200 dark:border-blue-800 text-center hover:shadow-lg transition-all duration-200 cursor-pointer group">
              <BarChart3 className="w-8 h-8 text-blue-600 dark:text-blue-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <h3 className="font-semibold text-gray-900 dark:text-white">New Analysis</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Start a new analysis workflow</p>
            </div>
            
            <div className="p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/10 dark:to-green-800/10 rounded-xl border border-green-200 dark:border-green-800 text-center hover:shadow-lg transition-all duration-200 cursor-pointer group">
              <Database className="w-8 h-8 text-green-600 dark:text-green-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <h3 className="font-semibold text-gray-900 dark:text-white">Import Data</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Upload new datasets for analysis</p>
            </div>
            
            <div className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/10 dark:to-purple-800/10 rounded-xl border border-purple-200 dark:border-purple-800 text-center hover:shadow-lg transition-all duration-200 cursor-pointer group">
              <TrendingUp className="w-8 h-8 text-purple-600 dark:text-purple-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <h3 className="font-semibold text-gray-900 dark:text-white">Publish Results</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Share analysis results</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticDashboard;
