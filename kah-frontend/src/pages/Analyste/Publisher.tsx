import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>nt,
  Button,
  Badge,
  Select,
  Input
} from '../../components/ui';
import { 
  Share, 
  Download,
  Eye,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  TrendingUp,
  Target
} from 'lucide-react';

interface Publication {
  id: string;
  title: string;
  description: string;
  target: 'BordDecision' | 'OperationBord' | 'both';
  status: 'draft' | 'published' | 'scheduled' | 'error';
  createdAt: string;
  publishedAt?: string;
  scheduledFor?: string;
  recipients: string[];
  data: {
    charts: number;
    reports: number;
    datasets: number;
  };
}

interface DashboardConfig {
  id: string;
  name: string;
  description: string;
  type: 'decision' | 'operational';
  lastUpdated: string;
  status: 'online' | 'offline' | 'maintenance';
}

const Publisher: React.FC = () => {
  const [publications, setPublications] = useState<Publication[]>([
    {
      id: '1',
      title: 'Q4 Market Analysis Report',
      description: 'Comprehensive analysis of real estate market trends for Q4 2024',
      target: '<PERSON>rdDecision',
      status: 'published',
      createdAt: '2024-01-15 10:30',
      publishedAt: '2024-01-15 11:00',
      recipients: ['<EMAIL>', '<EMAIL>'],
      data: {
        charts: 8,
        reports: 3,
        datasets: 2
      }
    },
    {
      id: '2',
      title: 'Weekly Operations Dashboard',
      description: 'Weekly operational metrics and performance indicators',
      target: 'OperationBord',
      status: 'scheduled',
      createdAt: '2024-01-15 09:15',
      scheduledFor: '2024-01-16 08:00',
      recipients: ['<EMAIL>'],
      data: {
        charts: 12,
        reports: 5,
        datasets: 4
      }
    }
  ]);

  const [dashboards] = useState<DashboardConfig[]>([
    {
      id: '1',
      name: 'BordDecision',
      description: 'Decision-making dashboard for executives and board members',
      type: 'decision',
      lastUpdated: '2024-01-15 11:00',
      status: 'online'
    },
    {
      id: '2',
      name: 'OperationBord',
      description: 'Operational dashboard for day-to-day management',
      type: 'operational',
      lastUpdated: '2024-01-15 10:30',
      status: 'online'
    }
  ]);

  const [selectedTarget, setSelectedTarget] = useState<string>('');
  const [publicationTitle, setPublicationTitle] = useState('');
  const [publicationDescription, setPublicationDescription] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');

  const getStatusIcon = (status: Publication['status']) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'scheduled':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: Publication['status']) => {
    switch (status) {
      case 'published':
        return 'success';
      case 'scheduled':
        return 'info';
      case 'error':
        return 'error';
      default:
        return 'secondary';
    }
  };

  const handlePublish = () => {
    if (!publicationTitle || !selectedTarget) {
      return;
    }

    const newPublication: Publication = {
      id: Date.now().toString(),
      title: publicationTitle,
      description: publicationDescription,
      target: selectedTarget as 'BordDecision' | 'OperationBord' | 'both',
      status: scheduledDate ? 'scheduled' : 'published',
      createdAt: new Date().toLocaleString(),
      publishedAt: scheduledDate ? undefined : new Date().toLocaleString(),
      scheduledFor: scheduledDate || undefined,
      recipients: [],
      data: {
        charts: Math.floor(Math.random() * 10) + 1,
        reports: Math.floor(Math.random() * 5) + 1,
        datasets: Math.floor(Math.random() * 3) + 1
      }
    };

    setPublications(prev => [newPublication, ...prev]);
    
    // Reset form
    setPublicationTitle('');
    setPublicationDescription('');
    setSelectedTarget('');
    setScheduledDate('');
  };

  return (
    <div className="p-6 space-y-6">
      {/* Dashboard Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {dashboards.map((dashboard) => (
          <Card key={dashboard.id} className="border-gray-200 dark:border-dark-700 shadow-sm">
            <CardHeader 
              title={dashboard.name}
              subtitle={dashboard.description}
              action={
                <Badge variant={dashboard.status === 'online' ? 'success' : 'error'} size="sm">
                  {dashboard.status}
                </Badge>
              }
            />
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {dashboard.type === 'decision' ? (
                    <Target className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  ) : (
                    <BarChart3 className="w-5 h-5 text-green-600 dark:text-green-400" />
                  )}
                  <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">{dashboard.type}</span>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Updated: {dashboard.lastUpdated}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Create Publication */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Create New Publication"
          subtitle="Publish analysis results to target dashboards"
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Publication Title
              </label>
              <Input
                value={publicationTitle}
                onChange={(e) => setPublicationTitle(e.target.value)}
                placeholder="Enter publication title"
                className="border-gray-300 dark:border-gray-600 focus:border-primary-500 dark:focus:border-primary-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Target Dashboard
              </label>
              <Select
                value={selectedTarget}
                onValueChange={setSelectedTarget}
                options={[
                  { value: 'BordDecision', label: 'BordDecision (Decision Board)' },
                  { value: 'OperationBord', label: 'OperationBord (Operations Board)' },
                  { value: 'both', label: 'Both Dashboards' }
                ]}
              />
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <Input
                value={publicationDescription}
                onChange={(e) => setPublicationDescription(e.target.value)}
                placeholder="Enter publication description"
                className="border-gray-300 dark:border-gray-600 focus:border-primary-500 dark:focus:border-primary-400"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Schedule Publication (Optional)
              </label>
              <Input
                type="datetime-local"
                value={scheduledDate}
                onChange={(e) => setScheduledDate(e.target.value)}
                className="border-gray-300 dark:border-gray-600 focus:border-primary-500 dark:focus:border-primary-400"
              />
            </div>
            <div className="flex items-end">
              <Button 
                variant="primary" 
                leftIcon={<Send className="w-4 h-4" />}
                onClick={handlePublish}
                disabled={!publicationTitle || !selectedTarget}
                className="w-full"
              >
                {scheduledDate ? 'Schedule Publication' : 'Publish Now'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Publications List */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Recent Publications"
          subtitle="Published and scheduled analysis results"
        />
        <CardContent>
          <div className="space-y-4">
            {publications.map((publication) => (
              <div key={publication.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg shadow-sm">
                    <Share className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{publication.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{publication.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>Target: {publication.target}</span>
                      <span>Created: {publication.createdAt}</span>
                      {publication.publishedAt && (
                        <span>Published: {publication.publishedAt}</span>
                      )}
                      {publication.scheduledFor && (
                        <span>Scheduled: {publication.scheduledFor}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge variant={getStatusColor(publication.status)}>
                    {getStatusIcon(publication.status)}
                    <span className="ml-1">{publication.status}</span>
                  </Badge>
                  
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-4 h-4" />
                      <span>{publication.data.charts} charts</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="w-4 h-4" />
                      <span>{publication.data.reports} reports</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                      Preview
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                      Export
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Success Alert */}
      {publications.length > 0 && (
        <div className="bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Publication Successful</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Your analysis results have been successfully published to the target dashboards.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Publisher;
