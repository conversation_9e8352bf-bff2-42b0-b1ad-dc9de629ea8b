import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON>, 
  <PERSON>Header, 
  CardContent,
  Button,
  Badge
} from '../../components/ui';
import { 
  ArrowLeft,
  Database,
  Eye,
  Download,
  AlertCircle,
  Activity,
  Table,
  BarChart3
} from 'lucide-react';
import analyticsService, { DataFramePreview, DataFrameTable } from '../../services/analyticsService';

const DataPreview: React.FC = () => {
  const navigate = useNavigate();
  const [dataframes, setDataframes] = useState<Record<string, DataFramePreview>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDataframe, setSelectedDataframe] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<string | null>(null);

  useEffect(() => {
    loadDataFramesPreview();
  }, []);

  const loadDataFramesPreview = async () => {
    try {
      setIsLoading(true);
      const response = await analyticsService.getDataFramesPreview();
      setDataframes(response.dataframes);
      
      // Sélectionner le premier dataframe par défaut
      const firstKey = Object.keys(response.dataframes)[0];
      if (firstKey) {
        setSelectedDataframe(firstKey);
        // Sélectionner la première table si elle existe
        const firstDataframe = response.dataframes[firstKey];
        if (firstDataframe.tables) {
          const firstTableKey = Object.keys(firstDataframe.tables)[0];
          if (firstTableKey) {
            setSelectedTable(firstTableKey);
          }
        }
      }
    } catch (error) {
      console.error('Erreur lors du chargement des previews:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDataType = (type: string) => {
    const typeMap: Record<string, string> = {
      'object': 'Text',
      'int64': 'Integer',
      'float64': 'Float',
      'datetime64[ns]': 'DateTime',
      'bool': 'Boolean'
    };
    return typeMap[type] || type;
  };

  const getDataTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'object': 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
      'int64': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
      'float64': 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400',
      'datetime64[ns]': 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400',
      'bool': 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
    };
    return colorMap[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center py-12">
          <div className="flex items-center space-x-3">
            <Activity className="w-6 h-6 text-blue-500 animate-spin" />
            <span className="text-lg text-gray-600 dark:text-gray-400">Loading dataframes preview...</span>
          </div>
        </div>
      </div>
    );
  }

  const selectedData = selectedDataframe ? dataframes[selectedDataframe] : null;
  const selectedTableData = selectedData && selectedTable && selectedData.tables ? selectedData.tables[selectedTable] : null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="ghost" 
            leftIcon={<ArrowLeft className="w-4 h-4" />}
            onClick={() => navigate('/analyst/donnee')}
          >
            Back to Data Sources
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Data Preview</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Preview of all available dataframes from internal applications
            </p>
          </div>
        </div>
        <Button 
          variant="outline" 
          leftIcon={<Download className="w-4 h-4" />}
        >
          Export All
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar - Liste des dataframes */}
        <div className="lg:col-span-1">
          <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
            <CardHeader
              title="Available DataFrames"
              subtitle={`${Object.keys(dataframes).length} sources loaded`}
            />
            <CardContent>
              <div className="space-y-2">
                {Object.entries(dataframes).map(([key, df]) => (
                  <div key={key} className="space-y-1">
                    <button
                      onClick={() => {
                        setSelectedDataframe(key);
                        // Sélectionner la première table si elle existe
                        if (df.tables && Object.keys(df.tables).length > 0) {
                          setSelectedTable(Object.keys(df.tables)[0]);
                        } else {
                          setSelectedTable(null);
                        }
                      }}
                      className={`w-full text-left p-3 rounded-lg border transition-all duration-200 ${
                        selectedDataframe === key
                          ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                          : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${
                          df.error
                            ? 'bg-red-100 dark:bg-red-900/20'
                            : 'bg-green-100 dark:bg-green-900/20'
                        }`}>
                          {df.error ? (
                            <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
                          ) : (
                            <Table className="w-4 h-4 text-green-600 dark:text-green-400" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 dark:text-white truncate">
                            {df.name || key}
                          </h4>
                          {!df.error && df.tables && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {Object.keys(df.tables).length} tables
                            </p>
                          )}
                          {df.error && (
                            <p className="text-sm text-red-500 dark:text-red-400">
                              {df.error}
                            </p>
                          )}
                        </div>
                      </div>
                    </button>

                    {/* Sous-tables si sélectionné */}
                    {selectedDataframe === key && df.tables && (
                      <div className="ml-4 space-y-1">
                        {Object.entries(df.tables).map(([tableKey, table]) => (
                          <button
                            key={tableKey}
                            onClick={() => setSelectedTable(tableKey)}
                            className={`w-full text-left p-2 rounded border text-sm transition-all duration-200 ${
                              selectedTable === tableKey
                                ? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700'
                                : 'bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-gray-900 dark:text-white">
                                {table.name}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {table.shape[0]} × {table.shape[1]}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content - Détails du dataframe sélectionné */}
        <div className="lg:col-span-3">
          {selectedData ? (
            <div className="space-y-6">
              {selectedData.error ? (
                <Card className="border-red-200 dark:border-red-800 shadow-sm">
                  <CardContent>
                    <div className="flex items-center space-x-3 p-4">
                      <AlertCircle className="w-6 h-6 text-red-600 dark:text-red-400" />
                      <div>
                        <h3 className="font-semibold text-red-900 dark:text-red-400">Error Loading DataFrame</h3>
                        <p className="text-sm text-red-700 dark:text-red-300">{selectedData.error}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : selectedTableData ? (
                <>
                  {/* Table Info */}
                  <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
                    <CardHeader
                      title={selectedTableData.name}
                      subtitle={`${selectedTableData.description || 'Table data'} - Last updated: ${new Date(selectedData.last_updated).toLocaleString('fr-FR')}`}
                    />
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {selectedTableData.shape[0].toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Rows</div>
                        </div>
                        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                            {selectedTableData.shape[1]}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Columns</div>
                        </div>
                        <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {Object.keys(selectedTableData.data_types).length}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Data Types</div>
                        </div>
                        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                            {selectedTableData.columns.length}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">Features</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Column Information */}
                  <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
                    <CardHeader
                      title="Column Information"
                      subtitle="Data types and column details"
                    />
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {selectedTableData.columns.map((column, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <span className="font-medium text-gray-900 dark:text-white truncate">
                              {column}
                            </span>
                            <Badge
                              variant="secondary"
                              className={`ml-2 ${getDataTypeColor(selectedTableData.data_types[column])}`}
                            >
                              {formatDataType(selectedTableData.data_types[column])}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Sample Data */}
                  <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
                    <CardHeader
                      title="Sample Data"
                      subtitle={`First ${selectedTableData.sample_data.length} rows of the dataset`}
                    />
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b border-gray-200 dark:border-gray-700">
                              {selectedTableData.columns.map((column, index) => (
                                <th key={index} className="text-left p-3 font-medium text-gray-900 dark:text-white">
                                  {column}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {selectedTableData.sample_data.map((row, rowIndex) => (
                              <tr key={rowIndex} className="border-b border-gray-100 dark:border-gray-800">
                                {selectedTableData.columns.map((column, colIndex) => (
                                  <td key={colIndex} className="p-3 text-gray-700 dark:text-gray-300">
                                    {row[column] !== null && row[column] !== undefined
                                      ? String(row[column]).substring(0, 50) + (String(row[column]).length > 50 ? '...' : '')
                                      : <span className="text-gray-400 italic">null</span>
                                    }
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
                  <CardContent>
                    <div className="text-center py-12">
                      <Table className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        Select a Table
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400">
                        Choose a table from the sidebar to view its details and sample data.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
              <CardContent>
                <div className="text-center py-12">
                  <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Select a DataFrame
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Choose a dataframe from the sidebar to view its details and sample data.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default DataPreview;
