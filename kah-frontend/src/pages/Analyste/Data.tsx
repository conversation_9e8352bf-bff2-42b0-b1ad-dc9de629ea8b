import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardContent,
  Button,
  Badge,
  Progress
} from '../../components/ui';
import {
  Upload,
  FileSpreadsheet,
  Database,
  Download,
  Eye,
  Trash2,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw,
  Play,
  Activity
} from 'lucide-react';
import analyticsService, { DataSource } from '../../services/analyticsService';

interface DataFile {
  id: string;
  name: string;
  size: string;
  type: 'csv' | 'xlsx';
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  uploadedAt: string;
  records: number;
}

const Data: React.FC = () => {
  const navigate = useNavigate();
  const [uploadedFiles, setUploadedFiles] = useState<DataFile[]>([
    {
      id: '1',
      name: 'real_estate_data.csv',
      size: '2.4 MB',
      type: 'csv',
      status: 'completed',
      progress: 100,
      uploadedAt: '2024-01-15 10:30',
      records: 15420
    },
    {
      id: '2',
      name: 'market_analysis.xlsx',
      size: '1.8 MB',
      type: 'xlsx',
      status: 'processing',
      progress: 65,
      uploadedAt: '2024-01-15 11:45',
      records: 8920
    }
  ]);

  // États pour les données backend
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(true);
  const [isCollecting, setIsCollecting] = useState(false);
  const [collectingSourceId, setCollectingSourceId] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Charger les sources de données au montage du composant
  useEffect(() => {
    loadDataSources();
  }, []);

  const loadDataSources = async () => {
    try {
      setIsLoadingDataSources(true);
      const response = await analyticsService.getDataSourcesStatus();
      setDataSources(response.sources);
    } catch (error) {
      console.error('Erreur lors du chargement des sources:', error);
    } finally {
      setIsLoadingDataSources(false);
    }
  };

  const handleCollectData = async (sourceId?: string) => {
    try {
      if (sourceId) {
        // Collecte d'une source spécifique
        setCollectingSourceId(sourceId);
        console.log(`🚀 Collecte de la source: ${sourceId}`);

        const response = await analyticsService.collectSingleSource(sourceId);

        // Simuler le processus de collecte
        setTimeout(async () => {
          await loadDataSources(); // Recharger les données
          setCollectingSourceId(null);

          // Afficher un message de succès
          console.log('✅ Collecte terminée pour', sourceId, ':', response.message);
        }, 2000);

      } else {
        // Collecte globale de toutes les sources
        setIsCollecting(true);
        console.log('🚀 Collecte globale de toutes les sources');

        const response = await analyticsService.collectAllData();

        // Simuler le processus de collecte
        setTimeout(async () => {
          await loadDataSources(); // Recharger les données
          setIsCollecting(false);

          // Afficher un message de succès
          console.log('✅ Collecte globale terminée:', response.message);
        }, 5000);
      }

    } catch (error) {
      console.error('Erreur lors de la collecte:', error);
      setIsCollecting(false);
      setCollectingSourceId(null);
    }
  };

  const handlePreviewData = async (sourceId?: string) => {
    try {
      if (sourceId) {
        // Preview d'une source spécifique
        navigate(`/analyst/data-preview?source=${sourceId}`);
      } else {
        // Preview de toutes les sources
        navigate('/analyst/data-preview');
      }
    } catch (error) {
      console.error('Erreur lors de la navigation:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setIsUploading(true);
      
      // Simulate file upload
      setTimeout(() => {
        const newFile: DataFile = {
          id: Date.now().toString(),
          name: files[0].name,
          size: `${(files[0].size / 1024 / 1024).toFixed(1)} MB`,
          type: files[0].name.endsWith('.csv') ? 'csv' : 'xlsx',
          status: 'uploading',
          progress: 0,
          uploadedAt: new Date().toLocaleString(),
          records: 0
        };
        
        setUploadedFiles(prev => [...prev, newFile]);
        
        // Simulate upload progress
        const interval = setInterval(() => {
          setUploadedFiles(prev => 
            prev.map(file => 
              file.id === newFile.id 
                ? { ...file, progress: Math.min(file.progress + 20, 100) }
                : file
            )
          );
        }, 500);

        setTimeout(() => {
          clearInterval(interval);
          setUploadedFiles(prev => 
            prev.map(file => 
              file.id === newFile.id 
                ? { ...file, status: 'completed', progress: 100, records: Math.floor(Math.random() * 10000) + 1000 }
                : file
            )
          );
          setIsUploading(false);
        }, 3000);
      }, 1000);
    }
  };

  // Fonctions pour les fichiers uploadés
  const getFileStatusIcon = (status: DataFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getFileStatusColor = (status: DataFile['status']) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'info';
      case 'error':
        return 'error';
      default:
        return 'secondary';
    }
  };

  // Fonctions pour les sources de données
  const getDataSourceStatusColor = (status: DataSource['status']) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'no_data':
        return 'secondary';
      case 'error':
        return 'error';
      default:
        return 'secondary';
    }
  };

  const getDataSourceStatusIcon = (status: DataSource['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'no_data':
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* File Upload Section - Simplifié */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader
          title="Import Data Files"
          subtitle="Upload CSV or Excel files for analysis"
        />
        <CardContent>
          <div className="flex justify-center">
            <input
              type="file"
              accept=".csv,.xlsx,.xls"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
              multiple
            />
            <label htmlFor="file-upload">
              <Button
                variant="primary"
                leftIcon={<Upload className="w-4 h-4" />}
                className="px-8 py-3"
              >
                Import Data Files
              </Button>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Uploaded Files"
          subtitle="Files imported for analysis"
        />
        <CardContent>
          <div className="space-y-4">
            {uploadedFiles.map((file) => (
              <div key={file.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg shadow-sm">
                    <FileSpreadsheet className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{file.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {file.size} • {file.records.toLocaleString()} records • {file.uploadedAt}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge variant={getFileStatusColor(file.status)}>
                    {getFileStatusIcon(file.status)}
                    <span className="ml-1">{file.status}</span>
                  </Badge>
                  
                  {file.status === 'processing' && (
                    <div className="w-24">
                      <Progress value={file.progress} max={100} size="sm" />
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                      Preview
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                      Download
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Trash2 className="w-4 h-4" />}>
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Internal Data Sources */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader
          title="Internal Data Sources"
          subtitle="Available data from KAYDAN applications"
          action={
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                leftIcon={<RefreshCw className="w-4 h-4" />}
                onClick={loadDataSources}
                disabled={isLoadingDataSources}
              >
                Refresh
              </Button>
              <Button
                variant="primary"
                size="sm"
                leftIcon={<Eye className="w-4 h-4" />}
                onClick={handlePreviewData}
              >
                Preview All
              </Button>
            </div>
          }
        />
        <CardContent>
          {isLoadingDataSources ? (
            <div className="flex justify-center items-center py-8">
              <div className="flex items-center space-x-3">
                <Activity className="w-5 h-5 text-blue-500 animate-spin" />
                <span className="text-gray-600 dark:text-gray-400">Loading data sources...</span>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dataSources.map((source) => (
                <div
                  key={source.id}
                  className={`p-4 rounded-xl border hover:shadow-lg transition-all duration-200 ${
                    source.status === 'available'
                      ? 'bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/10 dark:to-green-800/10 border-green-200 dark:border-green-800'
                      : source.status === 'no_data'
                      ? 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/10 dark:to-gray-800/10 border-gray-200 dark:border-gray-700'
                      : 'bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/10 dark:to-red-800/10 border-red-200 dark:border-red-800'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className={`p-2 rounded-lg shadow-sm ${
                      source.status === 'available'
                        ? 'bg-green-100 dark:bg-green-900/20'
                        : source.status === 'no_data'
                        ? 'bg-gray-100 dark:bg-gray-900/20'
                        : 'bg-red-100 dark:bg-red-900/20'
                    }`}>
                      <Database className={`w-5 h-5 ${
                        source.status === 'available'
                          ? 'text-green-600 dark:text-green-400'
                          : source.status === 'no_data'
                          ? 'text-gray-600 dark:text-gray-400'
                          : 'text-red-600 dark:text-red-400'
                      }`} />
                    </div>
                    <Badge variant={getDataSourceStatusColor(source.status)} size="sm">
                      {getDataSourceStatusIcon(source.status)}
                      <span className="ml-1 capitalize">{source.status.replace('_', ' ')}</span>
                    </Badge>
                  </div>

                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{source.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{source.description}</p>

                  <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex justify-between">
                      <span>Records:</span>
                      <span className="font-medium text-gray-900 dark:text-white">{source.records.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Updated:</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {source.last_updated
                          ? new Date(source.last_updated).toLocaleDateString('fr-FR')
                          : 'Never'
                        }
                      </span>
                    </div>
                  </div>

                  <div className="flex space-x-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      leftIcon={<Eye className="w-4 h-4" />}
                      onClick={() => handlePreviewData(source.id)}
                      disabled={source.status !== 'available'}
                    >
                      Preview
                    </Button>
                    <Button
                      variant="primary"
                      size="sm"
                      leftIcon={
                        collectingSourceId === source.id
                          ? <Activity className="w-4 h-4 animate-spin" />
                          : <Play className="w-4 h-4" />
                      }
                      onClick={() => handleCollectData(source.id)}
                      disabled={collectingSourceId === source.id || isCollecting}
                    >
                      {collectingSourceId === source.id ? 'Collecting...' : 'Collect'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Alerts */}
      {isUploading && (
        <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Uploading Files</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Please wait while your files are being processed...</p>
            </div>
          </div>
        </div>
      )}

      {isCollecting && (
        <div className="bg-green-50 dark:bg-green-900/10 border border-green-200 dark:border-green-800 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Activity className="w-5 h-5 text-green-600 dark:text-green-400 animate-spin" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Collecting Data</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Collecting data from all internal applications. This may take a few minutes...
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Global Collect Button */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardContent>
          <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/10 dark:to-blue-800/10 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Database className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">Collect All Data</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Trigger data collection from all internal applications
                </p>
              </div>
            </div>
            <Button
              variant="primary"
              leftIcon={
                isCollecting
                  ? <Activity className="w-4 h-4 animate-spin" />
                  : <Play className="w-4 h-4" />
              }
              onClick={() => handleCollectData()}
              disabled={isCollecting}
              className="px-6"
            >
              {isCollecting ? 'Collecting...' : 'Collect All Data'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Data;
