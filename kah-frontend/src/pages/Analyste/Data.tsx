import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>ontent,
  Button,
  Badge,
  Progress
} from '../../components/ui';
import { 
  Upload, 
  FileSpreadsheet, 
  Database, 
  Download,
  Eye,
  Trash2,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';

interface DataFile {
  id: string;
  name: string;
  size: string;
  type: 'csv' | 'xlsx';
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  uploadedAt: string;
  records: number;
}

interface BackendData {
  id: string;
  name: string;
  description: string;
  lastUpdated: string;
  records: number;
  source: string;
}

const Data: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<DataFile[]>([
    {
      id: '1',
      name: 'real_estate_data.csv',
      size: '2.4 MB',
      type: 'csv',
      status: 'completed',
      progress: 100,
      uploadedAt: '2024-01-15 10:30',
      records: 15420
    },
    {
      id: '2',
      name: 'market_analysis.xlsx',
      size: '1.8 MB',
      type: 'xlsx',
      status: 'processing',
      progress: 65,
      uploadedAt: '2024-01-15 11:45',
      records: 8920
    }
  ]);

  const [backendData] = useState<BackendData[]>([
    {
      id: '1',
      name: 'Property Database',
      description: 'Comprehensive real estate property data from multiple sources',
      lastUpdated: '2024-01-15 09:00',
      records: 45678,
      source: 'Internal Database'
    },
    {
      id: '2',
      name: 'Market Trends',
      description: 'Historical market trends and price analysis data',
      lastUpdated: '2024-01-14 16:30',
      records: 23450,
      source: 'External API'
    },
    {
      id: '3',
      name: 'Demographic Data',
      description: 'Population and demographic information for market analysis',
      lastUpdated: '2024-01-13 14:20',
      records: 12340,
      source: 'Government API'
    }
  ]);

  const [isUploading, setIsUploading] = useState(false);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setIsUploading(true);
      
      // Simulate file upload
      setTimeout(() => {
        const newFile: DataFile = {
          id: Date.now().toString(),
          name: files[0].name,
          size: `${(files[0].size / 1024 / 1024).toFixed(1)} MB`,
          type: files[0].name.endsWith('.csv') ? 'csv' : 'xlsx',
          status: 'uploading',
          progress: 0,
          uploadedAt: new Date().toLocaleString(),
          records: 0
        };
        
        setUploadedFiles(prev => [...prev, newFile]);
        
        // Simulate upload progress
        const interval = setInterval(() => {
          setUploadedFiles(prev => 
            prev.map(file => 
              file.id === newFile.id 
                ? { ...file, progress: Math.min(file.progress + 20, 100) }
                : file
            )
          );
        }, 500);

        setTimeout(() => {
          clearInterval(interval);
          setUploadedFiles(prev => 
            prev.map(file => 
              file.id === newFile.id 
                ? { ...file, status: 'completed', progress: 100, records: Math.floor(Math.random() * 10000) + 1000 }
                : file
            )
          );
          setIsUploading(false);
        }, 3000);
      }, 1000);
    }
  };

  const getStatusIcon = (status: DataFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: DataFile['status']) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'info';
      case 'error':
        return 'error';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* File Upload Section */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Import Data Files"
          subtitle="Upload CSV or Excel files for analysis"
        />
        <CardContent>
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-8 text-center hover:border-primary-500 dark:hover:border-primary-400 transition-colors bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800">
            <Upload className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Drop files here or click to upload
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Support for CSV and Excel files up to 10MB
            </p>
            <input
              type="file"
              accept=".csv,.xlsx,.xls"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
              multiple
            />
            <label htmlFor="file-upload">
              <Button variant="primary" leftIcon={<Upload className="w-4 h-4" />}>
                Choose Files
              </Button>
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Uploaded Files"
          subtitle="Files imported for analysis"
        />
        <CardContent>
          <div className="space-y-4">
            {uploadedFiles.map((file) => (
              <div key={file.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg shadow-sm">
                    <FileSpreadsheet className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{file.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {file.size} • {file.records.toLocaleString()} records • {file.uploadedAt}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge variant={getStatusColor(file.status)}>
                    {getStatusIcon(file.status)}
                    <span className="ml-1">{file.status}</span>
                  </Badge>
                  
                  {file.status === 'processing' && (
                    <div className="w-24">
                      <Progress value={file.progress} max={100} size="sm" />
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                      Preview
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                      Download
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Trash2 className="w-4 h-4" />}>
                      Delete
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Backend Data Sources */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Data Sources"
          subtitle="Available data from backend systems"
        />
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {backendData.map((data) => (
              <div key={data.id} className="p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/10 dark:to-green-800/10 rounded-xl border border-green-200 dark:border-green-800 hover:shadow-lg transition-all duration-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg shadow-sm">
                    <Database className="w-5 h-5 text-green-600 dark:text-green-400" />
                  </div>
                  <Badge variant="success" size="sm">Available</Badge>
                </div>
                
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{data.name}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{data.description}</p>
                
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex justify-between">
                    <span>Records:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{data.records.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Source:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{data.source}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Updated:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{data.lastUpdated}</span>
                  </div>
                </div>
                
                <div className="flex space-x-2 mt-4">
                  <Button variant="outline" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                    Preview
                  </Button>
                  <Button variant="primary" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                    Import
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Status Alert */}
      {isUploading && (
        <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Clock className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Uploading Files</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Please wait while your files are being processed...</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Data;
