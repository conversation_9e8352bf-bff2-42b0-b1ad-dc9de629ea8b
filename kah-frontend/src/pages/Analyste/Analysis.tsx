import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
  Tabs,
  Select
} from '../../components/ui';
import {
  Trend<PERSON>hart,
  DistributionChart
} from '../../components/analytics';
import { 
  BarChart3, 
  Download,
  Play,
  Settings,
  Eye,
  Zap,
  Target,
  TrendingUp,
  Activity
} from 'lucide-react';

interface AnalysisWorkflow {
  id: string;
  name: string;
  type: 'visualization' | 'statistical' | 'modeling';
  status: 'running' | 'completed' | 'error';
  progress: number;
  createdAt: string;
  description: string;
}

interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'heatmap';
  title: string;
  data: Record<string, unknown>[];
  config: Record<string, unknown>;
}

const Analysis: React.FC = () => {
  const [selectedDataset, setSelectedDataset] = useState('real_estate_data');
  const [workflows] = useState<AnalysisWorkflow[]>([
    {
      id: '1',
      name: 'Price Trend Analysis',
      type: 'visualization',
      status: 'completed',
      progress: 100,
      createdAt: '2024-01-15 10:30',
      description: 'Analysis of property price trends over time'
    },
    {
      id: '2',
      name: 'Market Segmentation',
      type: 'statistical',
      status: 'running',
      progress: 65,
      createdAt: '2024-01-15 11:45',
      description: 'Statistical analysis of market segments'
    }
  ]);

  const [charts] = useState<ChartConfig[]>([
    {
      type: 'line',
      title: 'Property Price Trends',
      data: [
        { time: 'Jan', value: 250000, target: 240000 },
        { time: 'Feb', value: 265000, target: 240000 },
        { time: 'Mar', value: 280000, target: 240000 },
        { time: 'Apr', value: 295000, target: 240000 },
        { time: 'May', value: 310000, target: 240000 },
        { time: 'Jun', value: 325000, target: 240000 },
      ],
      config: {
        lines: [
          { dataKey: 'value', name: 'Average Price', color: '#3b82f6' },
          { dataKey: 'target', name: 'Target', color: '#ef4444', strokeDasharray: '5 5' }
        ]
      }
    },
    {
      type: 'bar',
      title: 'Property Distribution by Type',
      data: [
        { name: 'Residential', value: 45, color: '#10b981' },
        { name: 'Commercial', value: 30, color: '#f59e0b' },
        { name: 'Industrial', value: 15, color: '#8b5cf6' },
        { name: 'Mixed', value: 10, color: '#06b6d4' },
      ],
      config: {
        showLabels: true,
        showLegend: true
      }
    }
  ]);

  const analysisTabs = [
    {
      id: 'visualization',
      label: 'Data Visualization',
      icon: <BarChart3 className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          {/* Chart Controls */}
          <Card>
            <CardHeader title="Chart Configuration" />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Chart Type
                  </label>
                  <Select
                    value="line"
                    onValueChange={(value) => console.log('Chart type:', value)}
                    options={[
                      { value: 'line', label: 'Line Chart' },
                      { value: 'bar', label: 'Bar Chart' },
                      { value: 'pie', label: 'Pie Chart' },
                      { value: 'scatter', label: 'Scatter Plot' },
                      { value: 'heatmap', label: 'Heatmap' }
                    ]}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Data Source
                  </label>
                  <Select
                    value={selectedDataset}
                    onValueChange={setSelectedDataset}
                    options={[
                      { value: 'real_estate_data', label: 'Real Estate Data' },
                      { value: 'market_analysis', label: 'Market Analysis' },
                      { value: 'demographic_data', label: 'Demographic Data' }
                    ]}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Time Range
                  </label>
                  <Select
                    value="last_6_months"
                    onValueChange={(value) => console.log('Time range:', value)}
                    options={[
                      { value: 'last_week', label: 'Last Week' },
                      { value: 'last_month', label: 'Last Month' },
                      { value: 'last_6_months', label: 'Last 6 Months' },
                      { value: 'last_year', label: 'Last Year' }
                    ]}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {charts.map((chart, index) => (
              <Card key={index}>
                <CardHeader 
                  title={chart.title}
                  action={
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                        Preview
                      </Button>
                      <Button variant="ghost" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                        Export
                      </Button>
                    </div>
                  }
                />
                <CardContent>
                  {chart.type === 'line' && (
                    <TrendChart
                      data={chart.data}
                      lines={chart.config.lines}
                      height={300}
                      showTrendIndicator
                      showReferenceLine
                    />
                  )}
                  {chart.type === 'bar' && (
                    <DistributionChart
                      data={chart.data}
                      showLabels={chart.config.showLabels}
                      showLegend={chart.config.showLegend}
                    />
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )
    },
    {
      id: 'statistical',
      label: 'Statistical Analysis',
      icon: <Target className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          <Card>
            <CardHeader title="Statistical Tests" />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Correlation Analysis</h4>
                  <p className="text-sm text-gray-600 mb-3">Analyze relationships between variables</p>
                  <Button variant="primary" size="sm" leftIcon={<Zap className="w-4 h-4" />}>
                    Run Analysis
                  </Button>
                </div>
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Regression Analysis</h4>
                  <p className="text-sm text-gray-600 mb-3">Predict values based on variables</p>
                  <Button variant="primary" size="sm" leftIcon={<TrendingUp className="w-4 h-4" />}>
                    Run Analysis
                  </Button>
                </div>
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Hypothesis Testing</h4>
                  <p className="text-sm text-gray-600 mb-3">Test statistical hypotheses</p>
                  <Button variant="primary" size="sm" leftIcon={<Target className="w-4 h-4" />}>
                    Run Analysis
                  </Button>
                </div>
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Descriptive Statistics</h4>
                  <p className="text-sm text-gray-600 mb-3">Summary statistics and distributions</p>
                  <Button variant="primary" size="sm" leftIcon={<BarChart3 className="w-4 h-4" />}>
                    Run Analysis
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    },
    {
      id: 'modeling',
      label: 'Predictive Modeling',
      icon: <Activity className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          <Card>
            <CardHeader title="Machine Learning Models" />
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Linear Regression</h4>
                  <p className="text-sm text-gray-600 mb-3">Predict continuous values</p>
                  <div className="flex space-x-2">
                    <Button variant="primary" size="sm" leftIcon={<Play className="w-4 h-4" />}>
                      Train Model
                    </Button>
                    <Button variant="outline" size="sm" leftIcon={<Settings className="w-4 h-4" />}>
                      Configure
                    </Button>
                  </div>
                </div>
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Random Forest</h4>
                  <p className="text-sm text-gray-600 mb-3">Ensemble learning for predictions</p>
                  <div className="flex space-x-2">
                    <Button variant="primary" size="sm" leftIcon={<Play className="w-4 h-4" />}>
                      Train Model
                    </Button>
                    <Button variant="outline" size="sm" leftIcon={<Settings className="w-4 h-4" />}>
                      Configure
                    </Button>
                  </div>
                </div>
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Neural Network</h4>
                  <p className="text-sm text-gray-600 mb-3">Deep learning for complex patterns</p>
                  <div className="flex space-x-2">
                    <Button variant="primary" size="sm" leftIcon={<Play className="w-4 h-4" />}>
                      Train Model
                    </Button>
                    <Button variant="outline" size="sm" leftIcon={<Settings className="w-4 h-4" />}>
                      Configure
                    </Button>
                  </div>
                </div>
                <div className="p-4 border rounded-xl">
                  <h4 className="font-semibold text-gray-900 mb-2">Clustering</h4>
                  <p className="text-sm text-gray-600 mb-3">Group similar data points</p>
                  <div className="flex space-x-2">
                    <Button variant="primary" size="sm" leftIcon={<Play className="w-4 h-4" />}>
                      Run Clustering
                    </Button>
                    <Button variant="outline" size="sm" leftIcon={<Settings className="w-4 h-4" />}>
                      Configure
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Active Workflows */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardHeader 
          title="Active Analysis Workflows"
          subtitle="Currently running analysis tasks"
        />
        <CardContent>
          <div className="space-y-4">
            {workflows.map((workflow) => (
              <div key={workflow.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-700 dark:to-dark-800 rounded-xl border border-gray-200 dark:border-dark-700 hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg shadow-sm">
                    <Activity className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{workflow.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{workflow.description}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">Started: {workflow.createdAt}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge variant={workflow.status === 'completed' ? 'success' : 'info'}>
                    {workflow.status}
                  </Badge>
                  
                  {workflow.status === 'running' && (
                    <div className="w-24">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${workflow.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                  
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="sm" leftIcon={<Eye className="w-4 h-4" />}>
                      View
                    </Button>
                    <Button variant="ghost" size="sm" leftIcon={<Download className="w-4 h-4" />}>
                      Export
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analysis Tools */}
      <Card className="border-gray-200 dark:border-dark-700 shadow-sm">
        <CardContent className="p-0">
          <Tabs
            items={analysisTabs}
            variant="detection"
            size="lg"
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default Analysis;
