import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  Button
} from '../components/ui';
import { 
  Settings, 
  Plus
} from 'lucide-react';

// Import layout components
import Header from '../components/layout/Header';
import Sidebar from '../components/layout/Sidebar';
import Footer from '../components/layout/Footer';

// Import operation board components
import OperationDashboard from './OperationBoard/OperationDashboard';
import ProjectCheck from './OperationBoard/ProjectCheck';
import StockManagement from './OperationBoard/StockManagement';
import CommercialPerformance from './OperationBoard/CommercialPerformance';
import PredictiveMaintenance from './OperationBoard/PredictiveMaintenance';

const OperationBord: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  // Determine which component to show based on the current route
  const getCurrentComponent = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/operations') {
      return <OperationDashboard />;
    } else if (path.includes('/projects')) {
      return <ProjectCheck />;
    } else if (path.includes('/stock')) {
      return <StockManagement />;
    } else if (path.includes('/commercial')) {
      return <CommercialPerformance />;
    } else if (path.includes('/maintenance')) {
      return <PredictiveMaintenance />;
    } else {
      // Default to dashboard view
      return <OperationDashboard />;
    }
  };

  // Get current page title based on route
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/operations') {
      return 'Tableau de Bord Opérationnel';
    } else if (path.includes('/projects')) {
      return 'Suivi de Projets';
    } else if (path.includes('/stock')) {
      return 'Gestion des Stocks';
    } else if (path.includes('/commercial')) {
      return 'Performance Commerciale';
    } else if (path.includes('/maintenance')) {
      return 'Maintenance Prédictive';
    } else {
      return 'Tableau de Bord Opérationnel';
    }
  };

  const getPageDescription = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/operations') {
      return 'Vue d\'ensemble des activités opérationnelles';
    } else if (path.includes('/projects')) {
      return 'État d\'avancement, délais et budget par programme';
    } else if (path.includes('/stock')) {
      return 'Niveaux, consommations et alertes de rupture';
    } else if (path.includes('/commercial')) {
      return 'Ventes, leads et conversion par canal et agent';
    } else if (path.includes('/maintenance')) {
      return 'Anticipation des besoins d\'intervention';
    } else {
      return 'Vue d\'ensemble des activités opérationnelles';
    }
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-dark-900">
      {/* Mobile sidebar overlay */}
      <div 
        className={`fixed inset-0 z-50 lg:hidden transition-opacity duration-300 ${
          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
      >
        <div 
          className="absolute inset-0 bg-black opacity-50"
          onClick={() => setSidebarOpen(false)}
        />
        <div className={`relative w-64 h-full bg-white dark:bg-dark-800 shadow-xl transform transition-transform duration-300 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}>
          <Sidebar />
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:block w-64 bg-white dark:bg-dark-800 shadow-lg border-r border-gray-200 dark:border-dark-700">
        <Sidebar />
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header onMenuClick={() => setSidebarOpen(true)} />

        {/* Main content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-dark-900">
          <div className="p-6">
            {/* Page Header */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{getPageTitle()}</h1>
                <p className="text-gray-600">{getPageDescription()}</p>
              </div>
              <div className="flex space-x-3">
                <Button 
                  variant="outline" 
                  leftIcon={<Settings className="w-4 h-4" />}
                >
                  Paramètres
                </Button>
                <Button 
                  variant="primary" 
                  leftIcon={<Plus className="w-4 h-4" />}
                >
                  Nouvelle Action
                </Button>
              </div>
            </div>

            {/* Dynamic Content */}
            {getCurrentComponent()}
          </div>
        </main>

        {/* Footer */}
        <Footer />
      </div>
    </div>
  );
};

export default OperationBord;
