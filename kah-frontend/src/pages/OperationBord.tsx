import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  Button
} from '../components/ui';
import { 
  Settings, 
  Plus,
  Bell
} from 'lucide-react';

// Import layout components
import BaseInterface from '../components/layout/BaseInterface';

// Import operation board components
import OperationDashboard from './OperationBoard/OperationDashboard';
import ProjectCheck from './OperationBoard/ProjectCheck';
import StockManagement from './OperationBoard/StockManagement';
import CommercialPerformance from './OperationBoard/CommercialPerformance';
import PredictiveMaintenance from './OperationBoard/PredictiveMaintenance';
import OperationReport from './OperationBoard/OperationReport';
import AiInsight from './AiInsight';

const OperationBord: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Determine which component to show based on the current route
  const getCurrentComponent = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/operations') {
      return <OperationDashboard />;
    } else if (path.includes('/projects')) {
      return <ProjectCheck />;
    } else if (path.includes('/stock')) {
      return <StockManagement />;
    } else if (path.includes('/commercial')) {
      return <CommercialPerformance />;
    } else if (path.includes('/maintenance')) {
      return <PredictiveMaintenance />;
    } else if (path.includes('/report')) {
      return <OperationReport />;
    } else if (path.includes('/ai_insight')) {
      return <AiInsight />;
    } else {
      // Default to dashboard view
      return <OperationDashboard />;
    }
  };

  // Get current page title based on route
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/operations') {
      return 'Tableau de Bord Opérationnel';
    } else if (path.includes('/projects')) {
      return 'Suivi de Projets';
    } else if (path.includes('/stock')) {
      return 'Gestion des Stocks';
    } else if (path.includes('/commercial')) {
      return 'Performance Commerciale';
    } else if (path.includes('/maintenance')) {
      return 'Maintenance Prédictive';
    } else if (path.includes('/report')) {
      return 'Rapports Opérationnels';
    } else if (path.includes('/ai_insight')) {
      return 'Insights IA';
    } else {
      return 'Tableau de Bord Opérationnel';
    }
  };

  const getPageDescription = () => {
    const path = location.pathname;
    
    if (path.includes('/dashboard') || path === '/operations') {
      return 'Vue d\'ensemble des activités opérationnelles';
    } else if (path.includes('/projects')) {
      return 'État d\'avancement, délais et budget par programme';
    } else if (path.includes('/stock')) {
      return 'Niveaux, consommations et alertes de rupture';
    } else if (path.includes('/commercial')) {
      return 'Ventes, leads et conversion par canal et agent';
    } else if (path.includes('/maintenance')) {
      return 'Anticipation des besoins d\'intervention';
    } else if (path.includes('/report')) {
      return 'Génération et gestion des rapports opérationnels';
    } else if (path.includes('/ai_insight')) {
      return 'Analyse et insights basés sur les données pour prendre des décisions éclairées.';
    } else {
      return 'Vue d\'ensemble des activités opérationnelles';
    }
  };

  // Action buttons for the page header
  const actionButtons = (
    <>
      <Button 
        variant="outline" 
        leftIcon={<Settings className="w-4 h-4" />}
        className="bg-white/10 border-white/20 text-white hover:bg-white/20"
      >
        Paramètres
      </Button>
      <Button 
        variant="primary" 
        leftIcon={<Bell className="w-4 h-4" />}
        className="bg-white text-primary-600 hover:bg-gray-100"
        onClick={() => navigate('/notifications')}
      >
        Notifications
      </Button>
      <Button 
        variant="primary" 
        leftIcon={<Plus className="w-4 h-4" />}
        className="bg-white text-primary-600 hover:bg-gray-100"
      >
        Nouvelle Action
      </Button>
    </>
  );

  return (
    <BaseInterface
      getCurrentComponent={getCurrentComponent}
      getPageTitle={getPageTitle}
      getPageDescription={getPageDescription}
      actionButtons={actionButtons}
    />
  );
};

export default OperationBord;
