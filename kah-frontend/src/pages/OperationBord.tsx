import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  Progress,
  Dropdown
} from '../components/ui';
import {
  TrendChart
} from '../components/analytics';
import { 
  Play, 
  Pause, 
  Square, 
  Settings, 
  Alert<PERSON>riangle,
  CheckCircle,
  Activity,
  Zap,
  Eye,
  Download,
  RefreshCw,
  Plus,
  MoreVertical
} from 'lucide-react';

const OperationBord: React.FC = () => {
  // Mock data for operations
  const operationsData = [
    { id: 'op-1', name: 'Data Processing Pipeline', status: 'running', progress: 75, type: 'processing' },
    { id: 'op-2', name: 'Report Generation', status: 'pending', progress: 0, type: 'report' },
    { id: 'op-3', name: 'Backup System', status: 'completed', progress: 100, type: 'backup' },
    { id: 'op-4', name: 'Data Sync', status: 'error', progress: 45, type: 'sync' },
  ];

  const performanceData = [
    { time: '00:00', cpu: 45, memory: 60, network: 30 },
    { time: '04:00', cpu: 52, memory: 65, network: 35 },
    { time: '08:00', cpu: 78, memory: 80, network: 55 },
    { time: '12:00', cpu: 85, memory: 85, network: 70 },
    { time: '16:00', cpu: 72, memory: 75, network: 60 },
    { time: '20:00', cpu: 58, memory: 68, network: 40 },
  ];

  const operationTabs = [
    {
      id: 'overview',
      label: 'Operations Overview',
      icon: <Activity className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card variant="metric">
              <CardHeader title="Active Operations" />
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">12</div>
                <div className="text-sm text-gray-600">Currently running</div>
              </CardContent>
            </Card>
            
            <Card variant="metric">
              <CardHeader title="Pending Tasks" />
              <CardContent>
                <div className="text-3xl font-bold text-orange-600">8</div>
                <div className="text-sm text-gray-600">Awaiting action</div>
              </CardContent>
            </Card>
            
            <Card variant="metric">
              <CardHeader title="Completed Today" />
              <CardContent>
                <div className="text-3xl font-bold text-green-600">24</div>
                <div className="text-sm text-gray-600">Successfully finished</div>
              </CardContent>
            </Card>
            
            <Card variant="metric">
              <CardHeader title="System Health" />
              <CardContent>
                <div className="text-3xl font-bold text-green-600">98%</div>
                <div className="text-sm text-gray-600">All systems operational</div>
              </CardContent>
            </Card>
          </div>

          {/* Current Operations */}
          <Card>
            <CardHeader 
              title="Current Operations"
              action={
                <Button variant="primary" size="sm" leftIcon={<Plus className="w-4 h-4" />}>
                  New Operation
                </Button>
              }
            />
            <CardContent>
              <div className="space-y-4">
                {operationsData.map((operation) => (
                  <div key={operation.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        {operation.status === 'running' && <Spinner size="sm" variant="primary" />}
                        {operation.status === 'completed' && <CheckCircle className="w-5 h-5 text-green-500" />}
                        {operation.status === 'error' && <AlertTriangle className="w-5 h-5 text-red-500" />}
                        {operation.status === 'pending' && <div className="w-5 h-5 text-gray-400" />}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{operation.name}</h4>
                        <p className="text-sm text-gray-600">Operation ID: {operation.id}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="w-32">
                        <Progress
                          value={operation.progress}
                          max={100}
                          size="sm"
                          variant={operation.status === 'error' ? 'error' : 'primary'}
                          showPercentage
                        />
                      </div>
                      
                      <Badge 
                        variant={
                          operation.status === 'running' ? 'success' : 
                          operation.status === 'completed' ? 'primary' :
                          operation.status === 'error' ? 'error' : 'secondary'
                        }
                      >
                        {operation.status}
                      </Badge>
                      
                      <Dropdown
                        items={[
                          { id: 'view', label: 'View Details', icon: <Eye className="w-4 h-4" /> },
                          { id: 'pause', label: 'Pause', icon: <Pause className="w-4 h-4" /> },
                          { id: 'stop', label: 'Stop', icon: <Square className="w-4 h-4" /> },
                          { id: 'logs', label: 'View Logs', icon: <Download className="w-4 h-4" /> },
                        ]}
                        trigger="click"
                      >
                        <Button variant="ghost" size="sm">
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </Dropdown>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )
    },
    {
      id: 'performance',
      label: 'Performance Monitoring',
      icon: <Zap className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          <Card>
            <CardHeader 
              title="System Performance"
              subtitle="Last 24 hours"
            />
            <CardContent>
              <TrendChart
                data={performanceData}
                lines={[
                  { dataKey: 'cpu', name: 'CPU Usage', color: '#ef4444' },
                  { dataKey: 'memory', name: 'Memory Usage', color: '#3b82f6' },
                  { dataKey: 'network', name: 'Network I/O', color: '#10b981' }
                ]}
                height={300}
                showLegend
                yAxisFormat="percentage"
              />
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader title="Resource Utilization" />
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>CPU Usage</span>
                      <span>78%</span>
                    </div>
                    <Progress value={78} max={100} variant="error" size="sm" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Memory Usage</span>
                      <span>65%</span>
                    </div>
                    <Progress value={65} max={100} variant="primary" size="sm" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Disk Usage</span>
                      <span>45%</span>
                    </div>
                    <Progress value={45} max={100} variant="success" size="sm" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader title="Active Users" />
              <CardContent>
                <div className="text-center">
                  <div className="text-4xl font-bold text-blue-600 mb-2">156</div>
                  <div className="text-sm text-gray-600">Currently online</div>
                  <div className="mt-4 text-xs text-gray-500">
                    Peak today: 234 users at 14:30
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )
    },
    {
      id: 'alerts',
      label: 'System Alerts',
      icon: <AlertTriangle className="w-4 h-4" />,
      content: (
        <div className="space-y-6">
          <Alert 
            variant="warning" 
            title="High CPU Usage Detected"
            description="CPU usage has exceeded 85% for the last 10 minutes. Consider scaling up resources."
            dismissible
          />
          
          <Alert 
            variant="error" 
            title="Database Connection Failed"
            description="Unable to connect to the primary database. Retrying connection..."
            dismissible
          />
          
          <Alert 
            variant="info" 
            title="Scheduled Maintenance"
            description="System maintenance is scheduled for tomorrow at 02:00 UTC. Expected downtime: 30 minutes."
            dismissible
          />

          <Card>
            <CardHeader title="Alert History" />
            <CardContent>
              <div className="space-y-3">
                {[
                  { type: 'info', message: 'Backup completed successfully', time: '2 hours ago' },
                  { type: 'warning', message: 'Memory usage approaching threshold', time: '4 hours ago' },
                  { type: 'success', message: 'New data source connected', time: '6 hours ago' },
                ].map((alert, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <Badge variant={alert.type as 'info' | 'warning' | 'success'} size="sm">
                        {alert.type}
                      </Badge>
                      <span className="text-sm">{alert.message}</span>
                    </div>
                    <span className="text-xs text-gray-500">{alert.time}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Operation Board</h1>
          <p className="text-gray-600">Real-time operations monitoring and management</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            leftIcon={<Settings className="w-4 h-4" />}
          >
            Settings
          </Button>
          <Button 
            variant="primary" 
            leftIcon={<Plus className="w-4 h-4" />}
          >
            New Operation
          </Button>
        </div>
      </div>

      {/* System Status */}
      <Alert variant="success" title="System Status" description="All core systems are operational. Last health check: 1 minute ago" />

      {/* Main Content */}
      <Tabs
        items={operationTabs}
        variant="detection"
        size="lg"
        className="bg-white rounded-lg shadow-sm"
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader title="Quick Actions" />
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" leftIcon={<Play className="w-4 h-4" />} fullWidth>
              Start All
            </Button>
            <Button variant="outline" leftIcon={<Pause className="w-4 h-4" />} fullWidth>
              Pause All
            </Button>
            <Button variant="outline" leftIcon={<RefreshCw className="w-4 h-4" />} fullWidth>
              Refresh
            </Button>
            <Button variant="outline" leftIcon={<Download className="w-4 h-4" />} fullWidth>
              Export Logs
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OperationBord;
