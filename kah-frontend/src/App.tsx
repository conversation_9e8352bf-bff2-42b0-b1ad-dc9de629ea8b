import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider, useAuth } from './context/AuthContext'
import PrivateRoute from './components/auth/PrivateRoute'
import AppLayout from './components/layout/AppLayout'
import Login from './pages/auth/Login'
import Account from './pages/Account'
import OperationBord from './pages/OperationBord'
import AnalystInterface from './pages/AnalystInterface'
import BordDecision from './pages/BordDecision'
import './App.css'

// Component to handle automatic redirection after login
const AuthRedirect: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) return null;
  
  if (!isAuthenticated || !user) {
    return <Navigate to="/auth/login" replace />;
  }
  
  // Redirect based on user role
  switch (user.role?.name) {
    case 'operation_board':
      return <Navigate to="/operations/dashboard" replace />;
    case 'analyst':
      return <Navigate to="/analyst/donnee" replace />;
    case 'decision_board':
      return <Navigate to="/board/dashboard" replace />;
    default:
      return <Navigate to="/operations/dashboard" replace />;
  }
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public routes */}
            <Route path="/auth/login" element={<Login />} />
            
            {/* Account route - accessible to all authenticated users */}
            <Route 
              path="/account" 
              element={
                <PrivateRoute>
                  <Account />
                </PrivateRoute>
              } 
            />
            
            {/* Analyst routes with AppLayout */}
            <Route 
              path="/analyst/*" 
              element={
                <PrivateRoute requiredRole="analyst">
                  <AppLayout />
                </PrivateRoute>
              } 
            >
              <Route index element={<Navigate to="/analyst/dashboard" replace />} />
              <Route path="dashboard" element={<AnalystInterface />} />
              <Route path="donnee" element={<AnalystInterface />} />
              <Route path="analyse" element={<AnalystInterface />} />
              <Route path="publie" element={<AnalystInterface />} />
            </Route>
            
            {/* Board routes */}
            <Route 
              path="/board/*" 
              element={
                <PrivateRoute requiredRole="decision_board">
                  <BordDecision />
                </PrivateRoute>
              } 
            />
            
            {/* Operations routes */}
            <Route 
              path="/operations/*" 
              element={
                <PrivateRoute requiredRole="operation_board">
                  <OperationBord />
                </PrivateRoute>
              } 
            />
            
            {/* Legacy routes for backward compatibility */}
            <Route 
              path="/operation-board" 
              element={
                <PrivateRoute requiredRole="operation_board">
                  <OperationBord />
                </PrivateRoute>
              } 
            />
            
            <Route 
              path="/analyst-interface" 
              element={
                <PrivateRoute requiredRole="analyst">
                  <AnalystInterface />
                </PrivateRoute>
              } 
            />
            
            <Route 
              path="/board-decision" 
              element={
                <PrivateRoute requiredRole="decision_board">
                  <BordDecision />
                </PrivateRoute>
              } 
            />
            
            {/* Default route - redirect based on authentication */}
            <Route path="/" element={<AuthRedirect />} />
            
            {/* Catch all - redirect to login */}
            <Route path="*" element={<Navigate to="/auth/login" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App
