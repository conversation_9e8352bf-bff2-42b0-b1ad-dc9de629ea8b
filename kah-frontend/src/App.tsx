import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider, useAuth } from './context/AuthContext'
import { ThemeProvider } from './context/ThemeContext'
import PrivateRoute from './components/auth/PrivateRoute'
import AppLayout from './components/layout/AppLayout'
import Login from './pages/auth/Login'
import Account from './pages/Account'
import Settings from './pages/Settings'
import Notification from './pages/Notification'
import OperationBord from './pages/OperationBord'
import AnalystInterface from './pages/AnalystInterface'
import BordDecision from './pages/BordDecision'
import './App.css'

// Component to handle automatic redirection after login
const AuthRedirect: React.FC = () => {
  const { user, isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) return null;
  
  if (!isAuthenticated || !user) {
    return <Navigate to="/auth/login" replace />;
  }
  
  // Redirect based on user role
  switch (user.role?.name) {
    case 'operation_board':
      return <Navigate to="/operations/dashboard" replace />;
    case 'analyst':
      return <Navigate to="/analyst/donnee" replace />;
    case 'decision_board':
      return <Navigate to="/board/dashboard" replace />;
    default:
      return <Navigate to="/operations/dashboard" replace />;
  }
};

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* Public routes */}
              <Route path="/auth/login" element={<Login />} />
              
              {/* Account route - accessible to all authenticated users */}
              <Route 
                path="/account" 
                element={
                  <PrivateRoute>
                    <Account />
                  </PrivateRoute>
                } 
              />
              
              {/* Settings route - accessible to all authenticated users */}
              <Route 
                path="/settings" 
                element={
                  <PrivateRoute>
                    <Settings />
                  </PrivateRoute>
                } 
              />
              
              {/* Notifications route - accessible to all authenticated users */}
              <Route 
                path="/notifications" 
                element={
                  <PrivateRoute>
                    <Notification />
                  </PrivateRoute>
                } 
              />
              
              {/* Analyst routes with AppLayout */}
              <Route 
                path="/analyst/*" 
                element={
                  <PrivateRoute requiredRole="analyst">
                    <AppLayout />
                  </PrivateRoute>
                } 
              >
                <Route index element={<Navigate to="/analyst/dashboard" replace />} />
                <Route path="dashboard" element={<AnalystInterface />} />
                <Route path="donnee" element={<AnalystInterface />} />
                <Route path="analyse" element={<AnalystInterface />} />
                <Route path="publie" element={<AnalystInterface />} />
              </Route>
              
              {/* Board routes with AppLayout */}
              <Route 
                path="/board/*" 
                element={
                  <PrivateRoute requiredRole="decision_board">
                    <AppLayout />
                  </PrivateRoute>
                } 
              >
                <Route index element={<Navigate to="/board/dashboard" replace />} />
                <Route path="dashboard" element={<BordDecision />} />
                <Route path="kpi" element={<BordDecision />} />
                <Route path="rentability" element={<BordDecision />} />
                <Route path="forecasting" element={<BordDecision />} />
                <Route path="benchmark" element={<BordDecision />} />
                <Route path="report" element={<BordDecision />} />
                <Route path="ai_insight" element={<BordDecision />} />
              </Route>
              
              {/* Operations routes with AppLayout */}
              <Route 
                path="/operations/*" 
                element={
                  <PrivateRoute requiredRole="operation_board">
                    <AppLayout />
                  </PrivateRoute>
                } 
              >
                <Route index element={<Navigate to="/operations/dashboard" replace />} />
                <Route path="dashboard" element={<OperationBord />} />
                <Route path="projects" element={<OperationBord />} />
                <Route path="stock" element={<OperationBord />} />
                <Route path="commercial" element={<OperationBord />} />
                <Route path="maintenance" element={<OperationBord />} />
                <Route path="report" element={<OperationBord />} />
                <Route path="ai_insight" element={<OperationBord />} />
              </Route>
              
              {/* Legacy routes for backward compatibility */}
              <Route 
                path="/operation-board" 
                element={
                  <PrivateRoute requiredRole="operation_board">
                    <Navigate to="/operations/dashboard" replace />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/analyst-interface" 
                element={
                  <PrivateRoute requiredRole="analyst">
                    <Navigate to="/analyst/dashboard" replace />
                  </PrivateRoute>
                } 
              />
              
              <Route 
                path="/board-decision" 
                element={
                  <PrivateRoute requiredRole="decision_board">
                    <Navigate to="/board/dashboard" replace />
                  </PrivateRoute>
                } 
              />
              
              {/* Default redirect */}
              <Route path="/" element={<AuthRedirect />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </Router>
      </ThemeProvider>
    </AuthProvider>
  )
}

export default App
