/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  darkMode: "class", // Enable dark mode with class strategy
  theme: {
    extend: {
      colors: {
        // Kaydan brand colors
        kaydan: {
          50: "#fff7ed",
          100: "#ffedd5",
          200: "#fed7aa",
          300: "#fdba74",
          400: "#fb923c",
          500: "#f97316", // Primary warm orange
          600: "#ea580c", // Darker reddish-orange
          700: "#c2410c",
          800: "#9a3412",
          900: "#7c2d12",
          950: "#431407",
        },
        "kaydan-gray": {
          50: "#f8fafc",
          100: "#f1f5f9",
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b", // Light gray for text
          600: "#475569",
          700: "#334155",
          800: "#1e293b",
          900: "#0f172a",
          950: "#020617",
        },
        // Map primary colors to kaydan colors
        primary: {
          50: "#fff7ed",
          100: "#ffedd5",
          200: "#fed7aa",
          300: "#fdba74",
          400: "#fb923c",
          500: "#f97316", // Primary warm orange
          600: "#ea580c", // Darker reddish-orange
          700: "#c2410c",
          800: "#9a3412",
          900: "#7c2d12",
          950: "#431407",
        },
        // Beautiful dark theme colors - much nicer and more readable
        dark: {
          50: "#f8fafc", // Lightest text on dark
          100: "#f1f5f9", // Very light text on dark
          200: "#e2e8f0", // Light text on dark
          300: "#cbd5e1", // Medium light text on dark
          400: "#94a3b8", // Medium text on dark
          500: "#64748b", // Dark text on light
          600: "#475569", // Darker text on light
          700: "#334155", // Dark surface
          800: "#1e293b", // Dark background
          900: "#0f172a", // Darkest background
          950: "#020617", // Ultra dark background
        },
        // Gray colors for both light and dark modes
        gray: {
          50: "#f8fafc",
          100: "#f1f5f9",
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b",
          600: "#475569",
          700: "#334155",
          800: "#1e293b",
          900: "#0f172a",
          950: "#020617",
        },
        // Success colors
        success: {
          50: "#f0fdf4",
          100: "#dcfce7",
          200: "#bbf7d0",
          300: "#86efac",
          400: "#4ade80",
          500: "#22c55e",
          600: "#16a34a",
          700: "#15803d",
          800: "#166534",
          900: "#14532d",
        },
        // Warning colors
        warning: {
          50: "#fffbeb",
          100: "#fef3c7",
          200: "#fde68a",
          300: "#fcd34d",
          400: "#fbbf24",
          500: "#f59e0b",
          600: "#d97706",
          700: "#b45309",
          800: "#92400e",
          900: "#78350f",
        },
        // Error colors
        error: {
          50: "#fef2f2",
          100: "#fee2e2",
          200: "#fecaca",
          300: "#fca5a5",
          400: "#f87171",
          500: "#ef4444",
          600: "#dc2626",
          700: "#b91c1c",
          800: "#991b1b",
          900: "#7f1d1d",
        },
        // Info colors
        info: {
          50: "#eff6ff",
          100: "#dbeafe",
          200: "#bfdbfe",
          300: "#93c5fd",
          400: "#60a5fa",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
          800: "#1e40af",
          900: "#1e3a8a",
        },
      },
      backgroundColor: {
        "dark-50": "#f8fafc",
        "dark-100": "#f1f5f9",
        "dark-200": "#e2e8f0",
        "dark-300": "#cbd5e1",
        "dark-400": "#94a3b8",
        "dark-500": "#64748b",
        "dark-600": "#475569",
        "dark-700": "#334155",
        "dark-800": "#1e293b",
        "dark-900": "#0f172a",
      },
      textColor: {
        "dark-50": "#f8fafc",
        "dark-100": "#f1f5f9",
        "dark-200": "#e2e8f0",
        "dark-300": "#cbd5e1",
        "dark-400": "#94a3b8",
        "dark-500": "#64748b",
        "dark-600": "#475569",
        "dark-700": "#334155",
        "dark-800": "#1e293b",
        "dark-900": "#0f172a",
      },
      borderColor: {
        "dark-50": "#f8fafc",
        "dark-100": "#f1f5f9",
        "dark-200": "#e2e8f0",
        "dark-300": "#cbd5e1",
        "dark-400": "#94a3b8",
        "dark-500": "#64748b",
        "dark-600": "#475569",
        "dark-700": "#334155",
        "dark-800": "#1e293b",
        "dark-900": "#0f172a",
      },
    },
  },
  plugins: [],
};
