{"name": "kah-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@types/axios": "^0.9.36", "@types/react-router-dom": "^5.3.3", "axios": "^1.11.0", "clsx": "^2.1.1", "i18next": "^25.3.2", "lucide-react": "^0.536.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.6.1", "react-router-dom": "^6.30.1", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.3.3", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^6.3.5"}}