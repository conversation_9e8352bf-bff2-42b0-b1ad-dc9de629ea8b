#!/usr/bin/env python3
"""
Analyse complète de toutes les structures de données
"""

import os
import sys
import django
import json
import pandas as pd
from datetime import datetime

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')
django.setup()

from api.models import (
    DQEData, GStockApprovisionnement, GStockSortie, GStockConsommation,
    GStockAchat, GProjet, EcoleTalents, GLocative, ESyndic, Sentinelle, CRM
)

def analyze_deep_structure(data, path="", level=0):
    """
    Analyse récursive de la structure des données
    """
    tables_found = []
    indent = "  " * level
    
    if isinstance(data, dict):
        print(f"{indent}📁 Dict avec {len(data)} clés: {list(data.keys())}")
        
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            print(f"{indent}  🔑 {key}: {type(value).__name__}")
            
            if isinstance(value, list) and len(value) > 0:
                if isinstance(value[0], dict):
                    # C'est une table !
                    print(f"{indent}    📊 TABLE DÉTECTÉE: {len(value)} lignes")
                    print(f"{indent}    📋 Colonnes: {list(value[0].keys())}")
                    
                    tables_found.append({
                        'name': key,
                        'path': current_path,
                        'rows': len(value),
                        'columns': list(value[0].keys()),
                        'sample': value[0]
                    })
                else:
                    print(f"{indent}    📝 Liste de {type(value[0]).__name__}: {value[:3]}")
            
            elif isinstance(value, dict):
                print(f"{indent}    📁 Sous-dictionnaire:")
                sub_tables = analyze_deep_structure(value, current_path, level + 2)
                tables_found.extend(sub_tables)
            
            elif isinstance(value, list):
                print(f"{indent}    📝 Liste vide ou simple: {value}")
            
            else:
                print(f"{indent}    📄 Valeur: {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
    
    elif isinstance(data, list):
        print(f"{indent}📊 Liste de {len(data)} éléments")
        if len(data) > 0:
            print(f"{indent}  Premier élément: {type(data[0]).__name__}")
            if isinstance(data[0], dict):
                sub_tables = analyze_deep_structure(data[0], path, level + 1)
                tables_found.extend(sub_tables)
    
    return tables_found

def analyze_model_completely(model_class, model_name):
    """
    Analyse complète d'un modèle
    """
    print(f"\n{'='*80}")
    print(f"🔍 ANALYSE COMPLÈTE DE {model_name.upper()}")
    print(f"{'='*80}")
    
    count = model_class.objects.count()
    print(f"📊 Nombre d'enregistrements: {count}")
    
    if count == 0:
        print("❌ Aucune donnée disponible")
        return []
    
    all_tables = []
    
    # Analyser tous les enregistrements
    for i, record in enumerate(model_class.objects.order_by('-created_at')[:5]):
        print(f"\n📝 ENREGISTREMENT {i+1}: {record.title}")
        print(f"📅 Créé le: {record.created_at}")
        
        # Analyser les champs directs
        record_dict = {
            'id': record.id,
            'title': record.title,
            'description': record.description,
            'created_at': record.created_at.isoformat(),
            'updated_at': record.updated_at.isoformat(),
        }
        
        # Ajouter les données JSON
        if hasattr(record, 'data') and record.data:
            if isinstance(record.data, dict):
                record_dict.update(record.data)
            else:
                record_dict['raw_data'] = record.data
        
        print(f"\n🔍 STRUCTURE DE L'ENREGISTREMENT {i+1}:")
        tables = analyze_deep_structure(record_dict)
        
        # Ajouter les tables trouvées
        for table in tables:
            table['source_record'] = i + 1
            table['record_title'] = record.title
            all_tables.append(table)
        
        print(f"📊 Tables trouvées dans cet enregistrement: {len(tables)}")
    
    # Résumé des tables uniques
    unique_tables = {}
    for table in all_tables:
        table_name = table['name']
        if table_name not in unique_tables:
            unique_tables[table_name] = table
        else:
            # Prendre celle avec le plus de lignes
            if table['rows'] > unique_tables[table_name]['rows']:
                unique_tables[table_name] = table
    
    print(f"\n🎯 RÉSUMÉ FINAL POUR {model_name}:")
    print(f"📊 Total tables uniques trouvées: {len(unique_tables)}")
    
    for table_name, table_info in unique_tables.items():
        print(f"  📋 {table_name}:")
        print(f"    📊 {table_info['rows']} lignes × {len(table_info['columns'])} colonnes")
        print(f"    📝 Colonnes: {table_info['columns']}")
        print(f"    📍 Source: Enregistrement {table_info['source_record']} ({table_info['record_title']})")
        print(f"    🔗 Chemin: {table_info['path']}")
    
    return list(unique_tables.values())

def main():
    """
    Analyse complète de tous les modèles
    """
    print("🚀 ANALYSE COMPLÈTE DE TOUTES LES STRUCTURES DE DONNÉES")
    print("="*80)
    
    models_to_analyze = [
        (DQEData, "DQE"),
        (GStockApprovisionnement, "G-Stock Approvisionnement"),
        (GStockSortie, "G-Stock Sortie"),
        (GStockConsommation, "G-Stock Consommation"),
        (GStockAchat, "G-Stock Achat"),
        (Sentinelle, "Sentinelle"),
        (GProjet, "G-Projet"),
        (CRM, "CRM"),
        (GLocative, "G-Locative"),
        (ESyndic, "E-Syndic"),
        (EcoleTalents, "École des Talents"),
    ]
    
    all_results = {}
    
    for model_class, model_name in models_to_analyze:
        try:
            tables = analyze_model_completely(model_class, model_name)
            all_results[model_name] = tables
        except Exception as e:
            print(f"❌ Erreur pour {model_name}: {e}")
            all_results[model_name] = []
    
    # Résumé global
    print(f"\n🎉 RÉSUMÉ GLOBAL")
    print("="*80)
    
    total_tables = 0
    for model_name, tables in all_results.items():
        print(f"📊 {model_name}: {len(tables)} tables")
        total_tables += len(tables)
        
        for table in tables:
            print(f"  📋 {table['name']} ({table['rows']} lignes)")
    
    print(f"\n🎯 TOTAL: {total_tables} tables détectées dans {len(all_results)} applications")
    
    # Sauvegarder les résultats
    with open('tables_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"💾 Résultats sauvegardés dans tables_analysis.json")

if __name__ == "__main__":
    main()
