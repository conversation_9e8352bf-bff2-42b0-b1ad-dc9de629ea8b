#!/usr/bin/env python3
"""
Script de diagnostic rapide pour vérifier les données
"""

import os
import sys
import django
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')
django.setup()

from api.models import ESyndic, GLocative

def check_esyndic():
    print("🔍 DIAGNOSTIC E-SYNDIC")
    print("="*50)
    
    count = ESyndic.objects.count()
    print(f"Nombre d'enregistrements: {count}")
    
    if count > 0:
        latest = ESyndic.objects.order_by('-created_at').first()
        print(f"Titre: {latest.title}")
        print(f"Data type: {type(latest.data)}")
        print(f"Data content: {latest.data}")
        
        if isinstance(latest.data, dict):
            print(f"Clés: {list(latest.data.keys())}")
            for key, value in latest.data.items():
                print(f"  {key}: {type(value)} - {value}")

def check_glocative():
    print("\n🔍 DIAGNOSTIC G-LOCATIVE")
    print("="*50)
    
    count = GLocative.objects.count()
    print(f"Nombre d'enregistrements: {count}")
    
    if count > 0:
        latest = GLocative.objects.order_by('-created_at').first()
        print(f"Titre: {latest.title}")
        print(f"Data type: {type(latest.data)}")
        print(f"Data content: {latest.data}")
        
        if isinstance(latest.data, dict):
            print(f"Clés: {list(latest.data.keys())}")
            for key, value in latest.data.items():
                print(f"  {key}: {type(value)} - {value}")

def fix_esyndic():
    print("\n🔧 CORRECTION E-SYNDIC")
    print("="*50)
    
    # Données corrigées pour E-Syndic
    esyndic_data = {
        "copropriete": "Les Terrasses du Plateau",
        "copropriétaires": 45,
        "incidents": [
            {"type": "Ascenseur en panne", "date": "2024-08-01", "statut": "Résolu", "cout": 850000},
            {"type": "Fuite canalisation", "date": "2024-08-03", "statut": "En cours", "cout": 0},
            {"type": "Éclairage parking", "date": "2024-08-05", "statut": "Planifié", "cout": 320000}
        ],
        "charges_trimestrielles": [
            {"type": "Entretien", "montant": 2400000, "trimestre": "Q3 2024"},
            {"type": "Assurance", "montant": 1800000, "trimestre": "Q3 2024"},
            {"type": "Gardiennage", "montant": 3600000, "trimestre": "Q3 2024"},
            {"type": "Travaux", "montant": 1200000, "trimestre": "Q3 2024"}
        ],
        "assemblees": [
            {"date": "2024-06-15", "type": "Assemblée générale", "participants": 38, "decisions": 12},
            {"date": "2024-03-20", "type": "Assemblée extraordinaire", "participants": 28, "decisions": 5}
        ]
    }
    
    # Mettre à jour ou créer
    esyndic, created = ESyndic.objects.update_or_create(
        title="E-Syndic Les Terrasses",
        defaults={
            "description": "Gestion de copropriété Les Terrasses du Plateau",
            "data": esyndic_data
        }
    )
    
    print(f"✅ E-Syndic {'créé' if created else 'mis à jour'}")

def fix_glocative():
    print("\n🔧 CORRECTION G-LOCATIVE")
    print("="*50)
    
    # Données corrigées pour G-Locative
    glocative_data = {
        "patrimoine": "Résidence Harmonie",
        "locataires": [
            {"nom": "BAMBA Koffi", "appartement": "A101", "loyer": 350000, "statut": "À jour", "depot_garantie": 700000},
            {"nom": "KONE Mariam", "appartement": "B205", "loyer": 420000, "statut": "Retard 1 mois", "depot_garantie": 840000},
            {"nom": "YAO Patrick", "appartement": "C308", "loyer": 380000, "statut": "À jour", "depot_garantie": 760000},
            {"nom": "DIALLO Fatou", "appartement": "A203", "loyer": 400000, "statut": "À jour", "depot_garantie": 800000}
        ],
        "charges": [
            {"type": "Électricité communs", "montant": 180000, "mois": "Août 2024", "repartition": "Tantième"},
            {"type": "Eau", "montant": 95000, "mois": "Août 2024", "repartition": "Consommation"},
            {"type": "Gardiennage", "montant": 240000, "mois": "Août 2024", "repartition": "Égale"},
            {"type": "Entretien ascenseur", "montant": 120000, "mois": "Août 2024", "repartition": "Tantième"}
        ],
        "paiements": [
            {"locataire": "BAMBA Koffi", "mois": "Août 2024", "loyer": 350000, "charges": 85000, "statut": "Payé"},
            {"locataire": "KONE Mariam", "mois": "Juillet 2024", "loyer": 420000, "charges": 92000, "statut": "Impayé"},
            {"locataire": "YAO Patrick", "mois": "Août 2024", "loyer": 380000, "charges": 88000, "statut": "Payé"}
        ]
    }
    
    # Mettre à jour ou créer
    glocative, created = GLocative.objects.update_or_create(
        title="G-Locative Résidence Harmonie",
        defaults={
            "description": "Gestion locative de la Résidence Harmonie",
            "data": glocative_data
        }
    )
    
    print(f"✅ G-Locative {'créé' if created else 'mis à jour'}")

if __name__ == "__main__":
    check_esyndic()
    check_glocative()
    fix_esyndic()
    fix_glocative()
    print("\n🎉 Diagnostic et corrections terminés !")
