{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Importing libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from datetime import datetime\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from ast import literal_eval\n", "import ast\n", "import pprint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  <PERSON><PERSON>, Convert and Save json to csv"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. Chargement des données JSON\n", "json_file = 'E_syndic.json'\n", "\n", "with open(json_file, 'r', encoding='utf-8') as f:\n", "    json_data = json.load(f)\n", "\n", "print(f\"Données JSON chargées depuis {json_file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. Conversion JSON vers DataFrame\n", "if isinstance(json_data, dict) and 'data' in json_data:\n", "    df = pd.DataFrame(json_data['data'])\n", "elif isinstance(json_data, list):\n", "    df = pd.DataFrame(json_data)\n", "else:\n", "    df = pd.DataFrame([json_data])\n", "\n", "print(f\"DataFrame créé: {df.shape}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. <PERSON><PERSON><PERSON><PERSON> en CSV\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f'esundic_{timestamp}.csv'\n", "\n", "os.makedirs('../exports', exist_ok=True)\n", "csv_filepath = f'../exports/{csv_filename}'\n", "\n", "df.to_csv(csv_filepath, index=False, encoding='utf-8')\n", "print(f\"CSV sauvegardé: {csv_filepath}\")\n", "print(f\"Nombre de lignes: {len(df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. Lecture du CSV sauvegardé\n", "# df_loaded = pd.read_csv(csv_filepath)\n", "\n", "# print(f\"CSV rechargé: {df_loaded.shape}\")\n", "# print(\"Aperçu des données:\")\n", "# df_loaded.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#  Exploration task"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>libelle</th>\n", "      <th>lieu</th>\n", "      <th>pays</th>\n", "      <th>ville</th>\n", "      <th>superficie</th>\n", "      <th>description</th>\n", "      <th>created_at</th>\n", "      <th>proprietaire</th>\n", "      <th>locataires</th>\n", "      <th>charges</th>\n", "      <th>incidents</th>\n", "      <th>evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>None</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>300</td>\n", "      <td>None</td>\n", "      <td>2024-07-01 10:57:55</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'appel_fonds': {'total_charge': 0, 'total_pay...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>None</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>15000</td>\n", "      <td>None</td>\n", "      <td>2024-03-05 17:05:49</td>\n", "      <td>[{'name': 'BLEOUE', 'prenoms': 'EHUI ROGER', '...</td>\n", "      <td>[]</td>\n", "      <td>{'appel_fonds': {'total_charge': 0, 'total_pay...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>None</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>50000</td>\n", "      <td>None</td>\n", "      <td>2020-11-04 11:08:21</td>\n", "      <td>[{'name': 'AKOU', 'prenoms': 'YAPOIDOU AUGUSTI...</td>\n", "      <td>[{'name': 'NEHOUAN', 'prenoms': 'SERGE PACOME'...</td>\n", "      <td>{'appel_fonds': {'total_charge': 47625000, 'to...</td>\n", "      <td>[{'id': 101, 'reference': '250422091253', 'aut...</td>\n", "      <td>[{'id': 10, 'titre': 'Hello world', 'descripti...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id                 libelle  lieu           pays             ville  \\\n", "0   3         CALLISTO ETOILE  None  Côte d'Ivoire      Grand-Bassam   \n", "1   2  CALLISTO BNETD PHASE 1  None  Côte d'Ivoire      Grand-Bassam   \n", "2   1               SYMPHONIA  None  Côte d'Ivoire  Abidjan - Cocody   \n", "\n", "  superficie description           created_at  \\\n", "0        300        None  2024-07-01 10:57:55   \n", "1      15000        None  2024-03-05 17:05:49   \n", "2      50000        None  2020-11-04 11:08:21   \n", "\n", "                                        proprietaire  \\\n", "0                                                 []   \n", "1  [{'name': 'BLEOUE', 'prenoms': 'EHUI ROGER', '...   \n", "2  [{'name': 'AKO<PERSON>', 'prenoms': 'YAPOIDOU AUGUSTI...   \n", "\n", "                                          locataires  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2  [{'name': 'NEHOUAN', 'prenoms': 'SERGE PACOME'...   \n", "\n", "                                             charges  \\\n", "0  {'appel_fonds': {'total_charge': 0, 'total_pay...   \n", "1  {'appel_fonds': {'total_charge': 0, 'total_pay...   \n", "2  {'appel_fonds': {'total_charge': 47625000, 'to...   \n", "\n", "                                           incidents  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2  [{'id': 101, 'reference': '250422091253', 'aut...   \n", "\n", "                                          evenements  \n", "0                                                 []  \n", "1                                                 []  \n", "2  [{'id': 10, 'titre': 'Hello world', 'descripti...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4. Lecture du CSV sauvegardé\n", "df_loaded = pd.read_csv('E_syndic.csv', delimiter=',')\n", "print(df_loaded)\n", "\n", "data_str = df_loaded.iloc[0, 0]\n", "try:\n", "    data = ast.literal_eval(data_str)\n", "except Exception as e:\n", "    print(f\"Error parsing data: {e}\")\n", "    data = []\n", "\n", "\n", "df_sites = pd.DataFrame(data)\n", "\n", "# Preview the result\n", "df_sites.head()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data cleanning "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. Extraction des données pour visualisation\n", "syndics = []\n", "for _, row in df_loaded.iterrows():\n", "    # Compter les propriétaires et locataires\n", "    proprietaires = eval(row['proprietaire']) if pd.notna(row['proprietaire']) and row['proprietaire'] != '[]' else []\n", "    locataires = eval(row['locataires']) if pd.notna(row['locataires']) and row['locataires'] != '[]' else []\n", "    incidents = eval(row['incidents']) if pd.notna(row['incidents']) and row['incidents'] != '[]' else []\n", "    evenements = eval(row['evenements']) if pd.notna(row['evenements']) and row['evenements'] != '[]' else []\n", "    \n", "    syndics.append({\n", "        'syndic': row['libelle'],\n", "        'ville': row['ville'],\n", "        'superficie': int(row['superficie']) if pd.notna(row['superficie']) else 0,\n", "        'nb_proprietaires': len(proprietaires),\n", "        'nb_locataires': len(locataires),\n", "        'nb_incidents': len(incidents),\n", "        'nb_evenements': len(evenements)\n", "    })\n", "\n", "df_viz = pd.DataFrame(syndics)\n", "print(df_viz)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. Visualisation 1: Répartition des syndics par ville\n", "fig1 = px.pie(df_viz, names='ville', title='Répartition des syndics par ville')\n", "fig1.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7. Visualisation 2: Nombre de propriétaires par syndic\n", "fig2 = px.bar(df_viz, x='syndic', y='nb_proprietaires', \n", "              title='Nombre de propriétaires par syndic',\n", "              labels={'nb_proprietaires': 'Nombre de propriétaires', 'syndic': 'Syndic'})\n", "fig2.update_xaxis(tickangle=45)\n", "fig2.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 8. Visualisation 3: Superficie vs Nombre d'incidents\n", "fig3 = px.scatter(df_viz, x='superficie', y='nb_incidents', \n", "                  size='nb_proprietaires', hover_name='syndic',\n", "                  title='Superficie vs Nombre d\\'incidents (taille = nb propriétaires)',\n", "                  labels={'superficie': 'Superficie (m²)', 'nb_incidents': 'Nombre d\\'incidents'})\n", "fig3.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 9. Visualisation 4: Comparaison propriétaires vs locataires\n", "fig4 = go.Figure()\n", "fig4.add_trace(go.Bar(name='Pro<PERSON>riétaire<PERSON>', x=df_viz['syndic'], y=df_viz['nb_proprietaires']))\n", "fig4.add_trace(go.Bar(name='Locataires', x=df_viz['syndic'], y=df_viz['nb_locataires']))\n", "fig4.update_layout(barmode='group', title='Comparaison Propriétaires vs Locataires par syndic',\n", "                   xaxis_title='Syndic', yaxis_title='Nombre de personnes')\n", "fig4.update_xaxis(tickangle=45)\n", "fig4.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 10. Statistiques descriptives\n", "print(\"=== STATISTIQUES DESCRIPTIVES ===\")\n", "print(f\"Nombre total de syndics: {len(df_viz)}\")\n", "print(f\"Superficie totale: {df_viz['superficie'].sum():,} m²\")\n", "print(f\"Nombre total de propriétaires: {df_viz['nb_proprietaires'].sum()}\")\n", "print(f\"Nombre total de locataires: {df_viz['nb_locataires'].sum()}\")\n", "print(f\"Nombre total d'incidents: {df_viz['nb_incidents'].sum()}\")\n", "print(f\"Nombre total d'événements: {df_viz['nb_evenements'].sum()}\")\n", "print(\"\\n=== MOYENNES ===\")\n", "print(f\"Superficie moyenne: {df_viz['superficie'].mean():.2f} m²\")\n", "print(f\"Propriétaires par syndic: {df_viz['nb_proprietaires'].mean():.2f}\")\n", "print(f\"Locataires par syndic: {df_viz['nb_locataires'].mean():.2f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}