from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import CustomUser, Role, Notification, Message


class RoleAdmin(admin.ModelAdmin):
    list_display = ("name", "description")
    list_filter = ("name",)
    search_fields = ("name", "description")

    fieldsets = (
        (None, {"fields": ("name", "description", "permissions")}),
    )


class NotificationAdmin(admin.ModelAdmin):
    list_display = ("recipient", "sender", "notification_type", "title", "is_read", "created_at")
    list_filter = ("notification_type", "priority", "is_read", "is_deleted", "created_at")
    search_fields = ("recipient__email", "sender__email", "title", "message")
    readonly_fields = ("created_at", "read_at")
    
    fieldsets = (
        (None, {"fields": ("recipient", "sender", "notification_type", "priority")}),
        (_("Content"), {"fields": ("title", "message")}),
        (_("Message Settings"), {"fields": ("is_message", "can_reply", "message_id")}),
        (_("Status"), {"fields": ("is_read", "is_deleted", "read_at")}),
        (_("Timestamps"), {"fields": ("created_at",), "classes": ("collapse",)}),
    )


class MessageAdmin(admin.ModelAdmin):
    list_display = ("sender", "message_type", "title", "priority", "created_at")
    list_filter = ("message_type", "priority", "created_at")
    search_fields = ("sender__email", "title", "content")
    readonly_fields = ("created_at", "updated_at")
    filter_horizontal = ("recipients",)
    
    fieldsets = (
        (None, {"fields": ("sender", "message_type", "priority")}),
        (_("Content"), {"fields": ("title", "content")}),
        (_("Recipients"), {"fields": ("recipients",)}),
        (_("Timestamps"), {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
    )


class CustomUserAdmin(UserAdmin):
    list_display = (
        "username",
        "email",
        "first_name",
        "last_name",
        "kaydan_subsidiary",
        "role",
        "email_verified",
        "is_active",
        "created_by",
        "is_staff",
    )
    list_filter = ("is_verified", "is_staff", "is_active", "date_joined", "role")
    search_fields = (
        "username",
        "email",
        "first_name",
        "last_name",
        "kaydan_subsidiary",
    )
    readonly_fields = ("created_by", "date_joined", "last_login")

    # Custom fieldsets to avoid duplicates
    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (_("Personal info"), {"fields": ("first_name", "last_name", "email")}),
        (
            _("Company Information"),
            {"fields": ("kaydan_subsidiary",)},
        ),
        (
            _("Role & Permissions"),
            {
                "fields": (
                    "role",
                    "is_active",
                    "created_by",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                )
            },
        ),
        (
            _("Email Verification"),
            {
                "fields": (
                    "email_verified",
                    "email_verification_code",
                    "email_verification_code_created_at",
                )
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "date_joined")}),
    )

    # Add custom fields to the add form
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("username", "email", "password1", "password2"),
            },
        ),
        (_("Personal info"), {"fields": ("first_name", "last_name")}),
        (_("Company Information"), {"fields": ("kaydan_subsidiary",)}),
        (_("Role"), {"fields": ("role",)}),
    )

    def save_model(self, request, obj, form, change):
        # Set created_by for new users
        if not change:  # This is a new user
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

    def get_queryset(self, request):
        # Super admins can see all users
        if request.user.is_super_admin():
            return super().get_queryset(request)
        # Regular admins can only see users they created
        return super().get_queryset(request).filter(created_by=request.user)


admin.site.register(Role, RoleAdmin)
admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(Notification, NotificationAdmin)
admin.site.register(Message, MessageAdmin)
