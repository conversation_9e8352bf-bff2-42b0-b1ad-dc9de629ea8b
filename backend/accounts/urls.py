from django.urls import path
from knox import views as knox_views
from .api import (
    RegisterAPI,
    LoginAPI,
    UserAPI,
    ChangePasswordAPI,
    EmailVerificationAPI,
    SendVerificationCodeAPI,
    VerifyEmailCodeAPI,
    UserListAPI,
    UserDetailAPI,
    RoleListAPI,
    RoleDetailAPI,
    PublicRoleListAPI,
    MessagingUsersAPI,
    NotificationListAPI,
    NotificationDetailAPI,
    MarkAllNotificationsReadAPI,
    MessageListAPI,
    MessageDetailAPI,
    MessageHistoryAPI,
)
from . import views

urlpatterns = [
    # Knox authentication endpoints
    path("auth/login/", LoginAPI.as_view(), name="login"),
    path("auth/logout/", knox_views.LogoutView.as_view(), name="logout"),
    path("auth/logoutall/", knox_views.LogoutAllView.as_view(), name="logoutall"),
    # User registration and management (Super Admin only)
    path("auth/register/", RegisterAPI.as_view(), name="register"),
    path("auth/user/", UserAPI.as_view(), name="user"),
    path("auth/change-password/", ChangePasswordAPI.as_view(), name="change-password"),
    # User management (Admin only)
    path("auth/users/", UserListAPI.as_view(), name="user-list"),
    path("auth/users/<int:pk>/", UserDetailAPI.as_view(), name="user-detail"),
    # Messaging users (for messaging system)
    path("auth/messaging-users/", MessagingUsersAPI.as_view(), name="messaging-users"),
    # Notifications
    path("auth/notifications/", NotificationListAPI.as_view(), name="notification-list"),
    path("auth/notifications/<int:pk>/", NotificationDetailAPI.as_view(), name="notification-detail"),
    path("auth/notifications/mark-all-read/", MarkAllNotificationsReadAPI.as_view(), name="mark-all-notifications-read"),
    # Messages
    path("auth/messages/", MessageListAPI.as_view(), name="message-list"),
    path("auth/messages/<int:pk>/", MessageDetailAPI.as_view(), name="message-detail"),
    path("auth/messages/history/", MessageHistoryAPI.as_view(), name="message-history"),
    # Role management (Super Admin only)
    path("auth/roles/", RoleListAPI.as_view(), name="role-list"),
    path("auth/roles/<int:pk>/", RoleDetailAPI.as_view(), name="role-detail"),
    # Public roles endpoint (for login form)
    path("auth/public-roles/", PublicRoleListAPI.as_view(), name="public-role-list"),
    # Email verification
    path(
        "auth/email-verification/",
        EmailVerificationAPI.as_view(),
        name="email-verification",
    ),
    path(
        "auth/send-verification-code/",
        SendVerificationCodeAPI.as_view(),
        name="send-verification-code",
    ),
    path(
        "auth/verify-email-code/",
        VerifyEmailCodeAPI.as_view(),
        name="verify-email-code",
    ),
]
