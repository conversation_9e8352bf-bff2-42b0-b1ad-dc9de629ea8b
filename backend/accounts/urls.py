from django.urls import path
from knox import views as knox_views
from .api import (
    RegisterAPI,
    LoginAPI,
    UserAPI,
    ChangePasswordAPI,
    EmailVerificationAPI,
    SendVerificationCodeAPI,
    VerifyEmailCodeAPI,
    UserListAPI,
    UserDetailAPI,
    RoleListAPI,
    RoleDetailAPI,
)
from . import views

urlpatterns = [
    # Knox authentication endpoints
    path("auth/login/", LoginAPI.as_view(), name="login"),
    path("auth/logout/", knox_views.LogoutView.as_view(), name="logout"),
    path("auth/logoutall/", knox_views.LogoutAllView.as_view(), name="logoutall"),
    # User registration and management (Super Admin only)
    path("auth/register/", RegisterAPI.as_view(), name="register"),
    path("auth/user/", UserAPI.as_view(), name="user"),
    path("auth/change-password/", ChangePasswordAPI.as_view(), name="change-password"),
    # User management (Admin only)
    path("auth/users/", UserListAPI.as_view(), name="user-list"),
    path("auth/users/<int:pk>/", UserDetailAPI.as_view(), name="user-detail"),
    # Role management (Super Admin only)
    path("auth/roles/", RoleListAPI.as_view(), name="role-list"),
    path("auth/roles/<int:pk>/", RoleDetailAPI.as_view(), name="role-detail"),
    # Email verification
    path(
        "auth/email-verification/",
        EmailVerificationAPI.as_view(),
        name="email-verification",
    ),
    path(
        "auth/send-verification-code/",
        SendVerificationCodeAPI.as_view(),
        name="send-verification-code",
    ),
    path(
        "auth/verify-email-code/",
        VerifyEmailCodeAPI.as_view(),
        name="verify-email-code",
    ),
]
