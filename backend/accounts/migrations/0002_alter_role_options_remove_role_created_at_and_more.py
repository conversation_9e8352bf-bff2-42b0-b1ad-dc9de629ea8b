# Generated by Django 5.2.3 on 2025-08-06 12:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='role',
            options={},
        ),
        migrations.RemoveField(
            model_name='role',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='role',
            name='updated_at',
        ),
        migrations.AlterField(
            model_name='role',
            name='name',
            field=models.CharField(choices=[('super_admin', 'Super Admin'), ('admin', 'Admin'), ('analyst', 'Analyst'), ('decision_board', 'Decision Board'), ('operation_board', 'Operation Board')], max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='role',
            name='permissions',
            field=models.JSONField(default=dict),
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message_type', models.CharField(choices=[('individual', 'Individual'), ('broadcast', 'Broadcast')], default='individual', max_length=20)),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipients', models.ManyToManyField(related_name='received_messages', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('message', 'Message'), ('system', 'System'), ('alert', 'Alert'), ('broadcast', 'Broadcast')], default='message', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')], default='medium', max_length=10)),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_message', models.BooleanField(default=False)),
                ('can_reply', models.BooleanField(default=False)),
                ('message_id', models.CharField(blank=True, max_length=100, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
