from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Role, CustomUser
from django.db import transaction

User = get_user_model()


class Command(BaseCommand):
    help = "Set up initial roles and create a super admin user"

    def add_arguments(self, parser):
        parser.add_argument(
            "--username",
            type=str,
            help="Username for the super admin",
            default="superadmin",
        )
        parser.add_argument(
            "--email",
            type=str,
            help="Email for the super admin",
            default="<EMAIL>",
        )
        parser.add_argument(
            "--password",
            type=str,
            help="Password for the super admin",
            default="admin123",
        )
        parser.add_argument(
            "--first-name",
            type=str,
            help="First name for the super admin",
            default="Super",
        )
        parser.add_argument(
            "--last-name",
            type=str,
            help="Last name for the super admin",
            default="Admin",
        )

    def handle(self, *args, **options):
        with transaction.atomic():
            # Create roles
            self.stdout.write("Creating roles...")

            roles_data = [
                {
                    "name": Role.SUPER_ADMIN,
                    "description": "Full system access and user management",
                    "permissions": {
                        "can_create_users": True,
                        "can_manage_users": True,
                        "can_access_admin": True,
                        "can_view_analytics": True,
                        "can_edit_analytics": True,
                    },
                },
                {
                    "name": Role.ADMIN,
                    "description": "User management and limited system access",
                    "permissions": {
                        "can_create_users": False,
                        "can_manage_users": True,
                        "can_access_admin": True,
                        "can_view_analytics": True,
                        "can_edit_analytics": False,
                    },
                },
                {
                    "name": Role.ANALYST,
                    "description": "Data analysis and reporting access",
                    "permissions": {
                        "can_create_users": False,
                        "can_manage_users": False,
                        "can_access_admin": False,
                        "can_view_analytics": True,
                        "can_edit_analytics": True,
                    },
                },
                {
                    "name": Role.USER,
                    "description": "Basic user access",
                    "permissions": {
                        "can_create_users": False,
                        "can_manage_users": False,
                        "can_access_admin": False,
                        "can_view_analytics": True,
                        "can_edit_analytics": False,
                    },
                },
            ]

            created_roles = {}
            for role_data in roles_data:
                role, created = Role.objects.get_or_create(
                    name=role_data["name"],
                    defaults={
                        "description": role_data["description"],
                        "permissions": role_data["permissions"],
                    },
                )
                created_roles[role.name] = role
                if created:
                    self.stdout.write(f"Created role: {role.name}")
                else:
                    self.stdout.write(f"Role already exists: {role.name}")

            # Create super admin user
            self.stdout.write("Creating super admin user...")

            super_admin_role = created_roles[Role.SUPER_ADMIN]

            # Check if super admin already exists
            if CustomUser.objects.filter(role=super_admin_role).exists():
                self.stdout.write(self.style.WARNING("Super admin user already exists"))
                return

            # Create super admin user
            super_admin = CustomUser.objects.create_user(
                username=options["username"],
                email=options["email"],
                password=options["password"],
                first_name=options["first_name"],
                last_name=options["last_name"],
                role=super_admin_role,
                is_staff=True,
                is_superuser=True,
                email_verified=True,
                is_verified=True,
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully created super admin user: {super_admin.username}"
                )
            )
            self.stdout.write(f"Email: {super_admin.email}")
            self.stdout.write(f'Password: {options["password"]}')
            self.stdout.write(
                self.style.WARNING("Please change the password after first login!")
            )
