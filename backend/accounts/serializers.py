from rest_framework import serializers
from .models import CustomUser, Role


# Role serializer
class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ["id", "name", "description", "permissions"]


# User serializer
class UserSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    role_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = CustomUser
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "email_verified",
            "role",
            "role_id",
            "is_active",
            "created_by",
        ]
        read_only_fields = ("id", "created_by")
        ref_name = "AccountUsersSerializer"


# login serializer
class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login with role validation.
    """

    username = serializers.Char<PERSON>ield(required=False)
    email = serializers.EmailField(required=False)
    password = serializers.Char<PERSON>ield(style={"input_type": "password"})
    role_id = serializers.IntegerField(required=True)

    def validate(self, data):
        # Ensure either username or email is provided
        if not data.get("username") and not data.get("email"):
            raise serializers.ValidationError(
                "Must include either 'username' or 'email'."
            )

        from django.contrib.auth import authenticate

        username = data.get("username")
        email = data.get("email")
        password = data.get("password")
        role_id = data.get("role_id")

        if username:
            user = authenticate(username=username, password=password)
        elif email:
            try:
                user_obj = CustomUser.objects.get(email=email)
                user = authenticate(username=user_obj.username, password=password)
            except CustomUser.DoesNotExist:
                user = None

        if not user:
            raise serializers.ValidationError(
                "Unable to log in with provided credentials."
            )

        # Validate that the user has the selected role
        if user.role_id != role_id:
            raise serializers.ValidationError(
                "The selected role is not assigned to this user."
            )

        data["user"] = user
        return data


# Registration serializer - Only for super admins
class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )
    password_confirm = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )
    role_id = serializers.IntegerField(required=True)

    class Meta:
        model = CustomUser
        fields = [
            "username",
            "email",
            "password",
            "password_confirm",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "role_id",
        ]
        extra_kwargs = {
            "password": {"write_only": True},
            "kaydan_subsidiary": {"required": False},
        }

    def validate(self, data):
        # Check that the two password entries match
        if data["password"] != data["password_confirm"]:
            raise serializers.ValidationError(
                {"password_confirm": "Passwords don't match."}
            )

        # Validate role exists
        role_id = data.get("role_id")
        try:
            role = Role.objects.get(id=role_id)
            data["role"] = role
        except Role.DoesNotExist:
            raise serializers.ValidationError({"role_id": "Invalid role selected."})

        return data

    def create(self, validated_data):
        # Remove password_confirm as it's not needed for creating the user
        validated_data.pop("password_confirm")

        # Set the creator as the current user (super admin)
        validated_data["created_by"] = self.context["request"].user

        # Create the user with the validated data
        password = validated_data.pop("password")
        user = CustomUser(**validated_data)
        user.set_password(password)
        user.save()
        return user


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer for password change endpoint.
    """

    current_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )
    new_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )
    confirm_new_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )

    def validate(self, data):
        # Check that the new password entries match
        if data["new_password"] != data["confirm_new_password"]:
            raise serializers.ValidationError(
                {"confirm_new_password": "New passwords don't match."}
            )
        return data


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user information.
    """

    role_id = serializers.IntegerField(required=False)

    class Meta:
        model = CustomUser
        fields = [
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "role_id",
            "is_active",
        ]

    def validate_role_id(self, value):
        try:
            Role.objects.get(id=value)
            return value
        except Role.DoesNotExist:
            raise serializers.ValidationError("Invalid role selected.")


class UserListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing users (admin only)
    """

    role = RoleSerializer(read_only=True)
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = CustomUser
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "email_verified",
            "role",
            "is_active",
            "created_by",
            "date_joined",
        ]
