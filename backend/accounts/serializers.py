from rest_framework import serializers
from .models import CustomUser, Role, Notification, Message


# Role serializer
class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ["id", "name", "description", "permissions"]


# User serializer
class UserSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    role_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = CustomUser
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "is_active",
            "date_joined",
            "role",
            "role_id",
        ]
        read_only_fields = ["id", "date_joined"]


# login serializer
class LoginSerializer(serializers.Serializer):
    """
    Serializer for user login with role validation.
    """

    username = serializers.CharField()
    password = serializers.CharField()
    role_id = serializers.IntegerField()

    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")
        role_id = attrs.get("role_id")

        if username and password and role_id:
            try:
                user = CustomUser.objects.get(username=username)
                if user.check_password(password):
                    # Check if user has the specified role
                    if user.role and user.role.id == role_id:
                        attrs["user"] = user
                        return attrs
                    else:
                        raise serializers.ValidationError(
                            "User does not have the specified role"
                        )
                else:
                    raise serializers.ValidationError("Invalid password")
            except CustomUser.DoesNotExist:
                raise serializers.ValidationError("User not found")
        else:
            raise serializers.ValidationError("All fields are required")


# Registration serializer - Only for super admins
class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )
    password_confirm = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )
    role_id = serializers.IntegerField(required=True)

    class Meta:
        model = CustomUser
        fields = [
            "username",
            "email",
            "password",
            "password_confirm",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "role_id",
        ]
        extra_kwargs = {
            "password": {"write_only": True},
            "kaydan_subsidiary": {"required": False},
        }

    def validate(self, data):
        # Check that the two password entries match
        if data["password"] != data["password_confirm"]:
            raise serializers.ValidationError(
                {"password_confirm": "Passwords don't match."}
            )

        # Validate role exists
        role_id = data.get("role_id")
        try:
            role = Role.objects.get(id=role_id)
            data["role"] = role
        except Role.DoesNotExist:
            raise serializers.ValidationError({"role_id": "Invalid role selected."})

        return data

    def create(self, validated_data):
        # Remove password_confirm as it's not needed for creating the user
        validated_data.pop("password_confirm")

        # Set the creator as the current user (super admin)
        validated_data["created_by"] = self.context["request"].user

        # Create the user with the validated data
        password = validated_data.pop("password")
        user = CustomUser(**validated_data)
        user.set_password(password)
        user.save()
        return user


class ChangePasswordSerializer(serializers.Serializer):
    """
    Serializer for password change endpoint.
    """

    current_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )
    new_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )
    confirm_new_password = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )

    def validate(self, data):
        # Check that the new password entries match
        if data["new_password"] != data["confirm_new_password"]:
            raise serializers.ValidationError(
                {"confirm_new_password": "New passwords don't match."}
            )
        return data


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating user information.
    """

    role_id = serializers.IntegerField(required=False)

    class Meta:
        model = CustomUser
        fields = [
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "role_id",
            "is_active",
        ]

    def validate_role_id(self, value):
        try:
            Role.objects.get(id=value)
            return value
        except Role.DoesNotExist:
            raise serializers.ValidationError("Invalid role selected.")


class UserListSerializer(serializers.ModelSerializer):
    """
    Serializer for listing users (admin only)
    """

    role = RoleSerializer(read_only=True)
    created_by = UserSerializer(read_only=True)

    class Meta:
        model = CustomUser
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "kaydan_subsidiary",
            "is_active",
            "date_joined",
            "role",
            "created_by",
        ]


class MessagingUserSerializer(serializers.ModelSerializer):
    """
    Serializer for messaging users - simplified version for messaging purposes
    """
    role = RoleSerializer(read_only=True)

    class Meta:
        model = CustomUser
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "role",
        ]


class NotificationSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)
    recipient = UserSerializer(read_only=True)
    
    class Meta:
        model = Notification
        fields = [
            "id",
            "sender",
            "recipient",
            "notification_type",
            "priority",
            "title",
            "message",
            "is_message",
            "can_reply",
            "message_id",
            "is_read",
            "is_deleted",
            "created_at",
            "read_at",
        ]
        read_only_fields = ["id", "created_at", "read_at"]


class MessageSerializer(serializers.ModelSerializer):
    sender = UserSerializer(read_only=True)
    recipients = UserSerializer(many=True, read_only=True)
    notifications = NotificationSerializer(many=True, read_only=True)
    
    class Meta:
        model = Message
        fields = [
            "id",
            "sender",
            "recipients",
            "message_type",
            "title",
            "content",
            "priority",
            "created_at",
            "updated_at",
            "notifications",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "notifications"]


class CreateMessageSerializer(serializers.ModelSerializer):
    recipient_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Message
        fields = [
            "message_type",
            "title",
            "content",
            "priority",
            "recipient_ids",
        ]
    
    def create(self, validated_data):
        recipient_ids = validated_data.pop("recipient_ids", [])
        sender = self.context["request"].user
        
        # Create the message
        message = Message.objects.create(
            sender=sender,
            **validated_data
        )
        
        # Add recipients
        if recipient_ids:
            recipients = CustomUser.objects.filter(id__in=recipient_ids)
            message.recipients.set(recipients)
        
        # Create notifications for recipients
        message.create_notifications_for_recipients()
        
        return message
