from rest_framework.response import Response
from rest_framework import generics, permissions
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from knox.models import AuthToken
from django.contrib.auth.hashers import make_password, check_password
from .serializers import (
    ChangePasswordSerializer,
    UserSerializer,
    RegisterSerializer,
    LoginSerializer,
    UserListSerializer,
    UserUpdateSerializer,
    RoleSerializer,
    MessagingUserSerializer,
    NotificationSerializer,
    MessageSerializer,
    CreateMessageSerializer,
)
from .models import CustomUser, Role, Notification, Message
from .permissions import (
    IsSuperAdmin,
    IsAdmin,
    CanCreateUsers,
    CanManageUsers,
    IsOwnerOrAdmin,
)
from django.utils import timezone
from datetime import timedelta
import random
from django.conf import settings
from rest_framework.exceptions import ValidationError
from django.db import models


# Register API - Only for super admins
class RegisterAPI(generics.GenericAPIView):
    permission_classes = [CanCreateUsers]
    serializer_class = RegisterSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user = serializer.save()
        return Response(
            {
                "user": RegisterSerializer(
                    user, context=self.get_serializer_context()
                ).data,
                "token": AuthToken.objects.create(user)[1],
                "message": "User created successfully",
            },
            status=status.HTTP_201_CREATED,
        )


# Login API
class LoginAPI(generics.GenericAPIView):
    serializer_class = LoginSerializer
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data["user"]  # Extract just the user object
        _, token = AuthToken.objects.create(user)
        return Response(
            {
                "user": UserSerializer(
                    user, context=self.get_serializer_context()
                ).data,
                "token": token,
            }
        )


class UserAPI(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    serializer_class = UserSerializer

    def get_object(self):
        return self.request.user


class ChangePasswordAPI(generics.GenericAPIView):
    serializer_class = ChangePasswordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            # Verify current password
            if not user.check_password(serializer.validated_data["current_password"]):
                return Response(
                    {"error": "Current password is incorrect"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Set new password
            user.set_password(serializer.validated_data["new_password"])
            user.save()

            # Logout all sessions for the user
            AuthToken.objects.filter(user=user).delete()

            return Response(
                {"message": "Password changed successfully"}, status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# User Management APIs (Admin only)
class UserListAPI(generics.ListAPIView):
    permission_classes = [CanManageUsers]
    serializer_class = UserListSerializer
    queryset = CustomUser.objects.all().select_related("role", "created_by")


class UserDetailAPI(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [CanManageUsers]
    serializer_class = UserUpdateSerializer
    queryset = CustomUser.objects.all()

    def perform_update(self, serializer):
        # Only super admins can change roles
        if (
            "role_id" in serializer.validated_data
            and not self.request.user.is_super_admin()
        ):
            raise ValidationError("Only super admins can change user roles")
        serializer.save()


# Messaging Users API - Get users for messaging
class MessagingUsersAPI(generics.ListAPIView):
    """
    Get list of users for messaging purposes.
    Returns all users except the current user for messaging.
    """
    permission_classes = [IsAuthenticated]
    serializer_class = MessagingUserSerializer
    
    def get_queryset(self):
        # Exclude the current user from the list, but include all other users regardless of role
        return CustomUser.objects.exclude(id=self.request.user.id).select_related("role").order_by('first_name', 'last_name')
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['for_messaging'] = True
        return context


# Notification APIs
class NotificationListAPI(generics.ListAPIView):
    """
    Get user's notifications
    """
    permission_classes = [IsAuthenticated]
    serializer_class = NotificationSerializer
    
    def get_queryset(self):
        return Notification.objects.filter(
            recipient=self.request.user,
            is_deleted=False
        ).select_related('sender', 'recipient').order_by('-created_at')


class NotificationDetailAPI(generics.RetrieveUpdateDestroyAPIView):
    """
    Get, update, or delete a specific notification
    """
    permission_classes = [IsAuthenticated]
    serializer_class = NotificationSerializer
    queryset = Notification.objects.all()
    
    def get_queryset(self):
        return Notification.objects.filter(recipient=self.request.user)
    
    def perform_update(self, serializer):
        # Mark as read when updating
        if 'is_read' in serializer.validated_data and serializer.validated_data['is_read']:
            serializer.instance.mark_as_read()
        serializer.save()
    
    def perform_destroy(self, instance):
        # Soft delete - mark as deleted instead of actually deleting
        instance.mark_as_deleted()


class MarkAllNotificationsReadAPI(APIView):
    """
    Mark all user's notifications as read
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        notifications = Notification.objects.filter(
            recipient=request.user,
            is_read=False,
            is_deleted=False
        )
        
        for notification in notifications:
            notification.mark_as_read()
        
        return Response({
            "message": f"Marked {notifications.count()} notifications as read"
        }, status=status.HTTP_200_OK)


# Message APIs
class MessageListAPI(generics.ListCreateAPIView):
    """
    Get user's messages or create a new message
    """
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CreateMessageSerializer
        return MessageSerializer
    
    def get_queryset(self):
        return Message.objects.filter(
            recipients=self.request.user
        ).select_related('sender').prefetch_related('recipients').order_by('-created_at')
    
    def perform_create(self, serializer):
        message = serializer.save()
        return Response({
            "message": "Message sent successfully",
            "data": MessageSerializer(message, context=self.get_serializer_context()).data
        }, status=status.HTTP_201_CREATED)


class MessageDetailAPI(generics.RetrieveAPIView):
    """
    Get a specific message
    """
    permission_classes = [IsAuthenticated]
    serializer_class = MessageSerializer
    queryset = Message.objects.all()
    
    def get_queryset(self):
        return Message.objects.filter(recipients=self.request.user)


class MessageHistoryAPI(generics.ListAPIView):
    """
    Get message history/conversation for a specific message thread
    """
    permission_classes = [IsAuthenticated]
    serializer_class = MessageSerializer
    
    def get_queryset(self):
        # Get messages where current user is either sender or recipient
        # and the message has the same title pattern (for replies)
        user = self.request.user
        return Message.objects.filter(
            models.Q(sender=user) | models.Q(recipients=user)
        ).select_related('sender').prefetch_related('recipients').order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        # Get messages related to the current user
        messages = self.get_queryset()
        
        # Group messages by conversation (same title pattern)
        conversations = {}
        for message in messages:
            # Extract base title (remove "Re:" prefix)
            base_title = message.title.replace('Re: ', '').replace('Re:', '')
            if base_title not in conversations:
                conversations[base_title] = []
            conversations[base_title].append(message)
        
        # Sort conversations by latest message
        sorted_conversations = sorted(
            conversations.items(),
            key=lambda x: max(msg.created_at for msg in x[1]),
            reverse=True
        )
        
        return Response({
            "conversations": [
                {
                    "title": title,
                    "messages": MessageSerializer(messages, many=True).data
                }
                for title, messages in sorted_conversations
            ]
        }, status=status.HTTP_200_OK)


# Role Management APIs (Super Admin only)
class RoleListAPI(generics.ListAPIView):
    permission_classes = [IsSuperAdmin]
    serializer_class = RoleSerializer
    queryset = Role.objects.all()


class PublicRoleListAPI(generics.ListAPIView):
    """
    Public endpoint to get available roles for login form.
    No authentication required.
    """

    permission_classes = [permissions.AllowAny]
    serializer_class = RoleSerializer
    queryset = Role.objects.all()


class RoleDetailAPI(generics.RetrieveAPIView):
    permission_classes = [IsSuperAdmin]
    serializer_class = RoleSerializer
    queryset = Role.objects.all()


class EmailVerificationAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        return Response({"is_verified": user.email_verified, "email": user.email})

    def post(self, request):
        user = request.user
        email = request.data.get("email")

        if email != user.email:
            return Response(
                {"matches": False, "message": "Email does not match our records"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response({"matches": True, "message": "Email verified successfully"})


class SendVerificationCodeAPI(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user

        # Generate 6-digit code
        verification_code = "".join([str(random.randint(0, 9)) for _ in range(6)])

        # Save code and timestamp
        user.email_verification_code = verification_code
        user.email_verification_code_created_at = timezone.now()
        user.save()

        # Here you would typically send the email with the code
        # For development, just print it
        print(f"Verification code for {user.email}: {verification_code}")

        return Response({"message": "Verification code sent successfully"})


class VerifyEmailCodeAPI(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        code = request.data.get("code")

        # Check if code exists and is not expired (15 minutes validity)
        if (
            not user.email_verification_code
            or not user.email_verification_code_created_at
        ):
            return Response(
                {"verified": False, "message": "No verification code found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if timezone.now() > user.email_verification_code_created_at + timedelta(
            minutes=15
        ):
            return Response(
                {"verified": False, "message": "Verification code expired"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if code != user.email_verification_code:
            return Response(
                {"verified": False, "message": "Invalid verification code"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Mark email as verified
        user.email_verified = True
        user.is_verified = True  # Update both fields for backward compatibility
        user.email_verification_code = None
        user.email_verification_code_created_at = None
        user.save()

        return Response({"verified": True, "message": "Email verified successfully"})
