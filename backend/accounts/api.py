from rest_framework.response import Response
from rest_framework import generics, permissions
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from knox.models import AuthToken
from django.contrib.auth.hashers import make_password, check_password
from .serializers import (
    ChangePasswordSerializer,
    UserSerializer,
    RegisterSerializer,
    LoginSerializer,
    UserListSerializer,
    UserUpdateSerializer,
    RoleSerializer,
)
from .models import CustomUser, Role
from .permissions import (
    IsSuperAdmin,
    IsAdmin,
    CanCreateUsers,
    CanManageUsers,
    IsOwnerOrAdmin,
)
from django.utils import timezone
from datetime import timedelta
import random
from django.conf import settings
from rest_framework.exceptions import ValidationError


# Register API - Only for super admins
class RegisterAPI(generics.GenericAPIView):
    permission_classes = [CanCreateUsers]
    serializer_class = RegisterSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        user = serializer.save()
        return Response(
            {
                "user": RegisterSerializer(
                    user, context=self.get_serializer_context()
                ).data,
                "token": AuthToken.objects.create(user)[1],
                "message": "User created successfully",
            },
            status=status.HTTP_201_CREATED,
        )


# Login API
class LoginAPI(generics.GenericAPIView):
    serializer_class = LoginSerializer
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data["user"]  # Extract just the user object
        _, token = AuthToken.objects.create(user)
        return Response(
            {
                "user": UserSerializer(
                    user, context=self.get_serializer_context()
                ).data,
                "token": token,
            }
        )


class UserAPI(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    serializer_class = UserSerializer

    def get_object(self):
        return self.request.user


class ChangePasswordAPI(generics.GenericAPIView):
    serializer_class = ChangePasswordSerializer
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            # Verify current password
            if not user.check_password(serializer.validated_data["current_password"]):
                return Response(
                    {"error": "Current password is incorrect"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Set new password
            user.set_password(serializer.validated_data["new_password"])
            user.save()

            # Logout all sessions for the user
            AuthToken.objects.filter(user=user).delete()

            return Response(
                {"message": "Password changed successfully"}, status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# User Management APIs (Admin only)
class UserListAPI(generics.ListAPIView):
    permission_classes = [CanManageUsers]
    serializer_class = UserListSerializer
    queryset = CustomUser.objects.all().select_related("role", "created_by")


class UserDetailAPI(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [CanManageUsers]
    serializer_class = UserUpdateSerializer
    queryset = CustomUser.objects.all()

    def perform_update(self, serializer):
        # Only super admins can change roles
        if (
            "role_id" in serializer.validated_data
            and not self.request.user.is_super_admin()
        ):
            raise ValidationError("Only super admins can change user roles")
        serializer.save()


# Role Management APIs (Super Admin only)
class RoleListAPI(generics.ListAPIView):
    permission_classes = [IsSuperAdmin]
    serializer_class = RoleSerializer
    queryset = Role.objects.all()


class RoleDetailAPI(generics.RetrieveAPIView):
    permission_classes = [IsSuperAdmin]
    serializer_class = RoleSerializer
    queryset = Role.objects.all()


class EmailVerificationAPI(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        return Response({"is_verified": user.email_verified, "email": user.email})

    def post(self, request):
        user = request.user
        email = request.data.get("email")

        if email != user.email:
            return Response(
                {"matches": False, "message": "Email does not match our records"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response({"matches": True, "message": "Email verified successfully"})


class SendVerificationCodeAPI(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user

        # Generate 6-digit code
        verification_code = "".join([str(random.randint(0, 9)) for _ in range(6)])

        # Save code and timestamp
        user.email_verification_code = verification_code
        user.email_verification_code_created_at = timezone.now()
        user.save()

        # Here you would typically send the email with the code
        # For development, just print it
        print(f"Verification code for {user.email}: {verification_code}")

        return Response({"message": "Verification code sent successfully"})


class VerifyEmailCodeAPI(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        code = request.data.get("code")

        # Check if code exists and is not expired (15 minutes validity)
        if (
            not user.email_verification_code
            or not user.email_verification_code_created_at
        ):
            return Response(
                {"verified": False, "message": "No verification code found"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if timezone.now() > user.email_verification_code_created_at + timedelta(
            minutes=15
        ):
            return Response(
                {"verified": False, "message": "Verification code expired"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if code != user.email_verification_code:
            return Response(
                {"verified": False, "message": "Invalid verification code"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Mark email as verified
        user.email_verified = True
        user.is_verified = True  # Update both fields for backward compatibility
        user.email_verification_code = None
        user.email_verification_code_created_at = None
        user.save()

        return Response({"verified": True, "message": "Email verified successfully"})
