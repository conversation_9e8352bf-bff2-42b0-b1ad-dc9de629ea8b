from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _


class Role(models.Model):
    """
    Role model to define user roles in the system
    """

    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    USER = "user"
    DECISION_BOARD = "decision_board"
    OPERATION_BOARD = "operation_board"
    ANALYST = "analyst"

    ROLE_CHOICES = [
        (SUPER_ADMIN, "Super Admin"),
        (ADMIN, "Admin"),
        (USER, "User"),
        (ANALYST, "Analyst"),
        (DECISION_BOARD, "Decision Board"),
        (OPERATION_BOARD, "Operation Board"),
    ]

    name = models.CharField(max_length=50, choices=ROLE_CHOICES, unique=True)
    description = models.TextField(blank=True)
    permissions = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Role"
        verbose_name_plural = "Roles"

    def __str__(self):
        return self.name


class CustomUser(AbstractUser):
    username = models.CharField(("username"), max_length=100, unique=True)
    last_name = models.CharField(("Last Name"), max_length=100)
    first_name = models.CharField(("First Name"), max_length=100)
    email = models.EmailField(unique=True)
    kaydan_subsidiary = models.CharField(
        ("kaydan subsidiary"), max_length=100, blank=True
    )
    is_verified = models.BooleanField(_("Email Verified"), default=False)

    # Email verification fields
    email_verified = models.BooleanField(default=False)
    email_verification_code = models.CharField(max_length=6, null=True, blank=True)
    email_verification_code_created_at = models.DateTimeField(null=True, blank=True)

    # Role-based fields
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True)
    created_by = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_users",
    )
    is_active = models.BooleanField(default=True)

    # Fix clashing reverse accessors
    groups = models.ManyToManyField(
        "auth.Group",
        related_name="customuser_set",
        blank=True,
        help_text=_("The groups this user belongs to."),
        verbose_name=_("groups"),
    )
    user_permissions = models.ManyToManyField(
        "auth.Permission",
        related_name="customuser_set",
        blank=True,
        help_text=_("Specific permissions for this user."),
        verbose_name=_("user permissions"),
    )

    class Meta:
        verbose_name = "CustomUser"
        verbose_name_plural = _("CustomUsers")

    def __str__(self):
        return self.email or self.username

    def is_super_admin(self):
        """Check if user is a super admin"""
        return self.role and self.role.name == Role.SUPER_ADMIN

    def is_admin(self):
        """Check if user is an admin"""
        return self.role and self.role.name in [Role.SUPER_ADMIN, Role.ADMIN]

    def can_create_users(self):
        """Check if user can create other users"""
        return self.is_super_admin()

    def can_manage_users(self):
        """Check if user can manage other users"""
        return self.is_admin()
