from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class Role(models.Model):
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    ANALYST = "analyst"
    DECISION_BOARD = "decision_board"
    OPERATION_BOARD = "operation_board"

    ROLE_CHOICES = [
        (SUPER_ADMIN, "Super Admin"),
        (ADMIN, "Admin"),
        (ANALYST, "Analyst"),
        (DECISION_BOARD, "Decision Board"),
        (OPERATION_BOARD, "Operation Board"),
    ]

    name = models.CharField(max_length=50, choices=ROLE_CHOICES, unique=True)
    description = models.TextField(blank=True)
    permissions = models.JSONField(default=dict)

    def __str__(self):
        return self.name


class CustomUser(AbstractUser):
    username = models.Char<PERSON><PERSON>(("username"), max_length=100, unique=True)
    last_name = models.Char<PERSON>ield(("Last Name"), max_length=100)
    first_name = models.CharField(("First Name"), max_length=100)
    email = models.EmailField(unique=True)
    kaydan_subsidiary = models.CharField(
        ("kaydan subsidiary"), max_length=100, blank=True
    )
    is_verified = models.BooleanField(_("Email Verified"), default=False)

    # Email verification fields
    email_verified = models.BooleanField(default=False)
    email_verification_code = models.CharField(max_length=6, null=True, blank=True)
    email_verification_code_created_at = models.DateTimeField(null=True, blank=True)

    # Role-based fields
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True)
    created_by = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_users",
    )
    is_active = models.BooleanField(default=True)

    # Fix clashing reverse accessors
    groups = models.ManyToManyField(
        "auth.Group",
        related_name="customuser_set",
        blank=True,
        help_text=_("The groups this user belongs to."),
        verbose_name=_("groups"),
    )
    user_permissions = models.ManyToManyField(
        "auth.Permission",
        related_name="customuser_set",
        blank=True,
        help_text=_("Specific permissions for this user."),
        verbose_name=_("user permissions"),
    )

    class Meta:
        verbose_name = "CustomUser"
        verbose_name_plural = _("CustomUsers")

    def __str__(self):
        return self.email or self.username

    def is_super_admin(self):
        """Check if user is a super admin"""
        return self.role and self.role.name == Role.SUPER_ADMIN

    def is_admin(self):
        """Check if user is an admin"""
        return self.role and self.role.name in [Role.SUPER_ADMIN, Role.ADMIN]


class Notification(models.Model):
    """
    Model for storing user notifications including messages
    """
    NOTIFICATION_TYPES = [
        ('message', 'Message'),
        ('system', 'System'),
        ('alert', 'Alert'),
        ('broadcast', 'Broadcast'),
    ]
    
    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
    ]
    
    recipient = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_notifications', null=True, blank=True)
    
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, default='message')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')
    
    title = models.CharField(max_length=255)
    message = models.TextField()
    
    # Message specific fields
    is_message = models.BooleanField(default=False)
    can_reply = models.BooleanField(default=False)
    message_id = models.CharField(max_length=100, null=True, blank=True)  # For linking to actual message
    
    # Status fields
    is_read = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.notification_type} notification for {self.recipient.email}: {self.title}"
    
    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()
    
    def mark_as_deleted(self):
        """Mark notification as deleted"""
        self.is_deleted = True
        self.save()


class Message(models.Model):
    """
    Model for storing actual messages between users
    """
    MESSAGE_TYPES = [
        ('individual', 'Individual'),
        ('broadcast', 'Broadcast'),
    ]
    
    sender = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='sent_messages')
    recipients = models.ManyToManyField(CustomUser, related_name='received_messages')
    
    message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='individual')
    title = models.CharField(max_length=255)
    content = models.TextField()
    priority = models.CharField(max_length=10, choices=Notification.PRIORITY_LEVELS, default='medium')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"Message from {self.sender.email} to {self.recipients.count()} recipients: {self.title}"
    
    def create_notifications_for_recipients(self):
        """Create notifications for all recipients"""
        notifications = []
        for recipient in self.recipients.all():
            notification = Notification.objects.create(
                recipient=recipient,
                sender=self.sender,
                notification_type='message',
                priority=self.priority,
                title=self.title,
                message=self.content,
                is_message=True,
                can_reply=self.message_type == 'individual',
                message_id=str(self.id)
            )
            notifications.append(notification)
        return notifications
