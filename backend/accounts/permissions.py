from rest_framework import permissions
from .models import Role


class IsSuperAdmin(permissions.BasePermission):
    """
    Custom permission to only allow super admins.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_super_admin()


class IsAdmin(permissions.BasePermission):
    """
    Custom permission to only allow admins (super admin or admin).
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_admin()


class CanCreateUsers(permissions.BasePermission):
    """
    Custom permission to only allow users who can create other users.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.can_create_users()


class CanManageUsers(permissions.BasePermission):
    """
    Custom permission to only allow users who can manage other users.
    """

    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.can_manage_users()


class IsOwnerOrAdmin(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or admins.
    """

    def has_object_permission(self, request, view, obj):
        # Admin can access any object
        if request.user.is_admin():
            return True

        # Owner can access their own object
        return obj == request.user


class IsOwnerOrSuperAdmin(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object or super admins.
    """

    def has_object_permission(self, request, view, obj):
        # Super admin can access any object
        if request.user.is_super_admin():
            return True

        # Owner can access their own object
        return obj == request.user
