from django.shortcuts import render
from rest_framework.response import Response
from rest_framework import permissions, viewsets

# Add these imports at the top of views.py
from django.db.models import Avg, Sum, Count, Q
from django.utils import timezone
from datetime import timedelta
from rest_framework.decorators import action
from rest_framework import status
from .analytic_engine import AnalyticsEngine
from .models import ProjectAnalytics, StockAnalytics, BusinessKPIs, AlertSystem
from .serializers import (
    ProjectAnalyticsSerializer,
    StockAnalyticsSerializer,
    BusinessKPIsSerializer,
    AlertSystemSerializer,
    DashboardSummarySerializer,
    AnalyticsInsightSerializer,
)

# Imports pour le service de collecte automatique
from celery import shared_task
from django.core.cache import cache
import json
import pandas as pd
from api.models import (
    DQEData, GStockApprovisionnement, GStockSortie, GStockConsommation,
    GStockAchat, GProjet, EcoleTalents, GLocative, ESyndic, Sentinelle, CRM
)

# Analytics API Views
# ========================================================


class AnalyticsViewSet(viewsets.ViewSet):
    """
    ViewSet for analytics operations and insights
    """

    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=["post"])
    def run_analysis(self, request):
        """
        Trigger complete analytics analysis
        """
        try:
            engine = AnalyticsEngine()
            results = engine.run_complete_analysis()

            return Response(
                {
                    "status": "success",
                    "message": "Analytics analysis completed successfully",
                    "results_summary": {
                        "projects_analyzed": len(results.get("project_analytics", [])),
                        "alerts_generated": len(results.get("alerts_generated", [])),
                        "analysis_timestamp": results.get("analysis_timestamp"),
                    },
                    "data": results,
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": f"Analytics analysis failed: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["post"])
    def collect_data(self, request):
        """
        Déclencher la collecte manuelle de données depuis toutes les applications
        """
        try:
            # Lancer la tâche de collecte en arrière-plan avec Celery
            task = collect_all_data_task.delay()

            return Response(
                {
                    "status": "success",
                    "message": "Collecte de données lancée en arrière-plan",
                    "task_id": task.id,
                    "estimated_duration": "2-5 minutes"
                },
                status=status.HTTP_202_ACCEPTED,
            )
        except Exception as e:
            # Fallback vers la collecte directe si Celery n'est pas disponible
            try:
                results = self._simulate_data_collection()
                return Response(
                    {
                        "status": "success",
                        "message": "Collecte de données terminée (mode direct)",
                        "results": results,
                        "estimated_duration": "Completed"
                    },
                    status=status.HTTP_200_OK,
                )
            except Exception as fallback_error:
                return Response(
                    {"status": "error", "message": f"Erreur lors de la collecte: {str(e)} | Fallback: {str(fallback_error)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

    @action(detail=False, methods=["post"])
    def collect_single_source(self, request):
        """
        Déclencher la collecte pour une source spécifique
        """
        try:
            source_id = request.data.get('source_id')
            if not source_id:
                return Response(
                    {"status": "error", "message": "source_id est requis"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Mapping des sources
            source_mapping = {
                'dqe': ('DQE', DQEData),
                'gstock_approvisionnement': ('G-Stock Approvisionnement', GStockApprovisionnement),
                'sentinelle': ('Sentinelle', Sentinelle),
                'gprojet': ('G-Projet', GProjet),
                'crm': ('CRM', CRM),
                'glocative': ('G-Locative', GLocative),
                'esyndic': ('E-Syndic', ESyndic),
                'ecole_talents': ('École des Talents', EcoleTalents),
            }

            if source_id not in source_mapping:
                return Response(
                    {"status": "error", "message": f"Source inconnue: {source_id}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            source_name, model_class = source_mapping[source_id]

            # Simuler la collecte pour cette source spécifique
            result = self._collect_single_source(source_name, model_class)

            return Response(
                {
                    "status": "success",
                    "message": f"Collecte terminée pour {source_name}",
                    "result": result
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": f"Erreur lors de la collecte: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _collect_single_source(self, source_name, model_class):
        """
        Collecte les données réelles depuis les APIs pour une source spécifique
        """
        print(f"🚀 Début de la collecte RÉELLE pour {source_name}...")

        try:
            # Appeler la vraie API selon la source
            api_data = self._fetch_from_real_api(source_name, model_class)

            if api_data:
                # Sauvegarder les nouvelles données
                new_record = model_class.objects.create(
                    title=f"Collecte API {source_name} - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
                    description=f"Données collectées depuis l'API {source_name}",
                    data=api_data
                )

                print(f"💾 Nouvelles données sauvegardées (ID: {new_record.id})")

                # Analyser la structure des données collectées
                self._analyze_collected_data(api_data, source_name)

                result = {
                    'name': source_name,
                    'records': 1,
                    'status': 'success',
                    'collected_at': timezone.now().isoformat(),
                    'new_record_id': new_record.id,
                    'data_structure': self._get_data_structure_info(api_data)
                }

                print(f"✅ {source_name}: Nouvelles données collectées et analysées")
                return result
            else:
                print(f"⚠️ Aucune donnée retournée par l'API {source_name}")
                return {
                    'name': source_name,
                    'status': 'no_data',
                    'error': 'API returned no data',
                    'collected_at': timezone.now().isoformat()
                }

        except Exception as e:
            error_msg = f"Erreur collecte API {source_name}: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                'name': source_name,
                'status': 'error',
                'error': error_msg,
                'collected_at': timezone.now().isoformat()
            }

    def _fetch_from_real_api(self, source_name, model_class):
        """
        Récupère les données depuis les modèles Django directement (pas d'API HTTP)
        """
        print(f"📡 Collecte DIRECTE depuis le modèle pour {source_name}...")

        try:
            # Récupérer toutes les données du modèle
            all_records = model_class.objects.all().order_by('-created_at')
            count = all_records.count()

            print(f"📊 {count} enregistrements trouvés dans {model_class.__name__}")

            if count == 0:
                print(f"⚠️ Aucune donnée dans le modèle {model_class.__name__}")
                return None

            # Prendre les 10 derniers enregistrements pour analyse
            recent_records = all_records[:10]

            # Convertir en format API-like
            records_data = []
            for record in recent_records:
                record_dict = {
                    'id': record.id,
                    'title': record.title,
                    'description': record.description,
                    'created_at': record.created_at.isoformat(),
                    'updated_at': record.updated_at.isoformat(),
                }

                # Ajouter les données JSON si elles existent
                if hasattr(record, 'data') and record.data:
                    if isinstance(record.data, dict):
                        record_dict.update(record.data)
                    else:
                        record_dict['raw_data'] = record.data

                records_data.append(record_dict)

            print(f"📥 {len(records_data)} enregistrements récents extraits")

            # Analyser la structure des données
            if records_data:
                first_record = records_data[0]
                print(f"📋 Clés disponibles: {list(first_record.keys())}")

                # Chercher des données tabulaires dans le premier enregistrement
                tabular_keys = []
                for key, value in first_record.items():
                    if isinstance(value, list) and len(value) > 0:
                        if isinstance(value[0], dict):
                            tabular_keys.append(key)
                            print(f"   📊 Table détectée '{key}': {len(value)} lignes")

                if tabular_keys:
                    print(f"✅ {len(tabular_keys)} tables détectées: {tabular_keys}")
                else:
                    print("⚠️ Aucune table détectée dans les données")

            return {
                'status': 'success',
                'source': source_name,
                'method': 'direct_model_access',
                'data': records_data,
                'timestamp': timezone.now().isoformat(),
                'total_records': count,
                'extracted_records': len(records_data)
            }

        except Exception as e:
            print(f"❌ Erreur accès modèle {model_class.__name__}: {e}")
            return None



    def _analyze_collected_data(self, data, source_name):
        """
        Analyse en détail les données collectées et affiche la structure
        """
        print(f"\n🔍 ANALYSE DÉTAILLÉE DES DONNÉES COLLECTÉES - {source_name}")
        print("=" * 60)

        if isinstance(data, dict):
            print(f"📊 Type: Dictionnaire avec {len(data)} clés principales")

            for key, value in data.items():
                print(f"\n📋 Clé: '{key}'")
                print(f"   Type: {type(value).__name__}")

                if isinstance(value, list):
                    print(f"   Longueur: {len(value)} éléments")
                    if len(value) > 0:
                        first_item = value[0]
                        print(f"   Premier élément: {type(first_item).__name__}")

                        if isinstance(first_item, dict):
                            print(f"   Colonnes détectées: {list(first_item.keys())}")

                            # Créer un DataFrame pour analyse
                            try:
                                df = pd.DataFrame(value)
                                print(f"   📈 DataFrame créé: {df.shape[0]} lignes × {df.shape[1]} colonnes")
                                print(f"   📊 Types de données: {dict(df.dtypes)}")

                                # Afficher un échantillon
                                print(f"   📋 Échantillon (3 premières lignes):")
                                for i, row in enumerate(df.head(3).to_dict('records')):
                                    print(f"      Ligne {i+1}: {row}")

                            except Exception as e:
                                print(f"   ❌ Erreur création DataFrame: {e}")
                        else:
                            print(f"   Valeurs: {value[:3]}{'...' if len(value) > 3 else ''}")

                elif isinstance(value, dict):
                    print(f"   Sous-dictionnaire avec {len(value)} clés: {list(value.keys())}")

                else:
                    print(f"   Valeur: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")

        elif isinstance(data, list):
            print(f"📊 Type: Liste avec {len(data)} éléments")
            if len(data) > 0:
                print(f"Premier élément: {type(data[0]).__name__}")

        else:
            print(f"📊 Type: {type(data).__name__}")
            print(f"Contenu: {str(data)[:200]}{'...' if len(str(data)) > 200 else ''}")

        print("=" * 60)

    def _get_data_structure_info(self, data):
        """
        Retourne un résumé de la structure des données
        """
        structure = {}

        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
                    structure[key] = {
                        'type': 'table',
                        'rows': len(value),
                        'columns': list(value[0].keys()) if value else []
                    }
                else:
                    structure[key] = {
                        'type': type(value).__name__,
                        'value': str(value)[:50] + '...' if len(str(value)) > 50 else str(value)
                    }

        return structure

    def _create_sample_data(self, source_name, model_class):
        """
        Crée des données d'exemple pour démonstration
        """
        try:
            sample_data_map = {
                'DQE': {
                    'projet': 'Résidence Les Palmiers',
                    'budget_total': 2500000000,
                    'postes': [
                        {'nom': 'Gros œuvre', 'montant': 1200000000, 'pourcentage': 48},
                        {'nom': 'Second œuvre', 'montant': 800000000, 'pourcentage': 32},
                        {'nom': 'Finitions', 'montant': 500000000, 'pourcentage': 20}
                    ],
                    'materiaux': [
                        {'nom': 'Ciment', 'quantite': 2500, 'unite': 'tonnes', 'prix_unitaire': 85000},
                        {'nom': 'Fer à béton', 'quantite': 180, 'unite': 'tonnes', 'prix_unitaire': 650000},
                        {'nom': 'Sable', 'quantite': 1200, 'unite': 'm3', 'prix_unitaire': 15000}
                    ],
                    'main_oeuvre': [
                        {'corps_metier': 'Maçonnerie', 'nb_ouvriers': 25, 'duree_jours': 120, 'cout_total': 180000000},
                        {'corps_metier': 'Plomberie', 'nb_ouvriers': 8, 'duree_jours': 45, 'cout_total': 54000000},
                        {'corps_metier': 'Électricité', 'nb_ouvriers': 6, 'duree_jours': 40, 'cout_total': 48000000}
                    ]
                },
                'G-Stock Approvisionnement': {
                    'entrepot': 'Dépôt Central Abidjan',
                    'approvisionnements': [
                        {'materiau': 'Ciment Portland', 'quantite': 500, 'fournisseur': 'CIMIVOIRE', 'date': '2024-08-01', 'prix_total': 42500000},
                        {'materiau': 'Fer 12mm', 'quantite': 50, 'fournisseur': 'SIDERURGIE CI', 'date': '2024-08-03', 'prix_total': 32500000},
                        {'materiau': 'Carrelage 60x60', 'quantite': 2000, 'fournisseur': 'CERAMIQUE PLUS', 'date': '2024-08-05', 'prix_total': 18000000}
                    ],
                    'stock_actuel': {
                        'ciment': {'quantite': 1250, 'valeur': 106250000},
                        'fer': {'quantite': 125, 'valeur': 81250000},
                        'carrelage': {'quantite': 5500, 'valeur': 49500000}
                    }
                },
                'Sentinelle': {
                    'programme': 'Les Jardins de Cocody',
                    'avancement_global': 65,
                    'phases': [
                        {'nom': 'Fondations', 'avancement': 100, 'statut': 'Terminé'},
                        {'nom': 'Gros œuvre', 'avancement': 85, 'statut': 'En cours'},
                        {'nom': 'Second œuvre', 'avancement': 45, 'statut': 'En cours'},
                        {'nom': 'Finitions', 'avancement': 0, 'statut': 'Non démarré'}
                    ],
                    'ventes': {
                        'total_logements': 120,
                        'vendus': 78,
                        'reserves': 15,
                        'disponibles': 27,
                        'taux_commercialisation': 65
                    }
                },
                'G-Projet': {
                    'projet': 'Tour Etoile',
                    'taches': [
                        {'nom': 'Études techniques', 'avancement': 100, 'duree_prevue': 30, 'duree_reelle': 28},
                        {'nom': 'Terrassement', 'avancement': 100, 'duree_prevue': 15, 'duree_reelle': 18},
                        {'nom': 'Fondations', 'avancement': 90, 'duree_prevue': 45, 'duree_reelle': 42},
                        {'nom': 'Structure', 'avancement': 60, 'duree_prevue': 90, 'duree_reelle': 54}
                    ],
                    'ressources': [
                        {'type': 'Chef de projet', 'nombre': 1, 'cout_jour': 150000},
                        {'type': 'Ingénieur', 'nombre': 3, 'cout_jour': 100000},
                        {'type': 'Ouvrier qualifié', 'nombre': 25, 'cout_jour': 15000}
                    ]
                },
                'CRM': {
                    'prospects': [
                        {'nom': 'KOUAME Jean', 'telephone': '0701234567', 'email': '<EMAIL>', 'statut': 'Qualifié', 'budget': 45000000},
                        {'nom': 'TRAORE Aminata', 'telephone': '0709876543', 'email': '<EMAIL>', 'statut': 'Négociation', 'budget': 38000000},
                        {'nom': 'DIALLO Moussa', 'telephone': '0705555555', 'email': '<EMAIL>', 'statut': 'Prospect', 'budget': 52000000}
                    ],
                    'ventes_mois': {
                        'janvier': 8, 'février': 12, 'mars': 15, 'avril': 10, 'mai': 18, 'juin': 22, 'juillet': 16, 'août': 14
                    },
                    'ca_mensuel': 1250000000
                },
                'G-Locative': {
                    'patrimoine': 'Résidence Harmonie',
                    'locataires': [
                        {'nom': 'BAMBA Koffi', 'appartement': 'A101', 'loyer': 350000, 'statut': 'À jour'},
                        {'nom': 'KONE Mariam', 'appartement': 'B205', 'loyer': 420000, 'statut': 'Retard 1 mois'},
                        {'nom': 'YAO Patrick', 'appartement': 'C308', 'loyer': 380000, 'statut': 'À jour'}
                    ],
                    'charges': [
                        {'type': 'Électricité communs', 'montant': 180000, 'mois': 'Août 2024'},
                        {'type': 'Eau', 'montant': 95000, 'mois': 'Août 2024'},
                        {'type': 'Gardiennage', 'montant': 240000, 'mois': 'Août 2024'}
                    ]
                },
                'E-Syndic': {
                    'copropriete': 'Les Terrasses du Plateau',
                    'copropriétaires': 45,
                    'incidents': [
                        {'type': 'Ascenseur en panne', 'date': '2024-08-01', 'statut': 'Résolu', 'cout': 850000},
                        {'type': 'Fuite canalisation', 'date': '2024-08-03', 'statut': 'En cours', 'cout': 0},
                        {'type': 'Éclairage parking', 'date': '2024-08-05', 'statut': 'Planifié', 'cout': 320000}
                    ],
                    'charges_trimestrielles': {
                        'entretien': 2400000,
                        'assurance': 1800000,
                        'gardiennage': 3600000,
                        'travaux': 1200000
                    }
                },
                'École des Talents': {
                    'formations': [
                        {'nom': 'Gestion de projet immobilier', 'participants': 25, 'duree': 40, 'taux_reussite': 88},
                        {'nom': 'Techniques de vente', 'participants': 18, 'duree': 24, 'taux_reussite': 94},
                        {'nom': 'Maintenance technique', 'participants': 12, 'duree': 32, 'taux_reussite': 92}
                    ],
                    'statistiques': {
                        'total_formes': 156,
                        'taux_satisfaction': 91,
                        'certifies': 142
                    }
                }
            }

            if source_name in sample_data_map:
                sample_data = sample_data_map[source_name]

                # Créer l'enregistrement
                record = model_class.objects.create(
                    title=f"Données d'exemple {source_name}",
                    description=f"Données de démonstration générées automatiquement pour {source_name}",
                    data=sample_data
                )

                print(f"📝 Données d'exemple créées pour {source_name} (ID: {record.id})")
                return record

        except Exception as e:
            print(f"❌ Erreur création données d'exemple pour {source_name}: {e}")
            return None

    def _simulate_data_collection(self):
        """
        Collecte réelle de toutes les données (version complète)
        """
        print("🚀 Début de la collecte globale de données...")

        results = {
            'status': 'success',
            'collected_sources': [],
            'errors': [],
            'timestamp': timezone.now().isoformat()
        }

        # Sources à collecter
        sources = [
            ('DQE', DQEData),
            ('G-Stock Approvisionnement', GStockApprovisionnement),
            ('Sentinelle', Sentinelle),
            ('G-Projet', GProjet),
            ('CRM', CRM),
            ('G-Locative', GLocative),
            ('E-Syndic', ESyndic),
            ('École des Talents', EcoleTalents),
        ]

        for source_name, model_class in sources:
            try:
                # Utiliser la méthode de collecte individuelle
                result = self._collect_single_source(source_name, model_class)
                results['collected_sources'].append(result)

            except Exception as e:
                error_msg = f"Erreur collecte {source_name}: {str(e)}"
                print(f"❌ {error_msg}")
                results['errors'].append(error_msg)

        print("🎉 Collecte globale terminée avec succès!")
        return results

    @action(detail=False, methods=["get"])
    def data_sources_status(self, request):
        """
        Obtenir le statut de toutes les sources de données internes
        """
        try:
            sources_status = []

            # DQE Data
            dqe_count = DQEData.objects.count()
            dqe_latest = DQEData.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "dqe",
                "name": "DQE (Devis Quantitatif Estimatif)",
                "description": "Données de coûts estimés et réels des projets immobiliers",
                "records": dqe_count,
                "last_updated": dqe_latest.created_at if dqe_latest else None,
                "status": "available" if dqe_count > 0 else "no_data",
                "endpoint": "/api/dqe/"
            })

            # G-Stock Approvisionnement
            gstock_count = GStockApprovisionnement.objects.count()
            gstock_latest = GStockApprovisionnement.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "gstock_approvisionnement",
                "name": "G-Stock Approvisionnement",
                "description": "Données d'approvisionnement et gestion des stocks",
                "records": gstock_count,
                "last_updated": gstock_latest.created_at if gstock_latest else None,
                "status": "available" if gstock_count > 0 else "no_data",
                "endpoint": "/api/gstock-approvisionnement/"
            })

            # Sentinelle
            sentinelle_count = Sentinelle.objects.count()
            sentinelle_latest = Sentinelle.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "sentinelle",
                "name": "Sentinelle",
                "description": "Suivi d'avancement des chantiers et évolution des ventes",
                "records": sentinelle_count,
                "last_updated": sentinelle_latest.created_at if sentinelle_latest else None,
                "status": "available" if sentinelle_count > 0 else "no_data",
                "endpoint": "/api/sentinelle/"
            })

            # G-Projet
            gprojet_count = GProjet.objects.count()
            gprojet_latest = GProjet.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "gprojet",
                "name": "G-Projet",
                "description": "Gestion des tâches et jalons des projets immobiliers",
                "records": gprojet_count,
                "last_updated": gprojet_latest.created_at if gprojet_latest else None,
                "status": "available" if gprojet_count > 0 else "no_data",
                "endpoint": "/api/gprojet/"
            })

            # CRM
            crm_count = CRM.objects.count()
            crm_latest = CRM.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "crm",
                "name": "CRM",
                "description": "Gestion des clients, interactions et statistiques de ventes",
                "records": crm_count,
                "last_updated": crm_latest.created_at if crm_latest else None,
                "status": "available" if crm_count > 0 else "no_data",
                "endpoint": "/api/crm/"
            })

            # G-Locative
            glocative_count = GLocative.objects.count()
            glocative_latest = GLocative.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "glocative",
                "name": "G-Locative",
                "description": "Gestion locative, contrats et paiements",
                "records": glocative_count,
                "last_updated": glocative_latest.created_at if glocative_latest else None,
                "status": "available" if glocative_count > 0 else "no_data",
                "endpoint": "/api/glocative/"
            })

            # E-Syndic
            esyndic_count = ESyndic.objects.count()
            esyndic_latest = ESyndic.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "esyndic",
                "name": "E-Syndic",
                "description": "Gestion de copropriété, charges et incidents",
                "records": esyndic_count,
                "last_updated": esyndic_latest.created_at if esyndic_latest else None,
                "status": "available" if esyndic_count > 0 else "no_data",
                "endpoint": "/api/esyndic/"
            })

            # École des Talents
            ecole_count = EcoleTalents.objects.count()
            ecole_latest = EcoleTalents.objects.order_by('-created_at').first()
            sources_status.append({
                "id": "ecole_talents",
                "name": "École des Talents",
                "description": "Données de formation et développement des compétences",
                "records": ecole_count,
                "last_updated": ecole_latest.created_at if ecole_latest else None,
                "status": "available" if ecole_count > 0 else "no_data",
                "endpoint": "/api/ecole-talents/"
            })

            return Response(
                {
                    "status": "success",
                    "total_sources": len(sources_status),
                    "sources": sources_status,
                    "last_check": timezone.now()
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": f"Erreur lors de la récupération du statut: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"], permission_classes=[])
    def get_dataframes_preview(self, request):
        """
        Obtenir un aperçu des dataframes pour toutes les applications avec données réelles
        """
        try:
            dataframes_preview = {}

            # Configuration des sources basée sur l'analyse réelle
            sources_config = [
                ('dqe', 'DQE Data', DQEData, ['postes', 'materiaux', 'main_oeuvre']),
                ('gstock_approvisionnement', 'G-Stock Approvisionnement', GStockApprovisionnement, ['approvisionnements', 'stock_actuel']),
                ('sentinelle', 'Sentinelle', Sentinelle, ['phases', 'ventes']),
                ('crm', 'CRM', CRM, ['prospects', 'ventes_mensuelles']),
                ('glocative', 'G-Locative', GLocative, ['locataires', 'charges']),
                ('ecole_talents', 'École des Talents', EcoleTalents, ['formations']),
                ('esyndic', 'E-Syndic', ESyndic, ['incidents']),
                ('gprojet', 'G-Projet', GProjet, ['taches', 'ressources']),
            ]

            for source_id, source_name, model_class, expected_tables in sources_config:
                try:
                    if model_class.objects.exists():
                        latest_record = model_class.objects.order_by('-created_at').first()
                        if latest_record and latest_record.data:
                            # Extraire les tableaux spécifiques
                            tables = self._extract_specific_tables(latest_record.data, expected_tables, source_name)

                            if tables:
                                dataframes_preview[source_id] = {
                                    'name': source_name,
                                    'tables': tables,
                                    'last_updated': latest_record.created_at.isoformat(),
                                    'total_tables': len(tables),
                                    'record_id': latest_record.id
                                }
                            else:
                                dataframes_preview[source_id] = {
                                    'name': source_name,
                                    'error': 'Aucune donnée tabulaire trouvée',
                                    'last_updated': latest_record.created_at.isoformat()
                                }
                    else:
                        dataframes_preview[source_id] = {
                            'name': source_name,
                            'error': 'Aucune donnée disponible',
                            'last_updated': None
                        }

                except Exception as e:
                    print(f"Erreur pour {source_name}: {e}")
                    dataframes_preview[source_id] = {
                        'name': source_name,
                        'error': f'Erreur: {str(e)}',
                        'last_updated': None
                    }

            return Response(
                {
                    "status": "success",
                    "dataframes": dataframes_preview,
                    "total_sources": len(dataframes_preview),
                    "generated_at": timezone.now().isoformat()
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": f"Erreur lors de la génération des previews: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"], permission_classes=[])
    def get_single_source_preview(self, request):
        """
        Obtenir un aperçu des dataframes pour une source spécifique
        """
        try:
            source_id = request.query_params.get('source_id')
            if not source_id:
                return Response(
                    {"status": "error", "message": "source_id est requis"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Configuration des sources basée sur l'analyse réelle complète
            sources_config = {
                'dqe': ('DQE Data', DQEData, ['postes', 'materiaux', 'main_oeuvre']),
                'gstock_approvisionnement': ('G-Stock Approvisionnement', GStockApprovisionnement, ['approvisionnements', 'stock_actuel']),
                'gstock_sortie': ('G-Stock Sortie', GStockSortie, ['sorties', 'mouvements']),
                'gstock_consommation': ('G-Stock Consommation', GStockConsommation, ['consommations', 'analyses']),
                'gstock_achat': ('G-Stock Achat', GStockAchat, ['achats', 'fournisseurs', 'commandes']),
                'sentinelle': ('Sentinelle', Sentinelle, ['phases', 'ventes']),
                'gprojet': ('G-Projet', GProjet, ['taches', 'ressources', 'jalons', 'risques']),
                'crm': ('CRM', CRM, ['prospects', 'ventes_mensuelles', 'clients']),
                'glocative': ('G-Locative', GLocative, ['data', 'locataires', 'charges', 'paiements']),
                'esyndic': ('E-Syndic', ESyndic, ['data', 'incidents', 'charges_trimestrielles', 'assemblees']),
                'ecole_talents': ('École des Talents', EcoleTalents, ['data', 'formations'])
            }

            if source_id not in sources_config:
                return Response(
                    {"status": "error", "message": f"Source inconnue: {source_id}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            source_name, model_class, expected_tables = sources_config[source_id]

            # Vérifier si des données existent
            if not model_class.objects.exists():
                return Response(
                    {
                        "status": "success",
                        "source": {
                            "id": source_id,
                            "name": source_name,
                            "error": "Aucune donnée disponible",
                            "tables": {}
                        }
                    },
                    status=status.HTTP_200_OK,
                )

            # Récupérer les données - chercher dans tous les enregistrements
            all_records = model_class.objects.order_by('-created_at')[:10]  # 10 derniers

            if not all_records:
                return Response(
                    {
                        "status": "success",
                        "source": {
                            "id": source_id,
                            "name": source_name,
                            "error": "Aucune donnée disponible",
                            "tables": {}
                        }
                    },
                    status=status.HTTP_200_OK,
                )

            # Chercher TOUTES les tables dans tous les enregistrements (pas seulement les attendues)
            print(f"🔍 Recherche INTELLIGENTE de tables dans {len(all_records)} enregistrements pour {source_name}")

            tables = {}
            for i, record in enumerate(all_records):
                print(f"   📝 Enregistrement {i+1}: {record.title}")

                if record.data:
                    # Construire l'objet complet de l'enregistrement
                    record_dict = {
                        'id': record.id,
                        'title': record.title,
                        'description': record.description,
                        'created_at': record.created_at.isoformat(),
                        'updated_at': record.updated_at.isoformat(),
                    }

                    # Ajouter les données JSON
                    if isinstance(record.data, dict):
                        record_dict.update(record.data)
                    else:
                        record_dict['raw_data'] = record.data

                    # Extraire TOUTES les tables trouvées (pas seulement les attendues)
                    record_tables = self._find_all_tables_in_record(record_dict, source_name, i+1)

                    # Ajouter les nouvelles tables trouvées
                    for table_name, table_info in record_tables.items():
                        if table_name not in tables:  # Prendre la première occurrence
                            tables[table_name] = table_info
                            print(f"✅ Table '{table_name}' trouvée dans enregistrement {i+1}")
                        elif table_info['shape'][0] > tables[table_name]['shape'][0]:
                            # Prendre celle avec plus de lignes
                            tables[table_name] = table_info
                            print(f"🔄 Table '{table_name}' mise à jour (plus de lignes) depuis enregistrement {i+1}")

            print(f"📊 Total final: {len(tables)} tables extraites")

            # Limiter à 7-8 lignes par table
            for table_key, table_data in tables.items():
                if 'sample_data' in table_data and len(table_data['sample_data']) > 8:
                    table_data['sample_data'] = table_data['sample_data'][:8]
                    table_data['truncated'] = True
                else:
                    table_data['truncated'] = False

            return Response(
                {
                    "status": "success",
                    "source": {
                        "id": source_id,
                        "name": source_name,
                        "tables": tables,
                        "last_updated": all_records[0].created_at.isoformat(),
                        "total_tables": len(tables),
                        "searched_records": len(all_records)
                    }
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"status": "error", "message": f"Erreur lors de la récupération: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _extract_specific_tables(self, raw_data, expected_tables, source_name):
        """
        Extrait les tableaux spécifiques basés sur l'analyse des données réelles
        """
        tables = {}

        try:
            print(f"🔍 Extraction tables pour {source_name}")
            print(f"📋 Type de données reçues: {type(raw_data)}")

            # Si c'est une liste (résultat de collecte), prendre le premier élément avec des données
            if isinstance(raw_data, list):
                print(f"📊 Liste de {len(raw_data)} enregistrements")

                # Chercher dans tous les enregistrements pour trouver les tables
                for i, record in enumerate(raw_data):
                    print(f"   📝 Enregistrement {i+1}: {record.get('title', 'Sans titre')}")

                    if isinstance(record, dict):
                        # Chercher les tables dans cet enregistrement
                        found_tables = self._find_tables_in_record(record, expected_tables, source_name)

                        # Ajouter les tables trouvées
                        for table_name, table_info in found_tables.items():
                            if table_name not in tables:  # Prendre la première occurrence trouvée
                                tables[table_name] = table_info
                                print(f"✅ Table '{table_name}' trouvée dans enregistrement {i+1}")

            elif isinstance(raw_data, dict):
                # Données directes
                tables = self._find_tables_in_record(raw_data, expected_tables, source_name)

            print(f"🎯 Total tables extraites: {len(tables)}")
            return tables

        except Exception as e:
            print(f"❌ Erreur générale extraction tables pour {source_name}: {e}")
            return {}

    def _find_all_tables_in_record(self, record, source_name, record_num):
        """
        Trouve TOUTES les tables dans un enregistrement (recherche récursive)
        """
        tables = {}

        def extract_tables_recursive(data, path="", level=0):
            """Extraction récursive de toutes les tables"""
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key

                    if isinstance(value, list) and len(value) > 0:
                        if isinstance(value[0], dict):
                            # C'est une table !
                            try:
                                df = pd.DataFrame(value)

                                # Nettoyer les noms de colonnes
                                df.columns = [str(col).replace('_', ' ').title() for col in df.columns]

                                # Nettoyer les valeurs NaN pour éviter les erreurs JSON
                                df_clean = df.fillna('')  # Remplacer NaN par des chaînes vides

                                table_key = key
                                tables[table_key] = {
                                    'name': key.replace('_', ' ').title(),
                                    'shape': df.shape,
                                    'columns': df_clean.columns.tolist(),
                                    'sample_data': df_clean.head(8).to_dict('records'),
                                    'data_types': df.dtypes.astype(str).to_dict(),
                                    'description': self._get_table_description(key, source_name),
                                    'path': current_path,
                                    'source_record': record_num
                                }

                                print(f"    📊 Table détectée '{key}': {df.shape[0]} lignes × {df.shape[1]} colonnes")

                            except Exception as e:
                                print(f"    ❌ Erreur DataFrame pour '{key}': {e}")

                    elif isinstance(value, dict):
                        # Recherche récursive dans les sous-dictionnaires
                        extract_tables_recursive(value, current_path, level + 1)

            elif isinstance(data, list) and len(data) > 0:
                # Si c'est une liste d'objets, analyser le premier
                if isinstance(data[0], dict):
                    extract_tables_recursive(data[0], path, level)

        # Lancer l'extraction récursive
        extract_tables_recursive(record)

        return tables

    def _find_tables_in_record(self, record, expected_tables, source_name):
        """
        Cherche les tables spécifiques dans un enregistrement (pour compatibilité)
        """
        # Utiliser la nouvelle méthode intelligente
        all_tables = self._find_all_tables_in_record(record, source_name, 1)

        # Filtrer pour ne garder que les tables attendues
        filtered_tables = {}
        for table_name in expected_tables:
            if table_name in all_tables:
                filtered_tables[table_name] = all_tables[table_name]

        return filtered_tables

    def _get_table_description(self, table_name, source_name):
        """
        Retourne une description pour chaque table (toutes les tables détectées)
        """
        descriptions = {
            # DQE
            'postes': 'Répartition budgétaire par poste de travaux',
            'materiaux': 'Liste des matériaux avec quantités et prix',
            'main_oeuvre': 'Détail de la main d\'œuvre par corps de métier',

            # G-Stock
            'approvisionnements': 'Historique des approvisionnements',
            'stock_actuel': 'État actuel des stocks avec seuils d\'alerte',

            # Sentinelle
            'phases': 'Avancement des phases de construction',
            'ventes': 'Statistiques de vente par typologie',

            # CRM
            'prospects': 'Liste des prospects et clients potentiels',
            'ventes_mensuelles': 'Évolution des ventes par mois',
            'clients': 'Base de données clients existants',

            # G-Locative
            'locataires': 'Informations détaillées sur les locataires',
            'charges': 'Détail des charges locatives par type',
            'paiements': 'Historique des paiements de loyers et charges',

            # E-Syndic
            'incidents': 'Historique des incidents et pannes signalés',
            'charges_trimestrielles': 'Charges de copropriété par trimestre',
            'assemblees': 'Historique des assemblées générales',
            'proprietaires': 'Informations sur les propriétaires',
            'copropriétaires': 'Liste des copropriétaires',

            # École des Talents
            'formations': 'Catalogue des formations dispensées',
            'participants': 'Liste des participants aux formations',
            'certifications': 'Certifications délivrées',

            # G-Projet
            'taches': 'Planning et suivi des tâches projet',
            'ressources': 'Allocation des ressources humaines',
            'jalons': 'Jalons et étapes importantes du projet',
            'risques': 'Identification et suivi des risques',

            # Tables de métadonnées (collecte)
            'data': 'Métadonnées et informations de collecte',

            # Tables imbriquées détectées automatiquement
            'evenements': 'Événements et activités programmés',
            'interventions': 'Interventions techniques effectuées',
            'factures': 'Facturation et comptabilité',
            'contrats': 'Contrats et accords',
            'maintenances': 'Opérations de maintenance',
            'equipements': 'Inventaire des équipements'
        }

        return descriptions.get(table_name, f'Table {table_name.replace("_", " ").title()} de {source_name}')


# ========================================================
# TÂCHES CELERY POUR COLLECTE AUTOMATIQUE
# ========================================================

@shared_task
def collect_all_data_task():
    """
    Tâche Celery pour collecter les données de toutes les applications
    """
    try:
        print("🚀 Début de la collecte automatique de données...")

        results = {
            'status': 'success',
            'collected_sources': [],
            'errors': [],
            'timestamp': timezone.now().isoformat()
        }

        # Simuler la collecte pour chaque source
        sources = [
            ('DQE', DQEData),
            ('G-Stock Approvisionnement', GStockApprovisionnement),
            ('Sentinelle', Sentinelle),
            ('G-Projet', GProjet),
            ('CRM', CRM),
            ('G-Locative', GLocative),
            ('E-Syndic', ESyndic),
            ('École des Talents', EcoleTalents),
        ]

        for source_name, model_class in sources:
            try:
                print(f"📊 Collecte des données {source_name}...")

                # Simuler un délai de collecte
                import time
                time.sleep(2)

                # Compter les enregistrements existants
                count = model_class.objects.count()

                results['collected_sources'].append({
                    'name': source_name,
                    'records': count,
                    'status': 'success' if count > 0 else 'no_data',
                    'collected_at': timezone.now().isoformat()
                })

                print(f"✅ {source_name}: {count} enregistrements collectés")

            except Exception as e:
                error_msg = f"Erreur collecte {source_name}: {str(e)}"
                print(f"❌ {error_msg}")
                results['errors'].append(error_msg)

        print("🎉 Collecte automatique terminée avec succès!")
        return results

    except Exception as e:
        error_msg = f"Erreur générale lors de la collecte: {str(e)}"
        print(f"💥 {error_msg}")
        return {
            'status': 'error',
            'message': error_msg,
            'timestamp': timezone.now().isoformat()
        }


@shared_task
def scheduled_data_collection():
    """
    Tâche planifiée pour collecter les données toutes les 3 jours
    """
    print("⏰ Collecte planifiée démarrée (toutes les 3 jours)")
    return collect_all_data_task()

    @action(detail=False, methods=["get"])
    def dashboard_summary(self, request):
        """
        Get dashboard summary data
        """
        try:
            # Calculate summary metrics
            recent_projects = ProjectAnalytics.objects.filter(
                analysis_date__gte=timezone.now() - timedelta(days=30)
            )

            active_alerts = AlertSystem.objects.filter(is_resolved=False)
            latest_kpis = BusinessKPIs.objects.order_by("-kpi_date").first()
            latest_stock = StockAnalytics.objects.order_by("-created_at").first()

            summary_data = {
                "total_projects": recent_projects.count(),
                "active_projects": recent_projects.filter(
                    completion_percentage__lt=100
                ).count(),
                "completed_projects": recent_projects.filter(
                    completion_percentage=100
                ).count(),
                "projects_at_risk": recent_projects.filter(
                    delivery_performance="At-risk"
                ).count(),
                "total_budget": recent_projects.aggregate(Sum("estimated_budget"))[
                    "estimated_budget__sum"
                ]
                or 0,
                "total_spent": recent_projects.aggregate(Sum("actual_cost"))[
                    "actual_cost__sum"
                ]
                or 0,
                "average_budget_variance": recent_projects.aggregate(
                    Avg("budget_variance_percentage")
                )["budget_variance_percentage__avg"]
                or 0,
                "total_savings": 0,  # Calculate from budget variance
                "overall_completion_rate": recent_projects.aggregate(
                    Avg("completion_percentage")
                )["completion_percentage__avg"]
                or 0,
                "on_time_delivery_rate": self._calculate_on_time_rate(recent_projects),
                "average_project_duration": recent_projects.aggregate(
                    Avg("actual_duration")
                )["actual_duration__avg"]
                or 0,
                "total_stock_value": (
                    latest_stock.total_stock_value if latest_stock else 0
                ),
                "stock_efficiency_score": self._calculate_stock_efficiency_score(
                    latest_stock
                ),
                "active_alerts_count": active_alerts.count(),
                "performance_trend": self._calculate_performance_trend(),
                "budget_trend": self._calculate_budget_trend(),
                "last_analysis_date": (
                    recent_projects.order_by("-analysis_date").first().analysis_date
                    if recent_projects.exists()
                    else None
                ),
            }

            # Calculate total savings
            budget_savings = (
                recent_projects.filter(budget_variance__lt=0).aggregate(
                    Sum("budget_variance")
                )["budget_variance__sum"]
                or 0
            )
            summary_data["total_savings"] = abs(budget_savings)

            serializer = DashboardSummarySerializer(summary_data)

            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Failed to generate dashboard summary: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def insights(self, request):
        """
        Get AI-powered insights and recommendations
        """
        try:
            insights = self._generate_insights()

            serializer = AnalyticsInsightSerializer(insights, many=True)

            return Response(
                {"status": "success", "count": len(insights), "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Failed to generate insights: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def performance_trends(self, request):
        """
        Get performance trends over time
        """
        try:
            # Get trend data for the last 6 months
            six_months_ago = timezone.now() - timedelta(days=180)

            # Project completion trends
            projects_by_month = (
                ProjectAnalytics.objects.filter(analysis_date__gte=six_months_ago)
                .extra(select={"month": "DATE_TRUNC('month', analysis_date)"})
                .values("month")
                .annotate(
                    avg_completion=Avg("completion_percentage"),
                    avg_budget_variance=Avg("budget_variance_percentage"),
                    project_count=Count("id"),
                )
                .order_by("month")
            )

            # KPI trends
            kpi_trends = (
                BusinessKPIs.objects.filter(kpi_date__gte=six_months_ago)
                .order_by("kpi_date")
                .values(
                    "kpi_date",
                    "project_completion_rate",
                    "gross_profit_margin",
                    "roi_percentage",
                )
            )

            # Stock performance trends
            stock_trends = (
                StockAnalytics.objects.filter(period_start__gte=six_months_ago)
                .order_by("period_start")
                .values(
                    "period_start",
                    "stock_turnover_ratio",
                    "total_stock_value",
                    "stockout_incidents",
                )
            )

            return Response(
                {
                    "status": "success",
                    "data": {
                        "project_trends": list(projects_by_month),
                        "kpi_trends": list(kpi_trends),
                        "stock_trends": list(stock_trends),
                    },
                },
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": f"Failed to get performance trends: {str(e)}",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _calculate_on_time_rate(self, projects):
        """Calculate on-time delivery rate"""
        total = projects.count()
        if total == 0:
            return 0.0

        on_time = projects.filter(
            Q(delivery_performance="Complete") | Q(delivery_performance="On-track")
        ).count()

        return (on_time / total) * 100

    def _calculate_stock_efficiency_score(self, stock_analytics):
        """Calculate overall stock efficiency score"""
        if not stock_analytics:
            return 0.0

        score = 0
        factors = 0

        if stock_analytics.stock_turnover_ratio:
            score += min(stock_analytics.stock_turnover_ratio * 20, 100)
            factors += 1

        if stock_analytics.demand_forecast_accuracy:
            score += stock_analytics.demand_forecast_accuracy
            factors += 1

        # Inverse of stockout incidents (fewer is better)
        stockout_penalty = min(stock_analytics.stockout_incidents * 10, 50)
        score += 50 - stockout_penalty
        factors += 1

        return score / factors if factors > 0 else 0.0

    def _calculate_performance_trend(self):
        """Calculate overall performance trend"""
        recent_kpis = BusinessKPIs.objects.order_by("-kpi_date")[:2]

        if len(recent_kpis) < 2:
            return "insufficient_data"

        current, previous = recent_kpis[0], recent_kpis[1]

        # Compare completion rates
        if current.project_completion_rate and previous.project_completion_rate:
            if current.project_completion_rate > previous.project_completion_rate:
                return "improving"
            elif current.project_completion_rate < previous.project_completion_rate:
                return "declining"

        return "stable"

    def _calculate_budget_trend(self):
        """Calculate budget performance trend"""
        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=60)
        ).order_by("-analysis_date")

        if recent_projects.count() < 10:
            return "insufficient_data"

        # Split into recent and older halves
        half_point = recent_projects.count() // 2
        recent_half = recent_projects[:half_point]
        older_half = recent_projects[half_point:]

        recent_avg = (
            recent_half.aggregate(Avg("budget_variance_percentage"))[
                "budget_variance_percentage__avg"
            ]
            or 0
        )

        older_avg = (
            older_half.aggregate(Avg("budget_variance_percentage"))[
                "budget_variance_percentage__avg"
            ]
            or 0
        )

        if abs(recent_avg) < abs(older_avg):
            return "improving"
        elif abs(recent_avg) > abs(older_avg):
            return "declining"
        else:
            return "stable"

    def _generate_insights(self):
        """Generate AI-powered insights"""
        insights = []

        # Budget performance insight
        budget_insight = self._analyze_budget_performance()
        if budget_insight:
            insights.append(budget_insight)

        # Project completion insight
        completion_insight = self._analyze_completion_performance()
        if completion_insight:
            insights.append(completion_insight)

        # Stock management insight
        stock_insight = self._analyze_stock_performance()
        if stock_insight:
            insights.append(stock_insight)

        # Risk assessment insight
        risk_insight = self._analyze_risk_factors()
        if risk_insight:
            insights.append(risk_insight)

        return insights

    def _analyze_budget_performance(self):
        """Analyze budget performance and generate insights"""
        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=30),
            budget_variance_percentage__isnull=False,
        )

        if not recent_projects.exists():
            return None

        avg_variance = recent_projects.aggregate(Avg("budget_variance_percentage"))[
            "budget_variance_percentage__avg"
        ]

        overrun_count = recent_projects.filter(budget_variance_percentage__gt=0).count()
        total_count = recent_projects.count()
        overrun_rate = (overrun_count / total_count) * 100 if total_count > 0 else 0

        if avg_variance > 10:
            impact_level = "high"
            recommendation = "Implement stricter budget controls and regular cost monitoring. Consider revising estimation processes."
        elif avg_variance > 5:
            impact_level = "medium"
            recommendation = "Review project planning processes and improve cost estimation accuracy."
        else:
            impact_level = "low"
            recommendation = "Continue current budget management practices. Monitor for early warning signs."

        return {
            "insight_type": "budget_performance",
            "title": f"Budget Variance Analysis: {avg_variance:.1f}% average variance",
            "description": f"{overrun_rate:.1f}% of projects exceeded budget in the last 30 days. Average variance is {avg_variance:.1f}%.",
            "impact_level": impact_level,
            "recommendation": recommendation,
            "supporting_data": {
                "average_variance": avg_variance,
                "overrun_rate": overrun_rate,
                "projects_analyzed": total_count,
            },
            "confidence_score": 0.85,
            "created_at": timezone.now(),
        }

    def _analyze_completion_performance(self):
        """Analyze project completion performance"""
        recent_projects = ProjectAnalytics.objects.filter(
            analysis_date__gte=timezone.now() - timedelta(days=30),
            completion_percentage__isnull=False,
        )

        if not recent_projects.exists():
            return None

        avg_completion = recent_projects.aggregate(Avg("completion_percentage"))[
            "completion_percentage__avg"
        ]

        at_risk_count = recent_projects.filter(delivery_performance="At-risk").count()
        total_count = recent_projects.count()
        at_risk_rate = (at_risk_count / total_count) * 100 if total_count > 0 else 0

        if avg_completion < 70:
            impact_level = "high"
            recommendation = "Urgently review project management processes. Consider additional resources or timeline adjustments."
        elif avg_completion < 85:
            impact_level = "medium"
            recommendation = "Improve project tracking and resource allocation. Identify and address common bottlenecks."
        else:
            impact_level = "low"
            recommendation = "Maintain current project management standards. Share best practices across teams."

        return {
            "insight_type": "completion_performance",
            "title": f"Project Completion: {avg_completion:.1f}% average progress",
            "description": f"{at_risk_rate:.1f}% of projects are at risk of delays. Average completion rate is {avg_completion:.1f}%.",
            "impact_level": impact_level,
            "recommendation": recommendation,
            "supporting_data": {
                "average_completion": avg_completion,
                "at_risk_rate": at_risk_rate,
                "projects_analyzed": total_count,
            },
            "confidence_score": 0.80,
            "created_at": timezone.now(),
        }

    def _analyze_stock_performance(self):
        """Analyze stock management performance"""
        latest_stock = StockAnalytics.objects.order_by("-created_at").first()

        if not latest_stock:
            return None

        # Analyze stock turnover and efficiency
        turnover_ratio = latest_stock.stock_turnover_ratio or 0
        stockout_incidents = latest_stock.stockout_incidents or 0

        if turnover_ratio < 2.0 or stockout_incidents > 5:
            impact_level = "high"
            recommendation = "Optimize inventory levels and improve demand forecasting. Review supplier relationships."
        elif turnover_ratio < 4.0 or stockout_incidents > 2:
            impact_level = "medium"
            recommendation = "Improve stock rotation and implement just-in-time ordering where possible."
        else:
            impact_level = "low"
            recommendation = (
                "Stock management is performing well. Monitor for seasonal variations."
            )

        return {
            "insight_type": "stock_performance",
            "title": f"Stock Management: {turnover_ratio:.1f}x turnover ratio",
            "description": f"Stock turnover ratio is {turnover_ratio:.1f}x with {stockout_incidents} stockout incidents.",
            "impact_level": impact_level,
            "recommendation": recommendation,
            "supporting_data": {
                "turnover_ratio": turnover_ratio,
                "stockout_incidents": stockout_incidents,
                "total_stock_value": float(latest_stock.total_stock_value or 0),
            },
            "confidence_score": 0.75,
            "created_at": timezone.now(),
        }

    def _analyze_risk_factors(self):
        """Analyze overall risk factors"""
        active_alerts = AlertSystem.objects.filter(is_resolved=False)
        critical_alerts = active_alerts.filter(severity="critical").count()
        high_alerts = active_alerts.filter(severity="high").count()

        total_alerts = active_alerts.count()

        if critical_alerts > 0:
            impact_level = "high"
            recommendation = "Address critical alerts immediately. Implement emergency response protocols."
        elif high_alerts > 3:
            impact_level = "medium"
            recommendation = "Prioritize resolution of high-severity alerts. Review alert thresholds."
        elif total_alerts > 10:
            impact_level = "medium"
            recommendation = (
                "Review and resolve pending alerts. Consider alert fatigue management."
            )
        else:
            impact_level = "low"
            recommendation = "Risk levels are manageable. Continue monitoring."

        return {
            "insight_type": "risk_assessment",
            "title": f"Risk Level: {total_alerts} active alerts",
            "description": f"{critical_alerts} critical and {high_alerts} high-severity alerts require attention.",
            "impact_level": impact_level,
            "recommendation": recommendation,
            "supporting_data": {
                "total_alerts": total_alerts,
                "critical_alerts": critical_alerts,
                "high_alerts": high_alerts,
                "medium_alerts": active_alerts.filter(severity="medium").count(),
                "low_alerts": active_alerts.filter(severity="low").count(),
            },
            "confidence_score": 0.90,
            "created_at": timezone.now(),
        }


class ProjectAnalyticsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Project Analytics CRUD operations
    """

    queryset = ProjectAnalytics.objects.all()
    serializer_class = ProjectAnalyticsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = ProjectAnalytics.objects.all()

        # Filter by project name
        project_name = self.request.query_params.get("project_name", None)
        if project_name:
            queryset = queryset.filter(project_name__icontains=project_name)

        # Filter by performance status
        performance = self.request.query_params.get("performance", None)
        if performance:
            queryset = queryset.filter(delivery_performance=performance)

        # Filter by date range
        start_date = self.request.query_params.get("start_date", None)
        end_date = self.request.query_params.get("end_date", None)

        if start_date:
            queryset = queryset.filter(analysis_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(analysis_date__lte=end_date)

        return queryset.order_by("-analysis_date")

    @action(detail=False, methods=["get"])
    def summary_stats(self, request):
        """
        Get summary statistics for projects
        """
        queryset = self.get_queryset()

        stats = {
            "total_projects": queryset.count(),
            "average_completion": queryset.aggregate(Avg("completion_percentage"))[
                "completion_percentage__avg"
            ]
            or 0,
            "average_budget_variance": queryset.aggregate(
                Avg("budget_variance_percentage")
            )["budget_variance_percentage__avg"]
            or 0,
            "total_estimated_budget": queryset.aggregate(Sum("estimated_budget"))[
                "estimated_budget__sum"
            ]
            or 0,
            "total_actual_cost": queryset.aggregate(Sum("actual_cost"))[
                "actual_cost__sum"
            ]
            or 0,
            "projects_over_budget": queryset.filter(
                budget_variance_percentage__gt=0
            ).count(),
            "projects_under_budget": queryset.filter(
                budget_variance_percentage__lt=0
            ).count(),
            "projects_on_track": queryset.filter(
                delivery_performance="On-track"
            ).count(),
            "projects_at_risk": queryset.filter(delivery_performance="At-risk").count(),
            "projects_completed": queryset.filter(
                delivery_performance="Complete"
            ).count(),
        }

        return Response({"status": "success", "data": stats}, status=status.HTTP_200_OK)


class StockAnalyticsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Stock Analytics CRUD operations
    """

    queryset = StockAnalytics.objects.all()
    serializer_class = StockAnalyticsSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=["get"])
    def latest_analysis(self, request):
        """
        Get the latest stock analysis
        """
        latest = StockAnalytics.objects.order_by("-created_at").first()

        if latest:
            serializer = StockAnalyticsSerializer(latest)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "error", "message": "No stock analytics data found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=False, methods=["get"])
    def efficiency_trends(self, request):
        """
        Get stock efficiency trends over time
        """
        three_months_ago = timezone.now() - timedelta(days=90)

        trends = (
            StockAnalytics.objects.filter(created_at__gte=three_months_ago)
            .order_by("created_at")
            .values(
                "period_start",
                "period_end",
                "stock_turnover_ratio",
                "total_stock_value",
                "stockout_incidents",
                "carrying_cost_percentage",
            )
        )

        return Response(
            {"status": "success", "data": list(trends)}, status=status.HTTP_200_OK
        )


class BusinessKPIsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Business KPIs CRUD operations
    """

    queryset = BusinessKPIs.objects.all()
    serializer_class = BusinessKPIsSerializer
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=["get"])
    def latest_kpis(self, request):
        """
        Get the latest business KPIs
        """
        latest = BusinessKPIs.objects.order_by("-kpi_date").first()

        if latest:
            serializer = BusinessKPIsSerializer(latest)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "error", "message": "No KPI data found"},
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=False, methods=["get"])
    def kpi_comparison(self, request):
        """
        Compare current KPIs with previous periods
        """
        kpi_type = request.query_params.get("type", "monthly")

        latest_kpis = BusinessKPIs.objects.filter(kpi_type=kpi_type).order_by(
            "-kpi_date"
        )[
            :6
        ]  # Last 6 periods

        if latest_kpis:
            serializer = BusinessKPIsSerializer(latest_kpis, many=True)
            return Response(
                {"status": "success", "data": serializer.data},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"status": "error", "message": f"No {kpi_type} KPI data found"},
                status=status.HTTP_404_NOT_FOUND,
            )


class AlertSystemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Alert System CRUD operations
    """

    queryset = AlertSystem.objects.all()
    serializer_class = AlertSystemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        queryset = AlertSystem.objects.all()

        # Filter by alert type
        alert_type = self.request.query_params.get("type", None)
        if alert_type:
            queryset = queryset.filter(alert_type=alert_type)

        # Filter by severity
        severity = self.request.query_params.get("severity", None)
        if severity:
            queryset = queryset.filter(severity=severity)

        # Filter by resolution status
        resolved = self.request.query_params.get("resolved", None)
        if resolved is not None:
            is_resolved = resolved.lower() == "true"
            queryset = queryset.filter(is_resolved=is_resolved)

        # Filter by project
        project_name = self.request.query_params.get("project", None)
        if project_name:
            queryset = queryset.filter(project_name__icontains=project_name)

        return queryset.order_by("-created_at")

    @action(detail=True, methods=["post"])
    def resolve(self, request, pk=None):
        """
        Mark an alert as resolved
        """
        alert = self.get_object()
        alert.is_resolved = True
        alert.resolved_at = timezone.now()
        alert.save()

        serializer = AlertSystemSerializer(alert)
        return Response(
            {
                "status": "success",
                "message": "Alert marked as resolved",
                "data": serializer.data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def active_alerts(self, request):
        """
        Get all active (unresolved) alerts
        """
        active_alerts = AlertSystem.objects.filter(is_resolved=False)

        # Group by severity
        alerts_by_severity = {
            "critical": active_alerts.filter(severity="critical"),
            "high": active_alerts.filter(severity="high"),
            "medium": active_alerts.filter(severity="medium"),
            "low": active_alerts.filter(severity="low"),
        }

        response_data = {}
        for severity, queryset in alerts_by_severity.items():
            response_data[severity] = AlertSystemSerializer(queryset, many=True).data

        return Response(
            {
                "status": "success",
                "total_active": active_alerts.count(),
                "data": response_data,
            },
            status=status.HTTP_200_OK,
        )

    @action(detail=False, methods=["get"])
    def alert_statistics(self, request):
        """
        Get alert statistics and trends
        """
        thirty_days_ago = timezone.now() - timedelta(days=30)

        # Current period stats
        current_alerts = AlertSystem.objects.filter(created_at__gte=thirty_days_ago)

        # Previous period stats for comparison
        sixty_days_ago = timezone.now() - timedelta(days=60)
        previous_alerts = AlertSystem.objects.filter(
            created_at__gte=sixty_days_ago, created_at__lt=thirty_days_ago
        )

        stats = {
            "current_period": {
                "total_alerts": current_alerts.count(),
                "by_severity": {
                    "critical": current_alerts.filter(severity="critical").count(),
                    "high": current_alerts.filter(severity="high").count(),
                    "medium": current_alerts.filter(severity="medium").count(),
                    "low": current_alerts.filter(severity="low").count(),
                },
                "by_type": {
                    "budget_overrun": current_alerts.filter(
                        alert_type="budget_overrun"
                    ).count(),
                    "schedule_delay": current_alerts.filter(
                        alert_type="schedule_delay"
                    ).count(),
                    "stock_shortage": current_alerts.filter(
                        alert_type="stock_shortage"
                    ).count(),
                    "quality_issue": current_alerts.filter(
                        alert_type="quality_issue"
                    ).count(),
                    "performance_drop": current_alerts.filter(
                        alert_type="performance_drop"
                    ).count(),
                    "cost_spike": current_alerts.filter(
                        alert_type="cost_spike"
                    ).count(),
                },
                "resolution_rate": self._calculate_resolution_rate(current_alerts),
            },
            "previous_period": {
                "total_alerts": previous_alerts.count(),
                "resolution_rate": self._calculate_resolution_rate(previous_alerts),
            },
        }

        # Calculate trends
        current_total = stats["current_period"]["total_alerts"]
        previous_total = stats["previous_period"]["total_alerts"]

        if previous_total > 0:
            stats["trend"] = {
                "alert_volume_change": (
                    (current_total - previous_total) / previous_total
                )
                * 100,
                "resolution_rate_change": (
                    stats["current_period"]["resolution_rate"]
                    - stats["previous_period"]["resolution_rate"]
                ),
            }
        else:
            stats["trend"] = {"alert_volume_change": 0, "resolution_rate_change": 0}

        return Response({"status": "success", "data": stats}, status=status.HTTP_200_OK)

    def _calculate_resolution_rate(self, alerts_queryset):
        """Calculate the resolution rate for a set of alerts"""
        total = alerts_queryset.count()
        if total == 0:
            return 0.0

        resolved = alerts_queryset.filter(is_resolved=True).count()
        return (resolved / total) * 100
