# Generated by Django 5.2.3 on 2025-08-05 09:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AlertSystem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "alert_type",
                    models.CharField(
                        choices=[
                            ("budget_overrun", "Budget Overrun"),
                            ("schedule_delay", "Schedule Delay"),
                            ("stock_shortage", "Stock Shortage"),
                            ("quality_issue", "Quality Issue"),
                            ("performance_drop", "Performance Drop"),
                            ("cost_spike", "Cost Spike"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        max_length=10,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                (
                    "project_name",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "affected_area",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("threshold_value", models.FloatField(blank=True, null=True)),
                ("actual_value", models.FloatField(blank=True, null=True)),
                ("variance_percentage", models.FloatField(blank=True, null=True)),
                ("is_resolved", models.BooleanField(default=False)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("project_name", models.CharField(max_length=255)),
                ("project_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "estimated_budget",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "actual_cost",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "budget_variance",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                (
                    "budget_variance_percentage",
                    models.FloatField(blank=True, null=True),
                ),
                ("planned_duration", models.IntegerField(blank=True, null=True)),
                ("actual_duration", models.IntegerField(blank=True, null=True)),
                ("completion_percentage", models.FloatField(blank=True, null=True)),
                ("stock_utilization_rate", models.FloatField(blank=True, null=True)),
                ("waste_percentage", models.FloatField(blank=True, null=True)),
                ("stock_cost_efficiency", models.FloatField(blank=True, null=True)),
                ("profitability_index", models.FloatField(blank=True, null=True)),
                (
                    "delivery_performance",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("analysis_date", models.DateTimeField(auto_now_add=True)),
                ("data_sources", models.JSONField(default=list)),
            ],
            options={
                "ordering": ["-analysis_date"],
            },
        ),
        migrations.CreateModel(
            name="StockAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("analysis_period", models.CharField(max_length=20)),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                (
                    "total_stock_value",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("stock_turnover_ratio", models.FloatField(blank=True, null=True)),
                (
                    "average_stock_level",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("stockout_incidents", models.IntegerField(default=0)),
                ("carrying_cost_percentage", models.FloatField(blank=True, null=True)),
                ("demand_forecast_accuracy", models.FloatField(blank=True, null=True)),
                ("supplier_performance", models.FloatField(blank=True, null=True)),
                ("materials_performance", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="BusinessKPIs",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("kpi_date", models.DateField()),
                ("kpi_type", models.CharField(max_length=50)),
                (
                    "total_revenue",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("gross_profit_margin", models.FloatField(blank=True, null=True)),
                ("operating_margin", models.FloatField(blank=True, null=True)),
                ("roi_percentage", models.FloatField(blank=True, null=True)),
                ("project_completion_rate", models.FloatField(blank=True, null=True)),
                ("average_project_delay", models.FloatField(blank=True, null=True)),
                (
                    "customer_satisfaction_score",
                    models.FloatField(blank=True, null=True),
                ),
                ("market_share_percentage", models.FloatField(blank=True, null=True)),
                ("sales_conversion_rate", models.FloatField(blank=True, null=True)),
                ("budget_overrun_frequency", models.FloatField(blank=True, null=True)),
                ("quality_incident_rate", models.FloatField(blank=True, null=True)),
                ("kpi_data", models.JSONField(default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-kpi_date"],
                "unique_together": {("kpi_date", "kpi_type")},
            },
        ),
    ]
