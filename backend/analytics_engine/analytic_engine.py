# analytics_engine.py
import json
import pandas as pd
from decimal import Decimal
from datetime import datetime, timedelta
from django.db.models import Q, Avg, Sum, Count
from django.utils import timezone
from api.models import (
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    GLocative,
    ESyndic,
    Sentinelle,
    CRM,
    UserProfile,
)


class DQEAnalytics:
    """
    Classe d'analyse pour les données DQE
    """
    
    def __init__(self):
        self.model = DQEData
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_materiaux = None
        self.df_main_oeuvre = None
        self.df_prix_unitaires = None
        self.df_totaux_categories = None
    
    def load_data(self):
        """
        Charge les données DQE depuis la base de données
        """
        try:
            latest_dqe = self.model.objects.order_by('-created_at').first()
            
            if not latest_dqe:
                print("Aucune donnée DQE trouvée")
                return False
            
            self.data = latest_dqe.data
            print(f"Données DQE chargées: {latest_dqe.title}")
            print(f"Date de création: {latest_dqe.created_at}")
            
            if isinstance(self.data, list):
                self._parse_dqe_structure()
                return True
            else:
                print("Structure de données invalide")
                return False
                
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False
    
    def _parse_dqe_structure(self):
        """
        Parse la structure des données DQE
        """
        try:
            all_elements = []
            
            for element in self.data:
                element_data = {
                    'ID': element.get('id', 0),
                    'Nom': element.get('nom', ''),
                    'Programme': element.get('programme', ''),
                    'Corps_Etat': element.get('corps_etat', ''),
                    'Type': element.get('type', ''),
                    'Unite_Name': element.get('unite_name', ''),
                    'Quantite': element.get('quantite', 0),
                    'PU_Materiel': element.get('pu_materiel', 0),
                    'PU_Main_Oeuvre': element.get('pu_main_oeuvre', 0),
                    'PT_Materiel': element.get('pt_materiel', 0),
                    'PT_Main_Oeuvre': element.get('pt_main_oeuvre', 0),
                    'PT_Total': element.get('pt_total', 0)
                }
                all_elements.append(element_data)
            
            self.df_raw = pd.DataFrame(all_elements)
            print(f"Structure parsée: {len(all_elements)} éléments extraits")
            
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")
    
    def clean_data(self):
        """
        Nettoie et formate les données DQE
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        
        try:
            self.df_clean = self.df_raw.copy()
            
            # Nettoyer les valeurs numériques
            numeric_columns = ['ID', 'Quantite', 'PU_Materiel', 'PU_Main_Oeuvre', 
                             'PT_Materiel', 'PT_Main_Oeuvre', 'PT_Total']
            
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)
            
            # Nettoyer les chaînes de caractères
            string_columns = ['Nom', 'Programme', 'Corps_Etat', 'Type', 'Unite_Name']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            
            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Nom'] != '') &
                (self.df_clean['Nom'] != 'nan') &
                (self.df_clean['PT_Total'] > 0)
            ]
            
            self._create_specialized_dataframes()
            
            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True
            
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False
    
    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des matériaux (PT_Materiel > 0)
        self.df_materiaux = self.df_clean[self.df_clean['PT_Materiel'] > 0].copy()
        
        # DataFrame de la main d'œuvre (PT_Main_Oeuvre > 0)
        self.df_main_oeuvre = self.df_clean[self.df_clean['PT_Main_Oeuvre'] > 0].copy()
        
        # DataFrame des prix unitaires
        self.df_prix_unitaires = self.df_clean[['Nom', 'PU_Materiel', 'PU_Main_Oeuvre', 'Unite_Name']].copy()
        self.df_prix_unitaires = self.df_prix_unitaires[
            (self.df_prix_unitaires['PU_Materiel'] > 0) | 
            (self.df_prix_unitaires['PU_Main_Oeuvre'] > 0)
        ]
        
        # DataFrame des totaux par catégorie
        self.df_totaux_categories = self.df_clean.groupby(['Type', 'Corps_Etat']).agg({
            'PT_Materiel': 'sum',
            'PT_Main_Oeuvre': 'sum',
            'PT_Total': 'sum',
            'Quantite': 'sum'
        }).round(2).reset_index()
    
    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return
        
        print("ANALYSE DES DONNEES DQE")
        print("Nombre total d'elements:", len(self.df_clean))
        print("Programmes:", self.df_clean['Programme'].nunique())
        print("Corps d'etat:", self.df_clean['Corps_Etat'].nunique())
        print("Types d'elements:", self.df_clean['Type'].nunique())
        print("Montant total:", self.df_clean['PT_Total'].sum(), "FCFA")

        if not self.df_materiaux.empty:
            print("\nMATERIAUX")
            print(self.df_materiaux.head(10).to_string())

        if not self.df_main_oeuvre.empty:
            print("\nMAIN D'OEUVRE")
            print(self.df_main_oeuvre.head(10).to_string())

        print("\nPRIX UNITAIRES")
        print(self.df_prix_unitaires.head(10).to_string())

        print("\nTOTAUX PAR CATEGORIE")
        print(self.df_totaux_categories.to_string())


class GStockApprovisionnementAnalytics:
    """
    Classe d'analyse pour les données G-Stock Approvisionnement
    """
    
    def __init__(self):
        self.model = GStockApprovisionnement
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_approvisionnements = None
        self.df_produits = None
        self.df_fournisseurs = None
        self.df_programmes = None
    
    def load_data(self):
        """
        Charge les données G-Stock Approvisionnement depuis la base de données
        """
        try:
            latest_gstock = self.model.objects.order_by('-created_at').first()
            
            if not latest_gstock:
                print("Aucune donnée G-Stock Approvisionnement trouvée")
                return False
            
            self.data = latest_gstock.data
            print(f"Données G-Stock chargées: {latest_gstock.title}")
            print(f"Date de création: {latest_gstock.created_at}")
            
            if isinstance(self.data, list):
                self._parse_gstock_structure()
                return True
            else:
                print("Structure de données invalide")
                return False
                
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False
    
    def _parse_gstock_structure(self):
        """
        Parse la structure des données G-Stock
        """
        try:
            all_items = []
            
            for item in self.data:
                item_data = {
                    'Approvisionnement_ID': item.get('id', 0),
                    'Reference': item.get('reference', ''),
                    'Programme_ID': item.get('programme_id', 0),
                    'Programme_Nom': item.get('programme', {}).get('nom', ''),
                    'Fournisseur_ID': item.get('fournisseur_id', 0),
                    'Fournisseur_Nom': item.get('fournisseur', {}).get('nom', ''),
                    'Produit_ID': item.get('produit_id', 0),
                    'Code_Produit': item.get('produit', {}).get('code', ''),
                    'Libelle_Produit': item.get('produit', {}).get('libelle', ''),
                    'Quantite': item.get('quantite', 0),
                    'Date_Approvisionnement': item.get('date_approvisionnement', ''),
                    'Valeur_Totale': item.get('quantite', 0) * item.get('produit', {}).get('prix_unitaire', 0)
                }
                all_items.append(item_data)
            
            self.df_raw = pd.DataFrame(all_items)
            print(f"Structure parsée: {len(all_items)} éléments extraits")
            
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")
    
    def clean_data(self):
        """
        Nettoie et formate les données G-Stock
        """
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        
        try:
            self.df_clean = self.df_raw.copy()
            
            # Nettoyer les valeurs numériques
            numeric_columns = ['Approvisionnement_ID', 'Programme_ID', 'Fournisseur_ID', 
                             'Produit_ID', 'Quantite', 'Valeur_Totale']
            
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)
            
            # Nettoyer les chaînes de caractères
            string_columns = ['Reference', 'Programme_Nom', 'Fournisseur_Nom', 'Code_Produit', 'Libelle_Produit']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            
            # Convertir les dates
            if 'Date_Approvisionnement' in self.df_clean.columns:
                self.df_clean['Date_Approvisionnement'] = pd.to_datetime(self.df_clean['Date_Approvisionnement'], errors='coerce')
            
            # Filtrer les données valides
            self.df_clean = self.df_clean[
                (self.df_clean['Reference'] != '') &
                (self.df_clean['Reference'] != 'nan') &
                (self.df_clean['Quantite'] > 0)
            ]
            
            self._create_specialized_dataframes()
            
            print(f"Données nettoyées: {len(self.df_clean)} éléments valides")
            return True
            
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        """
        Crée des DataFrames spécialisés pour différents types d'analyse
        """
        # DataFrame des approvisionnements groupés
        if not self.df_clean.empty:
            self.df_approvisionnements = self.df_clean.groupby(['Approvisionnement_ID', 'Reference', 'Programme_Nom']).agg({
                'Quantite': 'sum',
                'Valeur_Totale': 'sum',
                'Produit_ID': 'count'
            }).rename(columns={'Produit_ID': 'Nombre_Produits'}).reset_index()
            self.df_approvisionnements.columns = ['Approvisionnement_ID', 'Reference', 'Programme', 'Quantite_Totale', 'Valeur_Totale', 'Nombre_Produits']

            # DataFrame des produits
            self.df_produits = self.df_clean.groupby(['Produit_ID', 'Code_Produit', 'Libelle_Produit']).agg({
                'Quantite': 'sum',
                'Valeur_Totale': 'sum',
                'Approvisionnement_ID': 'count'
            }).rename(columns={'Approvisionnement_ID': 'Nombre_Approvisionnements'}).reset_index()
            self.df_produits.columns = ['Produit_ID', 'Code_Produit', 'Libelle_Produit', 'Quantite_Totale', 'Valeur_Totale', 'Nombre_Approvisionnements']

            # DataFrame des fournisseurs
            self.df_fournisseurs = self.df_clean.groupby(['Fournisseur_ID', 'Fournisseur_Nom']).agg({
                'Quantite': 'sum',
                'Valeur_Totale': 'sum',
                'Approvisionnement_ID': 'count'
            }).rename(columns={'Approvisionnement_ID': 'Nombre_Approvisionnements'}).reset_index()
            self.df_fournisseurs.columns = ['Fournisseur_ID', 'Fournisseur_Nom', 'Quantite_Totale', 'Valeur_Totale', 'Nombre_Approvisionnements']

            # DataFrame des programmes
            self.df_programmes = self.df_clean.groupby(['Programme_ID', 'Programme_Nom']).agg({
                'Quantite': 'sum',
                'Valeur_Totale': 'sum',
                'Approvisionnement_ID': 'nunique'
            }).rename(columns={'Approvisionnement_ID': 'Nombre_Approvisionnements'}).reset_index()
            self.df_programmes.columns = ['Programme_ID', 'Programme_Nom', 'Quantite_Totale', 'Valeur_Totale', 'Nombre_Approvisionnements']

    def display_dataframes(self):
        """
        Affiche tous les DataFrames organisés par type
        """
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher. Veuillez d'abord charger et nettoyer les données.")
            return

        print("ANALYSE DES DONNEES G-STOCK APPROVISIONNEMENT")
        print("Nombre total d'elements:", len(self.df_clean))
        print("Programmes:", self.df_clean['Programme_Nom'].nunique())
        print("Approvisionnements:", self.df_clean['Approvisionnement_ID'].nunique())
        print("Fournisseurs:", self.df_clean['Fournisseur_ID'].nunique())
        print("Produits differents:", self.df_clean['Produit_ID'].nunique())
        print("Quantite totale:", self.df_clean['Quantite'].sum())

        if not self.df_approvisionnements.empty:
            print("\nAPPROVISIONNEMENTS")
            print(self.df_approvisionnements.head(10).to_string())

        if not self.df_produits.empty:
            print("\nPRODUITS")
            print(self.df_produits.head(10).to_string())

        if not self.df_fournisseurs.empty:
            print("\nFOURNISSEURS")
            print(self.df_fournisseurs.head(10).to_string())

        if not self.df_programmes.empty:
            print("\nPROGRAMMES")
            print(self.df_programmes.to_string())


class GStockSortieAnalytics:
    """
    Classe d'analyse pour les données G-Stock Sortie
    """

    def __init__(self):
        self.model = GStockSortie
        self.data = None
        self.df_raw = None
        self.df_clean = None

    def load_data(self):
        try:
            latest_sortie = self.model.objects.order_by('-created_at').first()
            if not latest_sortie:
                print("Aucune donnée G-Stock Sortie trouvée")
                return False
            self.data = latest_sortie.data
            print(f"Données G-Stock Sortie chargées: {latest_sortie.title}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def clean_data(self):
        print("Données G-Stock Sortie prêtes pour traitement")
        return True

    def display_dataframes(self):
        print("ANALYSE DES DONNEES G-STOCK SORTIE")
        print("En attente de données")


class GStockConsommationAnalytics:
    """
    Classe d'analyse pour les données G-Stock Consommation
    """

    def __init__(self):
        self.model = GStockConsommation
        self.data = None
        self.df_raw = None
        self.df_clean = None

    def load_data(self):
        try:
            latest_consommation = self.model.objects.order_by('-created_at').first()
            if not latest_consommation:
                print("Aucune donnée G-Stock Consommation trouvée")
                return False
            self.data = latest_consommation.data
            print(f"Données G-Stock Consommation chargées: {latest_consommation.title}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def clean_data(self):
        print("Données G-Stock Consommation prêtes pour traitement")
        return True

    def display_dataframes(self):
        print("ANALYSE DES DONNEES G-STOCK CONSOMMATION")
        print("En attente de données")


class GStockAchatAnalytics:
    """
    Classe d'analyse pour les données G-Stock Achat
    """

    def __init__(self):
        self.model = GStockAchat
        self.data = None
        self.df_raw = None
        self.df_clean = None

    def load_data(self):
        try:
            latest_achat = self.model.objects.order_by('-created_at').first()
            if not latest_achat:
                print("Aucune donnée G-Stock Achat trouvée")
                return False
            self.data = latest_achat.data
            print(f"Données G-Stock Achat chargées: {latest_achat.title}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def clean_data(self):
        print("Données G-Stock Achat prêtes pour traitement")
        return True

    def display_dataframes(self):
        print("ANALYSE DES DONNEES G-STOCK ACHAT")
        print("En attente de données")


class GProjetAnalytics:
    """
    Classe d'analyse pour les données G-Projet
    """

    def __init__(self):
        self.model = GProjet
        self.data = None
        self.df_raw = None
        self.df_clean = None

    def load_data(self):
        try:
            latest_projet = self.model.objects.order_by('-created_at').first()
            if not latest_projet:
                print("Aucune donnée G-Projet trouvée")
                return False
            self.data = latest_projet.data
            print(f"Données G-Projet chargées: {latest_projet.title}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def clean_data(self):
        print("Données G-Projet prêtes pour traitement")
        return True

    def display_dataframes(self):
        print("ANALYSE DES DONNEES G-PROJET")
        print("En attente de données")


class EcoleTalentsAnalytics:
    """
    Classe d'analyse pour les données École des Talents
    """

    def __init__(self):
        self.model = EcoleTalents
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_formations = None
        self.df_eligibilite = None

    def load_data(self):
        try:
            latest_ecole = self.model.objects.order_by('-created_at').first()
            if not latest_ecole:
                print("Aucune donnée École des Talents trouvée")
                return False
            self.data = latest_ecole.data
            print(f"Données École des Talents chargées: {latest_ecole.title}")
            if isinstance(self.data, list):
                self._parse_ecole_structure()
                return True
            return False
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_ecole_structure(self):
        try:
            all_inscriptions = []
            for inscription in self.data:
                inscription_data = {
                    'ID': inscription.get('id', 0),
                    'Nom': inscription.get('nom', ''),
                    'Prenom': inscription.get('prenom', ''),
                    'Email': inscription.get('email', ''),
                    'Telephone': inscription.get('telephone', ''),
                    'Formation_ID': inscription.get('formation_id', 0),
                    'Formation_Nom': inscription.get('formation', {}).get('nom', ''),
                    'Eligible': inscription.get('eligible', False),
                    'Date_Inscription': inscription.get('date_inscription', ''),
                    'Statut': inscription.get('statut', '')
                }
                all_inscriptions.append(inscription_data)
            self.df_raw = pd.DataFrame(all_inscriptions)
            print(f"Structure parsée: {len(all_inscriptions)} inscriptions extraites")
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        try:
            self.df_clean = self.df_raw.copy()
            numeric_columns = ['ID', 'Formation_ID']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)
            string_columns = ['Nom', 'Prenom', 'Formation_Nom', 'Statut']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            if 'Date_Inscription' in self.df_clean.columns:
                self.df_clean['Date_Inscription'] = pd.to_datetime(self.df_clean['Date_Inscription'], errors='coerce')
            self.df_clean = self.df_clean[
                (self.df_clean['Nom'] != '') &
                (self.df_clean['Nom'] != 'nan')
            ]
            self._create_specialized_dataframes()
            print(f"Données nettoyées: {len(self.df_clean)} inscriptions valides")
            return True
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        if not self.df_clean.empty:
            self.df_formations = self.df_clean.groupby(['Formation_ID', 'Formation_Nom']).agg({
                'ID': 'count',
                'Eligible': 'sum'
            }).reset_index()
            self.df_formations.columns = ['Formation_ID', 'Formation_Nom', 'Nombre_Inscriptions', 'Nombre_Eligibles']
            self.df_formations['Taux_Eligibilite'] = (self.df_formations['Nombre_Eligibles'] / self.df_formations['Nombre_Inscriptions'] * 100).round(2)

            self.df_eligibilite = self.df_clean.groupby('Eligible').agg({
                'ID': 'count'
            }).reset_index()
            self.df_eligibilite.columns = ['Eligible', 'Nombre']

    def display_dataframes(self):
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher.")
            return
        print("ANALYSE DES DONNEES ECOLE DES TALENTS")
        print("Nombre total d'inscriptions:", len(self.df_clean))
        print("Formations:", self.df_clean['Formation_Nom'].nunique())
        print("Eligibles:", self.df_clean['Eligible'].sum())
        print("Taux d'eligibilite:", (self.df_clean['Eligible'].sum() / len(self.df_clean) * 100), "%")

        print("\nINSCRIPTIONS")
        colonnes_affichage = ['Nom', 'Prenom', 'Formation_Nom', 'Eligible', 'Statut']
        print(self.df_clean[colonnes_affichage].head(10).to_string())

        if hasattr(self, 'df_formations') and not self.df_formations.empty:
            print("\nANALYSE PAR FORMATION")
            print(self.df_formations.to_string())


class GLocativeAnalytics:
    """
    Classe d'analyse pour les données G-Locative
    """

    def __init__(self):
        self.model = GLocative
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_biens = None
        self.df_types = None
        self.df_impayés = None

    def load_data(self):
        try:
            latest_locative = self.model.objects.order_by('-created_at').first()
            if not latest_locative:
                print("Aucune donnée G-Locative trouvée")
                return False
            self.data = latest_locative.data
            print(f"Données G-Locative chargées: {latest_locative.title}")
            if isinstance(self.data, list):
                self._parse_locative_structure()
                return True
            return False
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_locative_structure(self):
        try:
            all_biens = []
            for bien in self.data:
                bien_data = {
                    'ID': bien.get('id', 0),
                    'Libelle': bien.get('libelle', ''),
                    'Adresse': bien.get('adresse', ''),
                    'Type_Libelle': bien.get('type', {}).get('libelle', ''),
                    'Total_Loyer': bien.get('totaLoyer', 0),
                    'Total_Impaye': bien.get('totalImpayer', 0),
                    'Nombre_Locataires': len(bien.get('locataires', [])),
                    'Statut_Occupation': 'Occupé' if len(bien.get('locataires', [])) > 0 else 'Vacant'
                }
                bien_data['Taux_Impaye'] = (bien_data['Total_Impaye'] / max(bien_data['Total_Loyer'], 1)) * 100 if bien_data['Total_Loyer'] > 0 else 0
                all_biens.append(bien_data)
            self.df_raw = pd.DataFrame(all_biens)
            print(f"Structure parsée: {len(all_biens)} biens extraits")
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        try:
            self.df_clean = self.df_raw.copy()
            numeric_columns = ['ID', 'Total_Loyer', 'Total_Impaye', 'Nombre_Locataires', 'Taux_Impaye']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)
            string_columns = ['Libelle', 'Adresse', 'Type_Libelle', 'Statut_Occupation']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            self.df_clean = self.df_clean[
                (self.df_clean['Libelle'] != '') &
                (self.df_clean['Libelle'] != 'nan')
            ]
            self._create_specialized_dataframes()
            print(f"Données nettoyées: {len(self.df_clean)} biens valides")
            return True
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        self.df_biens = self.df_clean.copy()
        if not self.df_clean.empty:
            self.df_types = self.df_clean.groupby('Type_Libelle').agg({
                'Total_Loyer': ['sum', 'mean', 'count'],
                'Total_Impaye': 'sum',
                'Taux_Impaye': 'mean'
            }).round(2)
            self.df_types.columns = ['Loyer_Total', 'Loyer_Moyen', 'Nombre_Biens', 'Impaye_Total', 'Taux_Impaye_Moyen']
            self.df_types = self.df_types.reset_index()

            self.df_impayés = self.df_clean[self.df_clean['Total_Impaye'] > 0].copy()
            if not self.df_impayés.empty:
                self.df_impayés = self.df_impayés.sort_values('Total_Impaye', ascending=False)

    def display_dataframes(self):
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher.")
            return
        print("ANALYSE DES DONNEES G-LOCATIVE")
        loyer_total = self.df_clean['Total_Loyer'].sum()
        impaye_total = self.df_clean['Total_Impaye'].sum()
        print("Nombre de biens:", len(self.df_clean))
        print("Loyer total:", loyer_total, "FCFA")
        print("Impayés total:", impaye_total, "FCFA")
        print("Biens occupés:", len(self.df_clean[self.df_clean['Statut_Occupation'] == 'Occupé']))
        print("Biens vacants:", len(self.df_clean[self.df_clean['Statut_Occupation'] == 'Vacant']))

        print("\nBIENS IMMOBILIERS")
        colonnes_affichage = ['Libelle', 'Type_Libelle', 'Total_Loyer', 'Total_Impaye', 'Statut_Occupation']
        print(self.df_biens[colonnes_affichage].head(10).to_string())

        if hasattr(self, 'df_types') and not self.df_types.empty:
            print("\nANALYSE PAR TYPE DE BIEN")
            print(self.df_types.to_string())


class ESyndicAnalytics:
    """
    Classe d'analyse pour les données E-Syndic
    """

    def __init__(self):
        self.model = ESyndic
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_coproprietes = None
        self.df_villes = None

    def load_data(self):
        try:
            latest_esyndic = self.model.objects.order_by('-created_at').first()
            if not latest_esyndic:
                print("Aucune donnée E-Syndic trouvée")
                return False
            self.data = latest_esyndic.data
            print(f"Données E-Syndic chargées: {latest_esyndic.title}")
            if isinstance(self.data, dict) and 'data' in self.data:
                self._parse_esyndic_structure()
                return True
            return False
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_esyndic_structure(self):
        try:
            esyndic_data = self.data['data']
            all_coproprietes = []
            for copropriete in esyndic_data:
                charges_data = copropriete.get('charges', {})
                appel_fonds = charges_data.get('appel_fonds', {})
                copro_data = {
                    'ID': copropriete.get('id', 0),
                    'Libelle': copropriete.get('libelle', ''),
                    'Ville': copropriete.get('ville', ''),
                    'Total_Charge': appel_fonds.get('total_charge', 0),
                    'Total_Payer': appel_fonds.get('total_payer', 0),
                    'Total_Impayes': appel_fonds.get('total_impayes', 0),
                    'Taux_Recouvrement': 0
                }
                if copro_data['Total_Charge'] > 0:
                    copro_data['Taux_Recouvrement'] = (copro_data['Total_Payer'] / copro_data['Total_Charge'] * 100)
                all_coproprietes.append(copro_data)
            self.df_raw = pd.DataFrame(all_coproprietes)
            print(f"Structure parsée: {len(all_coproprietes)} copropriétés extraites")
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        try:
            self.df_clean = self.df_raw.copy()
            numeric_columns = ['ID', 'Total_Charge', 'Total_Payer', 'Total_Impayes', 'Taux_Recouvrement']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)
            string_columns = ['Libelle', 'Ville']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            self.df_clean = self.df_clean[
                (self.df_clean['Libelle'] != '') &
                (self.df_clean['Libelle'] != 'nan')
            ]
            self._create_specialized_dataframes()
            print(f"Données nettoyées: {len(self.df_clean)} copropriétés valides")
            return True
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        self.df_coproprietes = self.df_clean.copy()
        if not self.df_clean.empty:
            self.df_villes = self.df_clean.groupby('Ville').agg({
                'Total_Charge': ['sum', 'count'],
                'Total_Payer': 'sum',
                'Total_Impayes': 'sum',
                'Taux_Recouvrement': 'mean'
            }).round(2)
            self.df_villes.columns = ['Charges_Total', 'Nombre_Coproprietes', 'Paye_Total', 'Impaye_Total', 'Taux_Recouvrement_Moyen']
            self.df_villes = self.df_villes.reset_index()

    def display_dataframes(self):
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher.")
            return
        print("ANALYSE DES DONNEES E-SYNDIC")
        charges_total = self.df_clean['Total_Charge'].sum()
        paye_total = self.df_clean['Total_Payer'].sum()
        print("Nombre de copropriétés:", len(self.df_clean))
        print("Charges totales:", charges_total, "FCFA")
        print("Montant payé:", paye_total, "FCFA")
        print("Villes:", self.df_clean['Ville'].nunique())

        print("\nCOPROPRIETES")
        colonnes_affichage = ['Libelle', 'Ville', 'Total_Charge', 'Taux_Recouvrement']
        print(self.df_coproprietes[colonnes_affichage].to_string())

        if hasattr(self, 'df_villes') and not self.df_villes.empty:
            print("\nANALYSE PAR VILLE")
            print(self.df_villes.to_string())


class SentinelleAnalytics:
    """
    Classe d'analyse pour les données Sentinelle
    """

    def __init__(self):
        self.model = Sentinelle
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_programmes = None
        self.df_clients = None

    def load_data(self):
        try:
            latest_sentinelle = self.model.objects.first()
            if not latest_sentinelle:
                print("Aucune donnée Sentinelle trouvée")
                return False
            self.data = latest_sentinelle.data
            print(f"Données Sentinelle chargées: {latest_sentinelle.title}")
            if isinstance(self.data, dict):
                self._parse_sentinelle_structure()
                return True
            return False
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_sentinelle_structure(self):
        try:
            all_lots = []
            if 'taux_programme' in self.data:
                for programme in self.data['taux_programme']:
                    programme_info = {
                        'ID_Programme': programme.get('id_programme', ''),
                        'Libelle_Programme': programme.get('libelle_programme', ''),
                        'Avancement_Global': programme.get('avancement_global', 0)
                    }
                    for lot in programme.get('lots', []):
                        lot_data = {
                            **programme_info,
                            'Lot': lot.get('lot', ''),
                            'Client': lot.get('client', ''),
                            'Type_Actif': lot.get('type_actif', ''),
                            'Cout_Actif': lot.get('cout_actif', 0),
                            'Paiement_En_Date': lot.get('paiement_en_date', 0),
                            'Reste_A_Payer': lot.get('reste_a_payer', 0)
                        }
                        cout_actif = float(lot_data['Cout_Actif']) if lot_data['Cout_Actif'] else 0
                        paiement = float(lot_data['Paiement_En_Date']) if lot_data['Paiement_En_Date'] else 0
                        lot_data['Taux_Paiement'] = (paiement / cout_actif * 100) if cout_actif > 0 else 0
                        all_lots.append(lot_data)
            self.df_raw = pd.DataFrame(all_lots)
            print(f"Structure parsée: {len(all_lots)} lots extraits")
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        try:
            self.df_clean = self.df_raw.copy()
            numeric_columns = ['Avancement_Global', 'Cout_Actif', 'Paiement_En_Date', 'Reste_A_Payer', 'Taux_Paiement']
            for col in numeric_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = pd.to_numeric(self.df_clean[col], errors='coerce').fillna(0)
            string_columns = ['Libelle_Programme', 'Client', 'Type_Actif']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            self.df_clean = self.df_clean[
                (self.df_clean['Client'] != '') &
                (self.df_clean['Client'] != 'nan')
            ]
            self._create_specialized_dataframes()
            print(f"Données nettoyées: {len(self.df_clean)} lots valides")
            return True
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        if not self.df_clean.empty:
            self.df_programmes = self.df_clean.groupby(['ID_Programme', 'Libelle_Programme']).agg({
                'Cout_Actif': ['sum', 'count'],
                'Paiement_En_Date': 'sum',
                'Reste_A_Payer': 'sum'
            }).round(2)
            self.df_programmes.columns = ['Cout_Total', 'Nombre_Lots', 'Paiement_Total', 'Reste_Total']
            self.df_programmes = self.df_programmes.reset_index()

            self.df_clients = self.df_clean.groupby('Client').agg({
                'Cout_Actif': 'sum',
                'Paiement_En_Date': 'sum',
                'Taux_Paiement': 'mean'
            }).round(2)
            self.df_clients.columns = ['Cout_Total', 'Paiement_Total', 'Taux_Paiement_Moyen']
            self.df_clients = self.df_clients.reset_index()

    def display_dataframes(self):
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher.")
            return
        print("ANALYSE DES DONNEES SENTINELLE")
        cout_total = self.df_clean['Cout_Actif'].sum()
        paiement_total = self.df_clean['Paiement_En_Date'].sum()
        print("Nombre de lots:", len(self.df_clean))
        print("Coût total des actifs:", cout_total, "FCFA")
        print("Paiements reçus:", paiement_total, "FCFA")
        print("Programmes:", self.df_clean['Libelle_Programme'].nunique())
        print("Clients:", self.df_clean['Client'].nunique())

        print("\nLOTS")
        colonnes_affichage = ['Client', 'Libelle_Programme', 'Type_Actif', 'Cout_Actif', 'Taux_Paiement']
        print(self.df_clean[colonnes_affichage].head(10).to_string())

        if hasattr(self, 'df_programmes') and not self.df_programmes.empty:
            print("\nANALYSE PAR PROGRAMME")
            print(self.df_programmes.to_string())


class CRMAnalytics:
    """
    Classe d'analyse pour les données CRM
    """

    def __init__(self):
        self.model = CRM
        self.data = None
        self.df_raw = None
        self.df_clean = None

    def load_data(self):
        try:
            latest_crm = self.model.objects.first()
            if not latest_crm:
                print("Aucune donnée CRM trouvée")
                return False
            self.data = latest_crm.data
            print(f"Données CRM chargées: {latest_crm.title}")
            return True
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def clean_data(self):
        print("Données CRM prêtes pour traitement")
        return True

    def display_dataframes(self):
        print("ANALYSE DES DONNEES CRM")
        print("En attente de données")


class UserProfileAnalytics:
    """
    Classe d'analyse pour les données UserProfile
    """

    def __init__(self):
        self.model = UserProfile
        self.data = None
        self.df_raw = None
        self.df_clean = None
        self.df_pays = None

    def load_data(self):
        try:
            profiles = self.model.objects.all()
            if not profiles.exists():
                print("Aucune donnée UserProfile trouvée")
                return False
            print(f"Données UserProfile chargées: {profiles.count()} profils")
            self._parse_userprofile_structure(profiles)
            return True
        except Exception as e:
            print(f"Erreur lors du chargement: {str(e)}")
            return False

    def _parse_userprofile_structure(self, profiles):
        try:
            all_profiles = []
            for profile in profiles:
                profile_data = {
                    'User_ID': profile.user_id,
                    'User': str(profile.user) if profile.user else '',
                    'Address': profile.address or '',
                    'Country': profile.country or ''
                }
                all_profiles.append(profile_data)
            self.df_raw = pd.DataFrame(all_profiles)
            print(f"Structure parsée: {len(all_profiles)} profils extraits")
        except Exception as e:
            print(f"Erreur lors du parsing: {str(e)}")

    def clean_data(self):
        if self.df_raw is None or self.df_raw.empty:
            print("Aucune donnée brute à nettoyer")
            return False
        try:
            self.df_clean = self.df_raw.copy()
            string_columns = ['User', 'Address', 'Country']
            for col in string_columns:
                if col in self.df_clean.columns:
                    self.df_clean[col] = self.df_clean[col].astype(str).str.strip().str.title()
            self.df_clean = self.df_clean[
                (self.df_clean['User'] != '') &
                (self.df_clean['User'] != 'nan')
            ]
            self._create_specialized_dataframes()
            print(f"Données nettoyées: {len(self.df_clean)} profils valides")
            return True
        except Exception as e:
            print(f"Erreur lors du nettoyage: {str(e)}")
            return False

    def _create_specialized_dataframes(self):
        if not self.df_clean.empty:
            self.df_pays = self.df_clean.groupby('Country').agg({
                'User_ID': 'count'
            }).reset_index()
            self.df_pays.columns = ['Pays', 'Nombre_Utilisateurs']
            self.df_pays = self.df_pays.sort_values('Nombre_Utilisateurs', ascending=False)

    def display_dataframes(self):
        if self.df_clean is None or self.df_clean.empty:
            print("Aucune donnée à afficher.")
            return
        print("ANALYSE DES DONNEES USER PROFILE")
        print("Nombre de profils:", len(self.df_clean))
        print("Pays représentés:", self.df_clean['Country'].nunique())

        print("\nPROFILS UTILISATEURS")
        colonnes_affichage = ['User', 'Country', 'Address']
        print(self.df_clean[colonnes_affichage].to_string())

        if hasattr(self, 'df_pays') and not self.df_pays.empty:
            print("\nREPARTITION PAR PAYS")
            print(self.df_pays.to_string())


class AnalyticsEngine:
    """
    Classe principale d'orchestration pour toutes les analyses
    """

    def __init__(self):
        self.dqe_analytics = DQEAnalytics()
        self.gstock_appro_analytics = GStockApprovisionnementAnalytics()
        self.gstock_sortie_analytics = GStockSortieAnalytics()
        self.gstock_consommation_analytics = GStockConsommationAnalytics()
        self.gstock_achat_analytics = GStockAchatAnalytics()
        self.gprojet_analytics = GProjetAnalytics()
        self.ecole_talents_analytics = EcoleTalentsAnalytics()
        self.glocative_analytics = GLocativeAnalytics()
        self.esyndic_analytics = ESyndicAnalytics()
        self.sentinelle_analytics = SentinelleAnalytics()
        self.crm_analytics = CRMAnalytics()
        self.userprofile_analytics = UserProfileAnalytics()

    def run_complete_analysis(self):
        """
        Lance une analyse complète de toutes les données
        """
        results = {
            'status': 'success',
            'analyses': {},
            'errors': []
        }

        analytics_modules = [
            ('dqe', self.dqe_analytics),
            ('gstock_approvisionnement', self.gstock_appro_analytics),
            ('gstock_sortie', self.gstock_sortie_analytics),
            ('gstock_consommation', self.gstock_consommation_analytics),
            ('gstock_achat', self.gstock_achat_analytics),
            ('gprojet', self.gprojet_analytics),
            ('ecole_talents', self.ecole_talents_analytics),
            ('glocative', self.glocative_analytics),
            ('esyndic', self.esyndic_analytics),
            ('sentinelle', self.sentinelle_analytics),
            ('crm', self.crm_analytics),
            ('userprofile', self.userprofile_analytics),
        ]

        for module_name, analytics_instance in analytics_modules:
            try:
                print(f"Analyse en cours pour {module_name}...")
                if analytics_instance.load_data():
                    if hasattr(analytics_instance, 'clean_data'):
                        analytics_instance.clean_data()
                    if hasattr(analytics_instance, 'analyze_data'):
                        analytics_instance.analyze_data()
                    results['analyses'][module_name] = 'completed'
                else:
                    results['analyses'][module_name] = 'no_data'
            except Exception as e:
                error_msg = f"Erreur lors de l'analyse {module_name}: {str(e)}"
                print(error_msg)
                results['errors'].append(error_msg)
                results['analyses'][module_name] = 'error'

        return results

    def get_analytics_summary(self):
        """
        Retourne un résumé des analyses disponibles
        """
        return {
            'available_modules': [
                'dqe', 'gstock_approvisionnement', 'gstock_sortie',
                'gstock_consommation', 'gstock_achat', 'gprojet',
                'ecole_talents', 'glocative', 'esyndic', 'sentinelle',
                'crm', 'userprofile'
            ],
            'description': 'Moteur d\'analyse pour toutes les données KAH'
        }
