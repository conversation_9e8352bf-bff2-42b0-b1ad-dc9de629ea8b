# kay_analytics/celery.py
import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kay_analytics.settings')

app = Celery('kay_analytics')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Configuration pour les tâches périodiques
app.conf.beat_schedule = {
    'collect-data-every-3-days': {
        'task': 'analytics_engine.views.scheduled_data_collection',
        'schedule': 60.0 * 60.0 * 24.0 * 3,  # Toutes les 3 jours (en secondes)
        'options': {'queue': 'analytics'}
    },
}

app.conf.timezone = 'UTC'

# Configuration des queues
app.conf.task_routes = {
    'analytics_engine.views.*': {'queue': 'analytics'},
}

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
